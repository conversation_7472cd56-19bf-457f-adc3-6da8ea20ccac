# 方案1：混合模式配置文件（含自动Token管理）
# 复制此文件为 .env 并填入实际值

# 基础配置
PORT=80
LOCAL_SERVER_URL=http://219.138.230.35:30002
WECHAT_TOKEN=wuduqiaojie2025token

# 方案1核心配置
# 设置为 true 启用异步模式（推荐）
# 设置为 false 保持同步模式（兼容模式）
WECHAT_ASYNC_MODE=false

# 🚀 自动Token管理配置（推荐）
# 配置后无需手动更新Access Token，系统每90分钟自动刷新
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret

# 手动Token配置（备用方案）
# 如果不配置APPID/SECRET，可以手动设置Access Token
WECHAT_ACCESS_TOKEN=

# 配置说明：
# 1. 同步模式（WECHAT_ASYNC_MODE=false）：
#    - 保持原有逻辑，4.5秒超时
#    - 不需要 Access Token
#    - 向后兼容，零风险
#
# 2. 异步模式（WECHAT_ASYNC_MODE=true）：
#    - 立即回复"正在查询..."
#    - 后台AI处理，无时间限制
#    - 通过客服API发送最终回复
#    - 需要配置 WECHAT_ACCESS_TOKEN

# 获取 Access Token 的方法：
# 1. 访问：https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=YOUR_APPID&secret=YOUR_SECRET
# 2. 或在微信公众号后台的开发者工具中获取
# 3. 注意：Access Token 有效期为2小时，需要定期更新

# 部署步骤：
# 1. 先部署代码（默认同步模式）
# 2. 测试确认功能正常
# 3. 获取并配置 WECHAT_ACCESS_TOKEN
# 4. 设置 WECHAT_ASYNC_MODE=true
# 5. 重新部署，享受方案1的优秀体验！
