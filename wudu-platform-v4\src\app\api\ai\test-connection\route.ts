import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 加密密钥
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';

// 解密函数
function decrypt(encryptedText: string): string {
  if (!encryptedText) return '';

  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);

    const textParts = encryptedText.split(':');
    if (textParts.length !== 2) {
      return encryptedText;
    }

    const iv = Buffer.from(textParts[0], 'hex');
    const encrypted = textParts[1];

    const decipher = crypto.createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return '';
  }
}

// 测试DeepSeek连接
async function testDeepSeekConnection(apiKey: string): Promise<{ success: boolean; message: string }> {
  try {
    const response = await fetch('https://api.deepseek.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `DeepSeek连接成功！可用模型: ${data.data?.length || 0} 个`
      };
    } else {
      return {
        success: false,
        message: `DeepSeek连接失败: ${response.status} ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `DeepSeek连接失败: ${error instanceof Error ? error.message : '网络错误'}`
    };
  }
}

// 测试OpenAI连接
async function testOpenAIConnection(apiKey: string): Promise<{ success: boolean; message: string }> {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `OpenAI连接成功！可用模型: ${data.data?.length || 0} 个`
      };
    } else {
      return {
        success: false,
        message: `OpenAI连接失败: ${response.status} ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `OpenAI连接失败: ${error instanceof Error ? error.message : '网络错误'}`
    };
  }
}

// POST /api/ai/test-connection - 测试AI服务连接
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, apiKey: providedApiKey } = body;

    if (!provider) {
      return NextResponse.json({
        success: false,
        message: '缺少服务提供商参数'
      }, { status: 400 });
    }

    console.log('🔍 测试AI服务连接:', provider);

    let apiKey = '';

    // 如果直接提供了API密钥，使用提供的密钥
    if (providedApiKey) {
      apiKey = providedApiKey;
      console.log('📝 使用前端提供的API密钥进行测试');
    } else {
      // 否则从数据库获取API密钥
      let configKey: string;
      switch (provider.toLowerCase()) {
        case 'deepseek':
          configKey = 'deepseek_api_key';
          break;
        case 'openai':
          configKey = 'openai_api_key';
          break;
        default:
          return NextResponse.json({
            success: false,
            message: '不支持的服务提供商'
          }, { status: 400 });
      }

      // 从数据库获取API密钥
      const config = await prisma.aiConfig.findUnique({
        where: { configKey }
      });

      if (!config || !config.configValue) {
        return NextResponse.json({
          success: false,
          message: `未找到 ${provider} 的API密钥配置`
        }, { status: 404 });
      }

      // 解密API密钥
      apiKey = config.isEncrypted ? decrypt(config.configValue) : config.configValue;
      console.log('💾 使用数据库中的API密钥进行测试');
    }

    if (!apiKey) {
      return NextResponse.json({
        success: false,
        message: 'API密钥为空或解密失败'
      }, { status: 400 });
    }

    // 测试连接
    let result: { success: boolean; message: string };
    
    switch (provider.toLowerCase()) {
      case 'deepseek':
        result = await testDeepSeekConnection(apiKey);
        break;
      case 'openai':
        result = await testOpenAIConnection(apiKey);
        break;
      default:
        result = { success: false, message: '不支持的服务提供商' };
    }

    console.log(`${result.success ? '✅' : '❌'} ${provider} 连接测试:`, result.message);

    return NextResponse.json({
      success: result.success,
      message: result.message,
      provider,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 测试AI连接失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '测试连接失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
