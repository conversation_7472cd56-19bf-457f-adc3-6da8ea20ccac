'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// 钉钉SDK声明
declare global {
  interface Window {
    DTFrameLogin: any;
  }
}

export function DingtalkLogin() {
  const [dingtalkReady, setDingtalkReady] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const { login } = useAuth();
  const router = useRouter();

  // 加载钉钉SDK
  useEffect(() => {
    const loadDingtalkSDK = () => {
      if (window.DTFrameLogin) {
        setDingtalkReady(true);
        setTimeout(() => initDingtalk(), 100);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js';
      script.onload = () => {
        console.log('✅ 钉钉SDK加载成功');
        setDingtalkReady(true);
        setTimeout(() => initDingtalk(), 100);
      };
      script.onerror = () => {
        console.error('❌ 钉钉SDK加载失败');
      };

      document.head.appendChild(script);
    };

    loadDingtalkSDK();
  }, []);

  // 初始化钉钉登录
  const initDingtalk = () => {
    if (!window.DTFrameLogin) return;

    try {
      window.DTFrameLogin(
        {
          id: 'dingtalk_container',
          width: 300,
          height: 300,
        },
        {
          redirect_uri: 'http://**************:30002/dingtalk-callback',
          client_id: 'dinggai5cng27n76jvbq',
          scope: 'openid',
          response_type: 'code',
          state: 'dingtalk_login_' + Date.now(),
          prompt: 'consent',
        },
        async (loginResult: any) => {
          console.log('✅ 钉钉登录成功:', loginResult);
          
          if (loginResult?.authCode) {
            const success = await login(loginResult.authCode, loginResult.state);
            if (success) {
              setShowAlert(true);
              setTimeout(() => window.location.href = 'http://**************:30002/dashboard', 1500);
            }
          }
        },
        (error: string) => {
          console.error('❌ 钉钉登录失败:', error);
        }
      );
    } catch (error) {
      console.error('❌ 钉钉初始化失败:', error);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-center">钉钉扫码登录</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-center mb-4">
          {dingtalkReady ? (
            <div 
              id="dingtalk_container"
              className="w-[300px] h-[300px] border-2 border-dashed border-gray-200 rounded-xl bg-gray-50 flex items-center justify-center"
            />
          ) : (
            <div className="w-[300px] h-[300px] border-2 border-dashed border-gray-200 rounded-xl bg-gray-50 flex flex-col items-center justify-center text-center p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              </div>
              <h4 className="text-lg font-semibold text-gray-800 mb-2">正在加载钉钉登录</h4>
              <p className="text-sm text-gray-600">请稍候...</p>
            </div>
          )}
        </div>

        <div className="text-center">
          {dingtalkReady ? (
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
              <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
              请使用钉钉APP扫描二维码
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
              <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
              正在加载钉钉登录组件...
            </div>
          )}
        </div>

        {showAlert && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <span className="text-green-800">登录成功！正在跳转...</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
