import { NextRequest, NextResponse } from 'next/server';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';
import { conversationManager } from '@/lib/ai/conversation-manager';

// POST /api/ai/status/test - 测试AI服务
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testMessage = '你好，请介绍一下景区' } = body;

    console.log('🧪 开始AI服务测试...');

    // 创建测试对话
    const testSessionId = `test_${Date.now()}`;
    const conversation = await conversationManager.getOrCreateConversationBySession(testSessionId);

    // 发送测试消息
    const startTime = Date.now();
    const result = await conversationManager.sendMessage({
      conversationId: conversation.id,
      content: testMessage,
      role: 'USER' as any,
      metadata: { test: true }
    });

    const testDuration = Date.now() - startTime;

    if (result.error) {
      return NextResponse.json({
        success: false,
        error: result.error,
        testDuration,
        testMessage
      });
    }

    // 清理测试对话
    await conversationManager.archiveConversation(conversation.id);

    console.log(`✅ AI服务测试完成，耗时: ${testDuration}ms`);

    return NextResponse.json({
      success: true,
      testResult: {
        testMessage,
        response: result.assistantMessage?.content || '无回复',
        responseTime: result.assistantMessage?.responseTimeMs || 0,
        testDuration,
        serviceUsed: result.assistantMessage?.metadata?.serviceUsed || 'unknown'
      }
    });

  } catch (error: any) {
    console.error('❌ AI服务测试失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || 'AI服务测试失败',
      testDuration: 0
    }, { status: 500 });
  }
}
