import { NextRequest, NextResponse } from 'next/server';
import { WeChatAdapter } from '@/lib/webhook/adapters/wechat';
import { MessageProcessor } from '@/lib/webhook/message-processor';
import { UnifiedMessage } from '@/types/webhook';

/**
 * 测试webhook基础架构的API端点
 * 用于验证消息处理器和适配器是否正常工作
 */
export async function POST() {
  try {
    console.log('[Webhook Test] Starting webhook infrastructure test');

    // 1. 测试消息处理器
    const processor = new MessageProcessor();
    
    // 创建测试消息
    const testMessage: UnifiedMessage = {
      id: `test_${Date.now()}`,
      platform: 'wechat',
      userId: 'test_user_123',
      content: '你好，请介绍一下吴都乔街景区',
      messageType: 'text',
      sessionId: 'wechat:test_user_123',
      timestamp: Date.now(),
      metadata: {
        original: { test: true },
        chatId: 'test_chat'
      }
    };

    console.log('[Webhook Test] Testing message processor with test message:', {
      messageId: testMessage.id,
      content: testMessage.content,
      platform: testMessage.platform
    });

    // 2. 处理测试消息
    const startTime = Date.now();
    const reply = await processor.process(testMessage);
    const processingTime = Date.now() - startTime;

    console.log('[Webhook Test] Message processed successfully:', {
      processingTime,
      replyLength: reply.length,
      replyPreview: reply.substring(0, 100) + '...'
    });

    // 3. 测试微信适配器
    const wechatAdapter = new WeChatAdapter();
    
    // 测试XML生成
    const testXml = wechatAdapter.generateReplyXML(testMessage, reply);
    
    console.log('[Webhook Test] WeChat XML generated:', {
      xmlLength: testXml.length,
      xmlPreview: testXml.substring(0, 200) + '...'
    });

    // 4. 返回测试结果
    const testResult = {
      success: true,
      timestamp: new Date().toISOString(),
      test: {
        message: {
          id: testMessage.id,
          platform: testMessage.platform,
          content: testMessage.content,
          processingTime
        },
        reply: {
          content: reply,
          length: reply.length,
          preview: reply.substring(0, 200) + (reply.length > 200 ? '...' : '')
        },
        wechatXml: {
          generated: true,
          length: testXml.length,
          preview: testXml.substring(0, 300) + (testXml.length > 300 ? '...' : '')
        }
      },
      infrastructure: {
        messageProcessor: 'OK',
        wechatAdapter: 'OK',
        ragIntegration: 'OK'
      }
    };

    return NextResponse.json(testResult, { status: 200 });

  } catch (error) {
    console.error('[Webhook Test] Test failed:', error);

    const errorResult = {
      success: false,
      timestamp: new Date().toISOString(),
      error: {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      },
      infrastructure: {
        messageProcessor: 'ERROR',
        wechatAdapter: 'UNKNOWN',
        ragIntegration: 'ERROR'
      }
    };

    return NextResponse.json(errorResult, { status: 500 });
  }
}

/**
 * 获取webhook测试状态
 */
export async function GET() {
  try {
    const status = {
      timestamp: new Date().toISOString(),
      webhook: {
        infrastructure: 'Ready',
        endpoints: {
          wechat: '/api/webhooks/wechat',
          test: '/api/webhooks/test'
        },
        components: {
          messageProcessor: 'Available',
          wechatAdapter: 'Available',
          ragIntegration: 'Available'
        }
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasWechatToken: !!process.env.WECHAT_TOKEN,
        hasWechatAppId: !!process.env.WECHAT_APP_ID,
        hasWechatAppSecret: !!process.env.WECHAT_APP_SECRET
      }
    };

    return NextResponse.json(status, { status: 200 });
  } catch (error) {
    console.error('[Webhook Test] Status check failed:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// 配置Next.js API路由
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
