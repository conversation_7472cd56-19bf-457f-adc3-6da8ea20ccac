/**
 * 增强搜索结果组件
 * 显示搜索结果，包含相似度评分和搜索类型标识
 */

'use client';

import React from 'react';
import { Eye, Edit, Trash2, Brain, Search, Sparkles, Target } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

// 搜索结果接口
interface SearchResult {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  category?: string;
  tags: string[];
  similarity?: number;
  relevanceScore?: number;
  priority: number;
  useCount: number;
  createdAt: string;
  updatedAt: string;
}

type SearchType = 'keyword' | 'vector' | 'hybrid';

interface EnhancedSearchResultsProps {
  results: SearchResult[];
  searchInfo: {
    query: string;
    searchType: SearchType;
    searchTime: number;
    totalCount: number;
  };
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onView?: (id: string) => void;
}

export function EnhancedSearchResults({
  results,
  searchInfo,
  onEdit,
  onDelete,
  onView
}: EnhancedSearchResultsProps) {
  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800';
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800';
      case 'ARCHIVED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  // 获取搜索类型图标
  const getSearchTypeIcon = (type: SearchType) => {
    switch (type) {
      case 'keyword':
        return <Search className="h-4 w-4" />;
      case 'vector':
        return <Brain className="h-4 w-4" />;
      case 'hybrid':
        return <Sparkles className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  // 获取搜索类型标签
  const getSearchTypeLabel = (type: SearchType) => {
    switch (type) {
      case 'keyword':
        return '关键词搜索';
      case 'vector':
        return '语义搜索';
      case 'hybrid':
        return '智能搜索';
      default:
        return '搜索';
    }
  };

  // 格式化相似度分数
  const formatSimilarity = (similarity?: number) => {
    if (similarity === undefined) return null;
    return Math.round(similarity * 100);
  };

  // 获取相似度颜色
  const getSimilarityColor = (similarity?: number) => {
    if (!similarity) return 'bg-gray-200';
    if (similarity >= 0.7) return 'bg-green-500';
    if (similarity >= 0.5) return 'bg-yellow-500';
    if (similarity >= 0.3) return 'bg-orange-500';
    return 'bg-red-500';
  };

  if (results.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-gray-100 rounded-full">
              {getSearchTypeIcon(searchInfo.searchType)}
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                没有找到匹配的结果
              </h3>
              <p className="text-gray-500 mt-1">
                {searchInfo.query ? `"${searchInfo.query}" 没有匹配的知识条目` : '没有符合条件的知识条目'}
              </p>
              {searchInfo.searchType === 'vector' && (
                <p className="text-sm text-gray-400 mt-2">
                  提示：尝试降低相似度阈值或使用不同的关键词
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索信息头部 */}
      {searchInfo.query && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {getSearchTypeIcon(searchInfo.searchType)}
                  <span className="font-medium">{getSearchTypeLabel(searchInfo.searchType)}</span>
                </div>
                <Badge variant="outline">
                  "{searchInfo.query}"
                </Badge>
                <Badge variant="secondary">
                  {searchInfo.totalCount} 个结果
                </Badge>
                {searchInfo.searchTime > 0 && (
                  <Badge variant="outline">
                    {searchInfo.searchTime}ms
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索结果列表 */}
      <div className="grid gap-4">
        {results.map((item, index) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  {/* 标题和相似度 */}
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {item.title}
                    </h3>
                    
                    {/* 相似度评分 */}
                    {item.similarity !== undefined && (
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-gray-500" />
                        <div className="flex items-center space-x-1">
                          <Progress 
                            value={formatSimilarity(item.similarity) || 0} 
                            className="w-16 h-2"
                          />
                          <span className="text-sm font-medium text-gray-600">
                            {formatSimilarity(item.similarity)}%
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {/* 搜索排名 */}
                    {searchInfo.query && (
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                    )}
                  </div>

                  {/* 状态和优先级 */}
                  <div className="flex items-center space-x-2 mb-3">
                    <Badge className="bg-blue-100 text-blue-800">
                      ACTIVE
                    </Badge>
                    <Badge variant="outline">
                      优先级 {item.priority}
                    </Badge>
                    {item.category && (
                      <Badge variant="secondary">
                        {item.category}
                      </Badge>
                    )}
                  </div>

                  {/* 摘要 */}
                  {item.summary && (
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {item.summary}
                    </p>
                  )}

                  {/* 统计信息 */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4" />
                      <span>{item.useCount} 次查看</span>
                    </div>
                    <span>使用 {item.useCount} 次</span>
                    <span>版本 1</span>
                    <span>创建者: 系统管理员</span>
                  </div>

                  {/* 标签 */}
                  {item.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {item.tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex space-x-2 ml-4">
                  {onView && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onView(item.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(item.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
