const express = require('express');
const axios = require('axios');
const crypto = require('crypto');

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// 配置
const LOCAL_SERVER_URL = process.env.LOCAL_SERVER_URL || 'http://219.138.230.35:30002';
const LOCAL_API_PATH = '/api/wechat-message';
const WECHAT_TOKEN = process.env.WECHAT_TOKEN || 'wuduqiaojie2025token';

// 微信API配置 - 需要在环境变量中配置
const WECHAT_ACCESS_TOKEN = process.env.WECHAT_ACCESS_TOKEN || '';
const WECHAT_CUSTOMER_SERVICE_URL = 'https://api.weixin.qq.com/cgi-bin/message/custom/send';

const PORT = process.env.PORT || 8080;

// 验证微信服务器
function verifyWechatSignature(signature, timestamp, nonce) {
  const token = WECHAT_TOKEN;
  const tmpArr = [token, timestamp, nonce].sort();
  const tmpStr = tmpArr.join('');
  const hash = crypto.createHash('sha1').update(tmpStr).digest('hex');
  return hash === signature;
}

// GET请求用于微信服务器验证
app.get('/wechat-message', (req, res) => {
  const { signature, timestamp, nonce, echostr } = req.query;
  
  console.log('[云托管中转] 微信验证请求:', { signature, timestamp, nonce, echostr });
  
  if (verifyWechatSignature(signature, timestamp, nonce)) {
    console.log('[云托管中转] 微信验证成功');
    res.send(echostr);
  } else {
    console.log('[云托管中转] 微信验证失败');
    res.status(403).send('Forbidden');
  }
});

// 发送客服消息
async function sendCustomerServiceMessage(toUser, content) {
  if (!WECHAT_ACCESS_TOKEN) {
    console.error('[云托管中转] 缺少微信Access Token，无法发送客服消息');
    return false;
  }

  try {
    const messageData = {
      touser: toUser,
      msgtype: 'text',
      text: {
        content: content
      }
    };

    const response = await axios.post(
      `${WECHAT_CUSTOMER_SERVICE_URL}?access_token=${WECHAT_ACCESS_TOKEN}`,
      messageData,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      }
    );

    console.log('[云托管中转] 客服消息发送结果:', response.data);
    return response.data.errcode === 0;
  } catch (error) {
    console.error('[云托管中转] 发送客服消息失败:', error.message);
    return false;
  }
}

// 异步处理消息函数
async function processMessageAsync(messageBody) {
  try {
    console.log('[云托管中转] 开始异步处理消息');
    
    // 1. 立即发送"正在思考中..."的临时回复
    const tempMessage = "🤔 正在为您查询，请稍候...";
    await sendCustomerServiceMessage(messageBody.FromUserName, tempMessage);
    console.log('[云托管中转] 已发送临时回复');

    // 2. 构建转发数据
    const messageData = {
      toUserName: messageBody.ToUserName,
      fromUserName: messageBody.FromUserName,
      createTime: messageBody.CreateTime,
      msgType: messageBody.MsgType,
      content: messageBody.Content || messageBody.Text || '',
      msgId: messageBody.MsgId,
      originalData: messageBody
    };
    
    console.log('[云托管中转] 转发消息到本地AI服务:', LOCAL_SERVER_URL + LOCAL_API_PATH);
    
    // 3. 调用本地AI服务
    const response = await axios.post(LOCAL_SERVER_URL + LOCAL_API_PATH, messageData, {
      timeout: 30000, // 30秒超时，因为是异步处理
      headers: {
        'Content-Type': 'application/json',
        'X-Wechat-Relay': 'true',
        'X-Async-Mode': 'true' // 标识异步模式
      }
    });
    
    console.log('[云托管中转] 本地AI服务响应:', response.status);
    
    // 4. 发送AI回复
    if (response.data && response.data.reply) {
      const aiReply = response.data.reply;
      const success = await sendCustomerServiceMessage(messageBody.FromUserName, aiReply);
      
      if (success) {
        console.log('[云托管中转] AI回复发送成功:', aiReply.substring(0, 50) + '...');
      } else {
        console.error('[云托管中转] AI回复发送失败');
        // 发送错误提示
        await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，系统暂时繁忙，请稍后再试。');
      }
    } else {
      console.log('[云托管中转] 本地服务无回复内容');
      await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，我暂时无法理解您的问题，请联系人工客服。');
    }
    
  } catch (error) {
    console.error('[云托管中转] 异步处理失败:', error.message);
    
    // 发送错误回复
    try {
      await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，系统出现问题，请稍后再试或联系人工客服。');
    } catch (sendError) {
      console.error('[云托管中转] 发送错误回复也失败了:', sendError.message);
    }
  }
}

// POST请求处理微信消息 - 异步模式
app.post('/wechat-message', async (req, res) => {
  console.log('[云托管中转] 收到微信消息，启动异步处理模式');
  
  // 立即返回success，避免微信5秒超时
  res.send('success');
  
  // 检查是否是配置测试请求
  if (req.body.action === 'CheckContainerPath') {
    console.log('[云托管中转] 配置测试请求，无需处理');
    return;
  }

  // 异步处理消息，不阻塞响应
  processMessageAsync(req.body).catch(error => {
    console.error('[云托管中转] 异步处理出现未捕获错误:', error);
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    mode: 'async',
    hasAccessToken: !!WECHAT_ACCESS_TOKEN
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`[云托管中转] 异步模式服务器启动在端口 ${PORT}`);
  console.log(`[云托管中转] 本地服务器: ${LOCAL_SERVER_URL}${LOCAL_API_PATH}`);
  console.log(`[云托管中转] 微信Token配置: ${WECHAT_TOKEN ? '已配置' : '未配置'}`);
  console.log(`[云托管中转] 微信Access Token: ${WECHAT_ACCESS_TOKEN ? '已配置' : '未配置'}`);
  
  if (!WECHAT_ACCESS_TOKEN) {
    console.warn('[云托管中转] ⚠️  警告：未配置WECHAT_ACCESS_TOKEN，无法发送客服消息');
  }
});
