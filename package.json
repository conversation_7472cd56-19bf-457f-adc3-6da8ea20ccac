{"name": "dingtalk-no-proxy-test", "version": "1.0.0", "description": "钉钉无代理登录方案测试环境", "main": "test-no-proxy-backend.js", "scripts": {"start": "node test-no-proxy-backend.js", "dev": "node test-no-proxy-backend.js", "test": "echo \"测试环境启动中...\" && npm start"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "login", "no-proxy", "test"], "author": "Augment Agent", "license": "MIT"}