{"name": "wudu-platform-v4", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -H 0.0.0.0 -p 30002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@types/xml2js": "^0.4.14", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "15.4.4", "prisma": "^6.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "xml2js": "^0.6.2", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}