/**
 * 向量搜索系统测试脚本
 * 测试embedding生成和向量搜索功能
 */

import { PrismaClient } from '@prisma/client';
import { embeddingService } from '../src/lib/ai/embedding-service';
import { vectorSearchService } from '../src/lib/ai/vector-search';

const prisma = new PrismaClient();

async function testVectorSearchSystem() {
  console.log('🧪 开始测试向量搜索系统...\n');

  try {
    // 1. 测试embedding服务初始化
    console.log('📋 步骤1: 初始化embedding服务');
    await embeddingService.initialize();
    const embeddingStatus = embeddingService.getStatus();
    console.log('✅ Embedding服务状态:', embeddingStatus);
    
    if (!embeddingStatus.isConfigured) {
      console.log('⚠️ 警告: Embedding服务未配置API密钥，将跳过向量生成测试');
    }
    console.log('');

    // 2. 检查现有知识库数据
    console.log('📋 步骤2: 检查知识库数据');
    const totalItems = await prisma.knowledgeBase.count({
      where: { status: 'ACTIVE' }
    });
    
    const itemsWithEmbedding = await prisma.knowledgeBase.count({
      where: { 
        status: 'ACTIVE',
        embedding: { not: null }
      }
    });

    console.log(`📊 知识库统计:`);
    console.log(`   - 总条目数: ${totalItems}`);
    console.log(`   - 有向量的条目: ${itemsWithEmbedding}`);
    console.log(`   - 向量覆盖率: ${totalItems > 0 ? Math.round((itemsWithEmbedding / totalItems) * 100) : 0}%`);
    console.log('');

    // 3. 测试单个向量生成（如果配置了API）
    if (embeddingStatus.isConfigured && totalItems > 0) {
      console.log('📋 步骤3: 测试单个向量生成');
      
      // 获取一个没有向量的条目
      const testItem = await prisma.knowledgeBase.findFirst({
        where: {
          status: 'ACTIVE',
          embedding: null
        }
      });

      if (testItem) {
        console.log(`🎯 测试条目: "${testItem.title}"`);
        
        try {
          const textToEmbed = [
            testItem.title,
            testItem.summary || '',
            testItem.content.substring(0, 500)
          ].filter(Boolean).join(' ');

          const embeddingResult = await embeddingService.generateEmbedding(textToEmbed);
          
          // 更新数据库
          await prisma.knowledgeBase.update({
            where: { id: testItem.id },
            data: {
              embedding: embeddingResult.vector,
              embeddingModel: embeddingResult.model,
              embeddingVersion: '1.0',
              embeddingUpdatedAt: new Date()
            }
          });

          console.log(`✅ 向量生成成功:`);
          console.log(`   - 维度: ${embeddingResult.dimensions}`);
          console.log(`   - 模型: ${embeddingResult.model}`);
          console.log(`   - 耗时: ${embeddingResult.processingTime}ms`);
        } catch (error: any) {
          console.log(`❌ 向量生成失败: ${error.message}`);
        }
      } else {
        console.log('ℹ️ 所有条目都已有向量，跳过生成测试');
      }
      console.log('');
    }

    // 4. 测试向量搜索
    console.log('📋 步骤4: 测试向量搜索');
    
    const updatedItemsWithEmbedding = await prisma.knowledgeBase.count({
      where: { 
        status: 'ACTIVE',
        embedding: { not: null }
      }
    });

    if (updatedItemsWithEmbedding > 0) {
      const testQueries = [
        '门票价格',
        '开放时间',
        '交通指南',
        '游览路线'
      ];

      for (const query of testQueries) {
        try {
          console.log(`🔍 搜索查询: "${query}"`);
          
          const searchResult = await vectorSearchService.vectorSearch(query, {
            similarityThreshold: 0.5,
            maxResults: 3,
            includeContent: false
          });

          console.log(`   结果数量: ${searchResult.results.length}`);
          console.log(`   搜索耗时: ${searchResult.searchTime}ms`);
          
          if (searchResult.results.length > 0) {
            console.log(`   最佳匹配: "${searchResult.results[0].title}" (相似度: ${searchResult.results[0].similarity})`);
          }
          
        } catch (error: any) {
          console.log(`   ❌ 搜索失败: ${error.message}`);
        }
        console.log('');
      }
    } else {
      console.log('⚠️ 没有向量数据，跳过搜索测试');
      console.log('');
    }

    // 5. 测试相似条目搜索
    console.log('📋 步骤5: 测试相似条目搜索');
    
    if (updatedItemsWithEmbedding > 1) {
      const sampleItem = await prisma.knowledgeBase.findFirst({
        where: {
          status: 'ACTIVE',
          embedding: { not: null }
        }
      });

      if (sampleItem) {
        try {
          console.log(`🎯 基准条目: "${sampleItem.title}"`);
          
          const similarResult = await vectorSearchService.findSimilarItems(sampleItem.id, {
            similarityThreshold: 0.3,
            maxResults: 3,
            includeContent: false
          });

          console.log(`   相似条目数量: ${similarResult.results.length}`);
          console.log(`   搜索耗时: ${similarResult.searchTime}ms`);
          
          similarResult.results.forEach((item, index) => {
            console.log(`   ${index + 1}. "${item.title}" (相似度: ${item.similarity})`);
          });
          
        } catch (error: any) {
          console.log(`   ❌ 相似搜索失败: ${error.message}`);
        }
      }
      console.log('');
    }

    // 6. 性能测试
    console.log('📋 步骤6: 性能测试');
    
    if (updatedItemsWithEmbedding > 0) {
      const performanceQueries = ['门票', '时间', '路线'];
      const startTime = Date.now();
      
      for (const query of performanceQueries) {
        await vectorSearchService.vectorSearch(query, {
          similarityThreshold: 0.5,
          maxResults: 5
        });
      }
      
      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / performanceQueries.length;
      
      console.log(`⚡ 性能测试结果:`);
      console.log(`   - 总查询数: ${performanceQueries.length}`);
      console.log(`   - 总耗时: ${totalTime}ms`);
      console.log(`   - 平均耗时: ${Math.round(avgTime)}ms/查询`);
      console.log('');
    }

    // 7. 系统状态总结
    console.log('📋 步骤7: 系统状态总结');
    
    const finalStats = await prisma.knowledgeBase.groupBy({
      by: ['embeddingModel'],
      where: {
        status: 'ACTIVE',
        embedding: { not: null }
      },
      _count: {
        id: true
      }
    });

    console.log('📊 最终统计:');
    console.log(`   - 总知识条目: ${totalItems}`);
    console.log(`   - 已向量化条目: ${updatedItemsWithEmbedding}`);
    console.log(`   - 向量覆盖率: ${totalItems > 0 ? Math.round((updatedItemsWithEmbedding / totalItems) * 100) : 0}%`);
    
    if (finalStats.length > 0) {
      console.log('   - 模型分布:');
      finalStats.forEach(stat => {
        console.log(`     * ${stat.embeddingModel || 'unknown'}: ${stat._count.id} 条`);
      });
    }

    console.log('\n🎉 向量搜索系统测试完成！');

  } catch (error: any) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  testVectorSearchSystem().catch(console.error);
}

export { testVectorSearchSystem };
