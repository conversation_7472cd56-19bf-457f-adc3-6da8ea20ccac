/**
 * RAG (检索增强生成) 服务
 * 集成向量搜索和AI生成，提供智能知识库问答功能
 */

import { vectorSearchService, type SearchResult } from './vector-search';
import { aiServiceManager, type ChatMessage, type ConversationContext } from './ai-service-manager';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// RAG配置
interface RAGConfig {
  maxKnowledgeItems: number;        // 最大检索知识条目数
  similarityThreshold: number;      // 相似度阈值
  knowledgeWeightFactor: number;    // 知识库权重因子
  enableHybridSearch: boolean;      // 是否启用混合搜索
  includeSourceReferences: boolean; // 是否包含来源引用
  contextWindowSize: number;        // 上下文窗口大小
}

// RAG响应结果
interface RAGResponse {
  answer: string;                   // AI生成的回答
  knowledgeUsed: KnowledgeReference[]; // 使用的知识库条目
  confidence: number;               // 回答置信度
  searchTime: number;               // 检索耗时
  generateTime: number;             // 生成耗时
  totalTime: number;                // 总耗时
  searchType: 'vector' | 'hybrid'; // 搜索类型
  metadata: {
    queryProcessed: string;         // 处理后的查询
    knowledgeCount: number;         // 检索到的知识条目数
    promptTokens?: number;          // 提示词token数
    responseTokens?: number;        // 响应token数
  };
}

// 知识库引用
interface KnowledgeReference {
  id: string;
  title: string;
  excerpt: string;                  // 相关片段
  similarity: number;               // 相似度分数
  category?: string;                // 分类
  tags: string[];                   // 标签
  useCount: number;                 // 使用次数
}

// 增强提示词构建结果
interface EnhancedPrompt {
  systemPrompt: string;
  userPrompt: string;
  knowledgeContext: string;
  totalTokens: number;
}

export class RAGService {
  private defaultConfig: RAGConfig = {
    maxKnowledgeItems: 5,
    similarityThreshold: 0.3, // 降低阈值，提高召回率
    knowledgeWeightFactor: 1.2,
    enableHybridSearch: true,
    includeSourceReferences: true,
    contextWindowSize: 10
  };

  /**
   * 执行RAG检索增强生成
   */
  async generateResponse(
    query: string,
    context: ConversationContext,
    config: Partial<RAGConfig> = {}
  ): Promise<RAGResponse> {
    const startTime = Date.now();
    const ragConfig = { ...this.defaultConfig, ...config };

    try {
      console.log(`🔍 开始RAG处理: "${query}"`);

      // 1. 预处理查询
      const processedQuery = this.preprocessQuery(query, context);
      console.log(`📝 查询预处理: "${processedQuery}"`);

      // 2. 检索相关知识
      const searchStartTime = Date.now();
      const knowledgeResults = await this.retrieveKnowledge(processedQuery, ragConfig);
      const searchTime = Date.now() - searchStartTime;

      console.log(`📚 检索到 ${knowledgeResults.length} 个相关知识条目，耗时: ${searchTime}ms`);

      // 3. 构建增强提示词
      const enhancedPrompt = this.buildEnhancedPrompt(
        processedQuery,
        knowledgeResults,
        context,
        ragConfig
      );

      console.log(`🔧 构建增强提示词，总token数: ${enhancedPrompt.totalTokens}`);

      // 4. 调用AI生成回答
      const generateStartTime = Date.now();
      const aiResponse = await aiServiceManager.chat([
        { role: 'system', content: enhancedPrompt.systemPrompt },
        { role: 'user', content: enhancedPrompt.userPrompt }
      ], context);
      const generateTime = Date.now() - generateStartTime;

      console.log(`🤖 AI生成完成，耗时: ${generateTime}ms`);

      // 5. 记录知识库使用情况
      await this.recordKnowledgeUsage(knowledgeResults, context.sessionId);

      // 6. 计算置信度
      const confidence = this.calculateConfidence(knowledgeResults, aiResponse.success);

      const totalTime = Date.now() - startTime;

      const response: RAGResponse = {
        answer: aiResponse.response,
        knowledgeUsed: this.formatKnowledgeReferences(knowledgeResults),
        confidence,
        searchTime,
        generateTime,
        totalTime,
        searchType: ragConfig.enableHybridSearch ? 'hybrid' : 'vector',
        metadata: {
          queryProcessed: processedQuery,
          knowledgeCount: knowledgeResults.length,
          promptTokens: enhancedPrompt.totalTokens,
          responseTokens: aiResponse.response.length // 简化的token计算
        }
      };

      console.log(`✅ RAG处理完成，总耗时: ${totalTime}ms，置信度: ${confidence}`);
      return response;

    } catch (error) {
      console.error('❌ RAG处理失败:', error);
      
      // 降级策略：直接使用AI回答，不使用知识库
      const fallbackResponse = await this.fallbackGenerate(query, context);
      
      return {
        answer: fallbackResponse,
        knowledgeUsed: [],
        confidence: 0.3, // 低置信度
        searchTime: 0,
        generateTime: Date.now() - startTime,
        totalTime: Date.now() - startTime,
        searchType: 'vector',
        metadata: {
          queryProcessed: query,
          knowledgeCount: 0
        }
      };
    }
  }

  /**
   * 预处理查询文本
   */
  private preprocessQuery(query: string, context: ConversationContext): string {
    // 1. 基础清理
    let processed = query.trim();
    
    // 2. 结合上下文信息
    if (context.intent) {
      // 根据意图调整查询
      switch (context.intent) {
        case 'ticket_inquiry':
          processed = `门票相关: ${processed}`;
          break;
        case 'opening_hours':
          processed = `开放时间相关: ${processed}`;
          break;
        case 'transportation':
          processed = `交通指南相关: ${processed}`;
          break;
        // 可以添加更多意图处理
      }
    }

    // 3. 考虑对话历史
    if (context.messages.length > 1) {
      const lastUserMessage = context.messages
        .filter(m => m.role === 'user')
        .slice(-2, -1)[0]; // 获取倒数第二条用户消息
      
      if (lastUserMessage && processed.length < 10) {
        // 如果当前查询很短，可能是追问，结合上一条消息
        processed = `${lastUserMessage.content} ${processed}`;
      }
    }

    return processed;
  }

  /**
   * 检索相关知识
   */
  private async retrieveKnowledge(
    query: string,
    config: RAGConfig
  ): Promise<SearchResult[]> {
    const searchConfig = {
      similarityThreshold: config.similarityThreshold,
      maxResults: config.maxKnowledgeItems,
      includeContent: true // RAG需要完整内容
    };

    if (config.enableHybridSearch) {
      const result = await vectorSearchService.hybridSearch(query, searchConfig);
      return result.results;
    } else {
      const result = await vectorSearchService.vectorSearch(query, searchConfig);
      return result.results;
    }
  }

  /**
   * 构建增强提示词
   */
  private buildEnhancedPrompt(
    query: string,
    knowledgeResults: SearchResult[],
    context: ConversationContext,
    config: RAGConfig
  ): EnhancedPrompt {
    // 1. 构建知识上下文
    const knowledgeContext = this.buildKnowledgeContext(knowledgeResults);
    
    // 2. 构建系统提示词
    const systemPrompt = this.buildSystemPrompt(context, config);
    
    // 3. 构建用户提示词
    const userPrompt = this.buildUserPrompt(query, knowledgeContext, config);
    
    // 4. 估算token数（简化计算）
    const totalTokens = (systemPrompt + userPrompt).length / 4; // 粗略估算

    return {
      systemPrompt,
      userPrompt,
      knowledgeContext,
      totalTokens
    };
  }

  /**
   * 构建知识上下文
   */
  private buildKnowledgeContext(knowledgeResults: SearchResult[]): string {
    if (knowledgeResults.length === 0) {
      return '暂无相关知识库信息。';
    }

    const knowledgeItems = knowledgeResults.map((item, index) => {
      const categoryInfo = item.category ? `[${item.category}]` : '';
      const tagsInfo = item.tags.length > 0 ? `标签: ${item.tags.join(', ')}` : '';
      
      return `知识${index + 1}${categoryInfo}: ${item.title}
内容: ${item.content || item.summary || ''}
相似度: ${item.similarity}
${tagsInfo}`;
    }).join('\n\n');

    return `以下是相关的知识库信息：\n\n${knowledgeItems}`;
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(context: ConversationContext, config: RAGConfig): string {
    const basePrompt = `你是吴都乔街景区的专业AI客服助手，专门为游客提供准确、友好的咨询服务。

你的职责：
1. 基于提供的知识库信息回答用户问题
2. 提供准确、详细、有用的景区相关信息
3. 保持友好、专业的服务态度
4. 如果知识库中没有相关信息，诚实说明并建议联系人工客服

回答要求：
- 优先使用知识库中的准确信息
- 回答要简洁明了，重点突出
- 如果有多个相关信息，按重要性排序
- 适当使用表情符号增加亲和力
${config.includeSourceReferences ? '- 在回答末尾标注信息来源' : ''}`;

    // 根据上下文调整提示词
    if (context.intent) {
      const intentPrompts = {
        'ticket_inquiry': '\n当前用户咨询门票相关问题，请重点关注票价、优惠政策、购票方式等信息。',
        'opening_hours': '\n当前用户咨询开放时间，请提供准确的营业时间和特殊时间安排。',
        'transportation': '\n当前用户咨询交通信息，请提供详细的交通指南和路线建议。',
        'route_planning': '\n当前用户需要路线规划，请提供游览建议和最佳路线。'
      };
      
      if (intentPrompts[context.intent]) {
        return basePrompt + intentPrompts[context.intent];
      }
    }

    return basePrompt;
  }

  /**
   * 构建用户提示词
   */
  private buildUserPrompt(
    query: string,
    knowledgeContext: string,
    config: RAGConfig
  ): string {
    return `${knowledgeContext}

用户问题：${query}

请基于上述知识库信息回答用户问题。如果知识库中没有相关信息，请诚实说明。`;
  }

  /**
   * 记录知识库使用情况
   */
  private async recordKnowledgeUsage(
    knowledgeResults: SearchResult[],
    sessionId: string
  ): Promise<void> {
    try {
      // 更新知识库条目的使用次数
      for (const item of knowledgeResults) {
        await prisma.knowledgeBase.update({
          where: { id: item.id },
          data: {
            useCount: {
              increment: 1
            }
          }
        });
      }

      console.log(`📊 已更新 ${knowledgeResults.length} 个知识条目的使用统计`);
    } catch (error) {
      console.warn('⚠️ 记录知识库使用情况失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 计算回答置信度
   */
  private calculateConfidence(
    knowledgeResults: SearchResult[],
    aiSuccess: boolean
  ): number {
    if (!aiSuccess) {
      return 0.1; // AI调用失败，极低置信度
    }

    if (knowledgeResults.length === 0) {
      return 0.3; // 没有知识库支持，低置信度
    }

    // 基于最高相似度和知识条目数量计算置信度
    const maxSimilarity = Math.max(...knowledgeResults.map(r => r.similarity));
    const knowledgeBonus = Math.min(knowledgeResults.length * 0.1, 0.3);
    
    const confidence = Math.min(maxSimilarity + knowledgeBonus, 0.95);
    return Math.round(confidence * 100) / 100; // 保留两位小数
  }

  /**
   * 格式化知识库引用
   */
  private formatKnowledgeReferences(knowledgeResults: SearchResult[]): KnowledgeReference[] {
    return knowledgeResults.map(item => ({
      id: item.id,
      title: item.title,
      excerpt: this.extractExcerpt(item.content || item.summary || '', 100),
      similarity: item.similarity,
      category: item.category,
      tags: item.tags,
      useCount: item.useCount
    }));
  }

  /**
   * 提取文本摘要
   */
  private extractExcerpt(content: string, maxLength: number): string {
    if (content.length <= maxLength) {
      return content;
    }
    
    return content.substring(0, maxLength - 3) + '...';
  }

  /**
   * 降级生成策略
   */
  private async fallbackGenerate(
    query: string,
    context: ConversationContext
  ): Promise<string> {
    try {
      console.log('🔄 使用降级策略生成回答');
      
      const aiResponse = await aiServiceManager.chat([
        { 
          role: 'system', 
          content: '你是吴都乔街景区的客服助手。由于知识库暂时不可用，请基于常识回答用户问题，并建议用户联系人工客服获取准确信息。' 
        },
        { role: 'user', content: query }
      ], context);

      return aiResponse.response + '\n\n💡 建议您联系人工客服获取更准确的信息。';
    } catch (error) {
      console.error('❌ 降级策略也失败了:', error);
      return '抱歉，系统暂时无法处理您的问题，请稍后重试或联系人工客服。';
    }
  }
}

// 单例实例
export const ragService = new RAGService();
