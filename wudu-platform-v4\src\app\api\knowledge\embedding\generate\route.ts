/**
 * 向量生成API接口
 * 为知识条目生成embedding向量
 */

import { NextRequest, NextResponse } from 'next/server';
import { embeddingService } from '@/lib/ai/embedding-service';
import { PrismaClient } from '@prisma/client';
import { auth } from '@/lib/auth';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { knowledgeId, forceRegenerate = false } = body;

    // 验证必需参数
    if (!knowledgeId) {
      return NextResponse.json(
        { error: '缺少知识条目ID' },
        { status: 400 }
      );
    }

    console.log(`🔄 开始为知识条目 ${knowledgeId} 生成向量`);

    // 获取知识条目
    const knowledgeItem = await prisma.knowledgeBase.findUnique({
      where: { id: knowledgeId }
    });

    if (!knowledgeItem) {
      return NextResponse.json(
        { error: '知识条目不存在' },
        { status: 404 }
      );
    }

    // 检查是否已有向量且不强制重新生成
    if (knowledgeItem.embedding && !forceRegenerate) {
      return NextResponse.json({
        success: true,
        message: '向量已存在，跳过生成',
        data: {
          knowledgeId,
          hasEmbedding: true,
          embeddingModel: knowledgeItem.embeddingModel,
          embeddingVersion: knowledgeItem.embeddingVersion,
          embeddingUpdatedAt: knowledgeItem.embeddingUpdatedAt
        }
      });
    }

    // 初始化embedding服务
    await embeddingService.initialize();

    // 准备要向量化的文本（标题 + 摘要 + 内容的前1000字符）
    const textToEmbed = [
      knowledgeItem.title,
      knowledgeItem.summary || '',
      knowledgeItem.content.substring(0, 1000)
    ].filter(Boolean).join(' ');

    // 生成向量
    const embeddingResult = await embeddingService.generateEmbedding(textToEmbed);

    // 更新数据库
    const updatedItem = await prisma.knowledgeBase.update({
      where: { id: knowledgeId },
      data: {
        embedding: embeddingResult.vector,
        embeddingModel: embeddingResult.model,
        embeddingVersion: '1.0',
        embeddingUpdatedAt: new Date()
      }
    });

    console.log(`✅ 向量生成完成: ID=${knowledgeId}, 维度=${embeddingResult.dimensions}, 耗时=${embeddingResult.processingTime}ms`);

    return NextResponse.json({
      success: true,
      message: '向量生成成功',
      data: {
        knowledgeId,
        dimensions: embeddingResult.dimensions,
        model: embeddingResult.model,
        processingTime: embeddingResult.processingTime,
        embeddingUpdatedAt: updatedItem.embeddingUpdatedAt
      }
    });

  } catch (error: any) {
    console.error('❌ 向量生成API错误:', error);
    
    return NextResponse.json(
      { 
        error: '向量生成服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const knowledgeId = searchParams.get('id');

    if (!knowledgeId) {
      return NextResponse.json(
        { error: '缺少知识条目ID参数' },
        { status: 400 }
      );
    }

    // 获取知识条目的向量信息
    const knowledgeItem = await prisma.knowledgeBase.findUnique({
      where: { id: knowledgeId },
      select: {
        id: true,
        title: true,
        embedding: true,
        embeddingModel: true,
        embeddingVersion: true,
        embeddingUpdatedAt: true
      }
    });

    if (!knowledgeItem) {
      return NextResponse.json(
        { error: '知识条目不存在' },
        { status: 404 }
      );
    }

    const hasEmbedding = !!knowledgeItem.embedding;
    const embeddingArray = knowledgeItem.embedding as number[] | null;

    return NextResponse.json({
      success: true,
      data: {
        knowledgeId: knowledgeItem.id,
        title: knowledgeItem.title,
        hasEmbedding,
        dimensions: hasEmbedding && embeddingArray ? embeddingArray.length : 0,
        embeddingModel: knowledgeItem.embeddingModel,
        embeddingVersion: knowledgeItem.embeddingVersion,
        embeddingUpdatedAt: knowledgeItem.embeddingUpdatedAt
      }
    });

  } catch (error: any) {
    console.error('❌ 向量信息查询API错误:', error);
    
    return NextResponse.json(
      { 
        error: '向量信息查询失败',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
