import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { auth } from '@/lib/auth';

// GET /api/knowledge/items - 获取知识库列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const categoryId = searchParams.get('categoryId');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const tagId = searchParams.get('tagId');

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { content: { contains: search } },
        { summary: { contains: search } }
      ];
    }

    if (tagId) {
      where.tags = {
        some: {
          tagId: tagId
        }
      };
    }

    // 获取知识库列表
    const [items, total] = await Promise.all([
      prisma.knowledgeBase.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { priority: 'desc' },
          { updatedAt: 'desc' }
        ],
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              avatar: true
            }
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  color: true
                }
              }
            }
          },
          _count: {
            select: {
              versions: true
            }
          }
        }
      }),
      prisma.knowledgeBase.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        items,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ 获取知识库列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取知识库列表失败' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/items - 创建知识条目
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { title, content, summary, categoryId, tagIds, priority = 0 } = body;

    // 验证必需字段
    if (!title || !content) {
      return NextResponse.json(
        { success: false, error: '标题和内容不能为空' },
        { status: 400 }
      );
    }

    // 创建知识条目
    const knowledgeItem = await prisma.knowledgeBase.create({
      data: {
        title,
        content,
        summary,
        categoryId,
        priority,
        status: 'DRAFT',
        createdBy: session.user.id,
        tags: tagIds ? {
          create: tagIds.map((tagId: string) => ({
            tagId
          }))
        } : undefined
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          }
        }
      }
    });

    console.log(`✅ 创建知识条目成功: ${knowledgeItem.id}`);

    return NextResponse.json({
      success: true,
      data: knowledgeItem
    });

  } catch (error) {
    console.error('❌ 创建知识条目失败:', error);
    return NextResponse.json(
      { success: false, error: '创建知识条目失败' },
      { status: 500 }
    );
  }
}
