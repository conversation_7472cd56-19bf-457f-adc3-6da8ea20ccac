import { UnifiedMessage } from '@/types/webhook';

/**
 * 消息处理器
 * 负责将统一格式的消息发送到RAG系统进行处理
 */
export class MessageProcessor {
  private readonly baseUrl: string;

  constructor(baseUrl?: string) {
    // 在服务器端使用内部URL，在客户端使用相对路径
    this.baseUrl = baseUrl || (typeof window === 'undefined' ? 'http://localhost:30002' : '');
  }

  /**
   * 处理消息，调用RAG系统生成回复
   * @param message 统一格式的消息
   * @returns AI生成的回复内容
   */
  async process(message: UnifiedMessage): Promise<string> {
    const startTime = Date.now();
    
    try {
      this.logInfo('Processing message', {
        platform: message.platform,
        userId: message.userId,
        content: message.content.substring(0, 100) + '...'
      });

      // 调用现有的RAG API
      const response = await fetch(`${this.baseUrl}/api/ai/chat`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'User-Agent': 'Webhook-Processor/1.0'
        },
        body: JSON.stringify({
          message: message.content,
          sessionId: message.sessionId,
          userId: message.userId,
          platform: message.platform,
          metadata: {
            messageId: message.id,
            timestamp: message.timestamp,
            messageType: message.messageType
          }
        }),
        // 设置超时时间为30秒
        signal: AbortSignal.timeout(30000)
      });

      if (!response.ok) {
        throw new Error(`RAG API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;

      this.logInfo('Message processed successfully', {
        platform: message.platform,
        userId: message.userId,
        processingTime,
        hasReply: !!result.assistantMessage?.content
      });

      // 返回AI回复内容，如果没有回复则返回默认消息
      return result.assistantMessage?.content || this.getDefaultReply(message.platform);

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logError('Message processing failed', {
        platform: message.platform,
        userId: message.userId,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      // 根据错误类型返回不同的错误消息
      if (error instanceof Error && error.name === 'AbortError') {
        return '抱歉，处理您的消息超时了，请稍后再试。';
      }

      return this.getErrorReply(message.platform);
    }
  }

  /**
   * 获取默认回复消息
   * @param platform 平台类型
   * @returns 默认回复
   */
  private getDefaultReply(platform: string): string {
    const replies = {
      wechat: '感谢您的咨询！我是吴都乔街智能客服，很高兴为您服务。请问有什么可以帮助您的吗？',
      douyin: '感谢您的评论！有任何问题都可以随时咨询我们哦～',
      telegram: 'Hello! I\'m the intelligent customer service assistant. How can I help you today?'
    };

    return replies[platform as keyof typeof replies] || '抱歉，我暂时无法回答这个问题。';
  }

  /**
   * 获取错误回复消息
   * @param platform 平台类型
   * @returns 错误回复
   */
  private getErrorReply(platform: string): string {
    const replies = {
      wechat: '系统暂时繁忙，请稍后再试。如有紧急问题，请联系人工客服。',
      douyin: '系统暂时繁忙，请稍后再试～',
      telegram: 'System is temporarily busy, please try again later.'
    };

    return replies[platform as keyof typeof replies] || '系统暂时繁忙，请稍后再试。';
  }

  /**
   * 记录信息日志
   * @param message 日志信息
   * @param context 上下文信息
   */
  private logInfo(message: string, context?: Record<string, unknown>): void {
    console.log(`[MessageProcessor] ${message}`, context);
  }

  /**
   * 记录错误日志
   * @param message 错误信息
   * @param context 上下文信息
   */
  private logError(message: string, context?: Record<string, unknown>): void {
    console.error(`[MessageProcessor] ${message}`, context);
  }
}
