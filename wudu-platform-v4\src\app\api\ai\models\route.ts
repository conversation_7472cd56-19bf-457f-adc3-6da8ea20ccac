import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 加密密钥
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';

// 解密函数
function decrypt(encryptedText: string): string {
  if (!encryptedText) return '';
  
  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    
    const textParts = encryptedText.split(':');
    if (textParts.length !== 2) {
      return encryptedText;
    }
    
    const iv = Buffer.from(textParts[0], 'hex');
    const encrypted = textParts[1];
    
    const decipher = crypto.createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return '';
  }
}

// 获取DeepSeek可用模型
async function getDeepSeekModels(apiKey: string) {
  try {
    const response = await fetch('https://api.deepseek.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(10000)
    });

    if (response.ok) {
      const data = await response.json();
      return data.data?.map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: 'DeepSeek',
        description: `DeepSeek模型 - ${model.id}`,
        maxTokens: 32768, // DeepSeek默认
        costPer1k: 0.14,
        available: true
      })) || [];
    }
  } catch (error) {
    console.error('获取DeepSeek模型失败:', error);
  }
  return [];
}

// 获取OpenAI可用模型
async function getOpenAIModels(apiKey: string) {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      signal: AbortSignal.timeout(10000)
    });

    if (response.ok) {
      const data = await response.json();
      // 只返回聊天模型
      const chatModels = data.data?.filter((model: any) => 
        model.id.includes('gpt') && !model.id.includes('instruct')
      ) || [];
      
      return chatModels.map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: 'OpenAI',
        description: `OpenAI模型 - ${model.id}`,
        maxTokens: model.id.includes('gpt-4') ? 8192 : 4096,
        costPer1k: model.id.includes('gpt-4') ? 30 : 1.5,
        available: true
      }));
    }
  } catch (error) {
    console.error('获取OpenAI模型失败:', error);
  }
  return [];
}

// GET /api/ai/models - 获取可用AI模型列表
export async function GET() {
  try {
    console.log('🔍 获取可用AI模型列表...');
    
    const allModels = [];
    
    // 获取DeepSeek模型
    try {
      const deepseekConfig = await prisma.aiConfig.findUnique({
        where: { configKey: 'deepseek_api_key' }
      });
      
      if (deepseekConfig?.configValue) {
        const apiKey = deepseekConfig.isEncrypted ? 
          decrypt(deepseekConfig.configValue) : 
          deepseekConfig.configValue;
        
        if (apiKey) {
          const deepseekModels = await getDeepSeekModels(apiKey);
          allModels.push(...deepseekModels);
          console.log(`✅ 获取到 ${deepseekModels.length} 个DeepSeek模型`);
        }
      }
    } catch (error) {
      console.error('❌ 获取DeepSeek模型失败:', error);
    }
    
    // 获取OpenAI模型
    try {
      const openaiConfig = await prisma.aiConfig.findUnique({
        where: { configKey: 'openai_api_key' }
      });
      
      if (openaiConfig?.configValue) {
        const apiKey = openaiConfig.isEncrypted ? 
          decrypt(openaiConfig.configValue) : 
          openaiConfig.configValue;
        
        if (apiKey) {
          const openaiModels = await getOpenAIModels(apiKey);
          allModels.push(...openaiModels);
          console.log(`✅ 获取到 ${openaiModels.length} 个OpenAI模型`);
        }
      }
    } catch (error) {
      console.error('❌ 获取OpenAI模型失败:', error);
    }
    
    // 如果没有获取到任何模型，返回默认模型列表
    if (allModels.length === 0) {
      console.log('📋 返回默认模型列表');
      const defaultModels = [
        {
          id: 'deepseek-chat',
          name: 'DeepSeek Chat',
          provider: 'DeepSeek',
          description: '高性能对话模型，适合客服场景',
          maxTokens: 32768,
          costPer1k: 0.14,
          available: false
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'OpenAI',
          description: '经典对话模型，响应快速',
          maxTokens: 4096,
          costPer1k: 1.5,
          available: false
        },
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'OpenAI',
          description: '最强大的对话模型，理解能力出色',
          maxTokens: 8192,
          costPer1k: 30,
          available: false
        }
      ];
      allModels.push(...defaultModels);
    }

    return NextResponse.json({
      success: true,
      message: '获取AI模型列表成功',
      models: allModels,
      count: allModels.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 获取AI模型列表失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取AI模型列表失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
