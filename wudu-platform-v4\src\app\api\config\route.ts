import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const config = {
      // 应用配置
      app: {
        url: process.env.NEXT_PUBLIC_APP_URL,
        environment: process.env.NODE_ENV || 'development',
        version: '4.0.0'
      },
      
      // 钉钉配置
      dingtalk: {
        clientId: process.env.NEXT_PUBLIC_DINGTALK_CLIENT_ID,
        corpId: process.env.DINGTALK_CORP_ID,
        hasSecret: !!process.env.DINGTALK_CLIENT_SECRET,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/dingtalk-callback`,
        loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/login`,
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
      },
      
      // 数据库配置
      database: {
        connected: !!process.env.DATABASE_URL,
        type: 'MySQL',
        host: 'localhost:3306',
        database: 'wudu_platform'
      },
      
      // API端点
      api: {
        dingtalkLogin: '/api/auth/dingtalk/official-login',
        healthCheck: '/api/health/database',
        config: '/api/config'
      },
      
      // 系统状态
      status: {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        platform: process.platform,
        nodeVersion: process.version
      }
    };

    return NextResponse.json({
      success: true,
      message: '系统配置信息',
      config,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 获取配置信息失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取配置信息失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
