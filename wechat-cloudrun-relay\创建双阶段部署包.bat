@echo off
echo ========================================
echo 创建双阶段回复部署包
echo ========================================

echo.
echo 正在创建部署包...

:: 删除旧的部署包
if exist cloudrun-deploy-two-stage.zip del cloudrun-deploy-two-stage.zip

:: 创建新的部署包
powershell -Command "Compress-Archive -Path 'index.js', 'package.json', 'Dockerfile' -DestinationPath 'cloudrun-deploy-two-stage.zip' -Force"

echo.
echo ========================================
echo 双阶段回复部署包创建完成！
echo ========================================
echo.
echo 文件名: cloudrun-deploy-two-stage.zip
echo.
echo 包含功能:
echo - 双阶段回复机制
echo - 智能临时回复生成
echo - 正式回复接收接口
echo - 兼容传统模式
echo.
echo 部署步骤:
echo 1. 上传 cloudrun-deploy-two-stage.zip 到微信云托管
echo 2. 重新部署服务
echo 3. 重启本地服务器以支持双阶段模式
echo.
echo ========================================

pause
