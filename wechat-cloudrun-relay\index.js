const express = require('express');
const axios = require('axios');
const crypto = require('crypto');

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// 配置
const LOCAL_SERVER_URL = process.env.LOCAL_SERVER_URL || 'http://219.138.230.35:30002';
const LOCAL_API_PATH = '/api/wechat-message';
const WECHAT_TOKEN = process.env.WECHAT_TOKEN || 'wuduqiaojie2025token';

// 方案1：混合模式配置
const ASYNC_MODE = process.env.WECHAT_ASYNC_MODE === 'true';
const WECHAT_CUSTOMER_SERVICE_URL = 'https://api.weixin.qq.com/cgi-bin/message/custom/send';

// Access Token自动管理
const WECHAT_APPID = process.env.WECHAT_APPID || '';
const WECHAT_SECRET = process.env.WECHAT_SECRET || '';
let currentAccessToken = process.env.WECHAT_ACCESS_TOKEN || '';
let tokenExpireTime = 0;

// Access Token自动刷新管理 - 使用官方推荐的稳定版接口
async function refreshAccessToken() {
  if (!WECHAT_APPID || !WECHAT_SECRET) {
    console.warn('[Token管理] 缺少APPID或SECRET，无法自动刷新Token');
    return false;
  }

  try {
    console.log('[Token管理] 开始刷新Access Token（稳定版接口）...');
    const response = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/stable_token',
      {
        grant_type: 'client_credential',
        appid: WECHAT_APPID,
        secret: WECHAT_SECRET,
        force_refresh: false // 普通模式，推荐使用
      },
      {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' },
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false // 忽略SSL证书验证
        })
      }
    );

    if (response.data.access_token) {
      // 检查Token长度（官方要求至少512字符空间）
      if (response.data.access_token.length > 500) {
        console.warn('[Token管理] ⚠️ Token长度超过500字符，请确保存储空间足够');
      }

      currentAccessToken = response.data.access_token;
      tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000; // 提前5分钟刷新

      console.log('[Token管理] ✅ Access Token刷新成功');
      console.log('[Token管理] Token长度:', response.data.access_token.length);
      console.log('[Token管理] 有效期至:', new Date(tokenExpireTime).toLocaleString());
      console.log('[Token管理] 实际有效期:', response.data.expires_in, '秒');

      return true;
    } else {
      console.error('[Token管理] ❌ 刷新失败:', response.data);
      return false;
    }
  } catch (error) {
    console.error('[Token管理] ❌ 刷新异常:', error.message);
    return false;
  }
}

// 获取当前有效的Access Token
function getCurrentAccessToken() {
  if (Date.now() > tokenExpireTime) {
    console.warn('[Token管理] ⚠️ Token已过期，尝试刷新...');
    refreshAccessToken(); // 异步刷新
  }
  return currentAccessToken;
}

console.log('[云托管中转] 启动配置:');
console.log('[云托管中转] 本地服务器地址:', LOCAL_SERVER_URL);
console.log('[云托管中转] API路径:', LOCAL_API_PATH);
console.log('[云托管中转] 微信Token:', WECHAT_TOKEN);
console.log('[云托管中转] 异步模式:', ASYNC_MODE ? '启用' : '禁用');
console.log('[云托管中转] APPID配置:', WECHAT_APPID ? '已配置' : '未配置');
console.log('[云托管中转] SECRET配置:', WECHAT_SECRET ? '已配置' : '未配置');

// 生成智能临时回复消息
function generateThinkingMessage(userMessage) {
  const message = userMessage.toLowerCase();
  const hour = new Date().getHours();

  // 🕐 时间感知 - 优先级最高
  const timeGreeting = getTimeBasedGreeting(hour);

  // 😊 情感分析 - 检测用户情绪
  const emotionResponse = analyzeUserEmotion(message);
  if (emotionResponse) {
    return emotionResponse;
  }

  // 景区相关
  if (message.includes('景区') || message.includes('门票') || message.includes('开放时间') ||
      message.includes('营业时间') || message.includes('票价') || message.includes('门票价格')) {
    return `🏛️ ${timeGreeting}小乔正在为您查询景区相关信息，请稍候...`;
  }

  // 交通路线
  if (message.includes('路线') || message.includes('怎么去') || message.includes('交通') ||
      message.includes('地址') || message.includes('位置') || message.includes('怎么走') ||
      message.includes('公交') || message.includes('地铁') || message.includes('开车')) {
    return `🗺️ ${timeGreeting}小乔正在为您规划最佳路线，请稍等...`;
  }

  // 美食餐饮
  if (message.includes('美食') || message.includes('餐厅') || message.includes('小吃') ||
      message.includes('吃饭') || message.includes('特色菜') || message.includes('推荐菜')) {
    return `🍜 ${timeGreeting}小乔正在为您推荐美食信息，请稍候...`;
  }

  // 住宿相关
  if (message.includes('住宿') || message.includes('酒店') || message.includes('民宿') ||
      message.includes('宾馆') || message.includes('过夜') || message.includes('住哪')) {
    return "🏨 小乔正在为您查找住宿信息，请稍等...";
  }

  // 活动娱乐
  if (message.includes('活动') || message.includes('表演') || message.includes('节目') ||
      message.includes('演出') || message.includes('娱乐') || message.includes('玩什么')) {
    return "🎭 小乔正在为您查询活动信息，请稍候...";
  }

  // 购物相关
  if (message.includes('购物') || message.includes('特产') || message.includes('纪念品') ||
      message.includes('商店') || message.includes('买什么') || message.includes('礼品')) {
    return "🛍️ 小乔正在为您推荐购物信息，请稍等...";
  }

  // 天气相关
  if (message.includes('天气') || message.includes('温度') || message.includes('下雨') ||
      message.includes('晴天') || message.includes('穿什么')) {
    return "🌤️ 小乔正在为您查询天气信息，请稍候...";
  }

  // 问题咨询
  if (message.includes('问题') || message.includes('咨询') || message.includes('帮助') ||
      message.includes('客服') || message.includes('投诉') || message.includes('建议')) {
    return "💬 小乔正在为您转接专业客服，请稍等...";
  }

  // 基于问句类型的智能判断
  if (message.includes('什么时候') || message.includes('几点') || message.includes('时间')) {
    return "⏰ 小乔正在为您查询时间信息，请稍候...";
  }

  if (message.includes('多少钱') || message.includes('价格') || message.includes('费用') ||
      message.includes('收费') || message.includes('花费')) {
    return "💰 小乔正在为您查询价格信息，请稍等...";
  }

  if (message.includes('怎么样') || message.includes('好不好') || message.includes('值得') ||
      message.includes('推荐')) {
    return "⭐ 小乔正在为您分析推荐信息，请稍候...";
  }

  // 基于消息长度和复杂度的智能选择
  if (message.length > 20) {
    return `🧠 ${timeGreeting}小乔正在仔细分析您的问题，请稍等片刻...`;
  } else if (message.length > 10) {
    return `🔍 ${timeGreeting}小乔正在为您搜索相关信息，请稍候...`;
  } else {
    return `💭 ${timeGreeting}小乔正在思考您的问题，马上回复...`;
  }
}

// 🕐 时间感知功能
function getTimeBasedGreeting(hour) {
  if (hour >= 5 && hour < 9) {
    return "🌅 早上好！";
  } else if (hour >= 9 && hour < 12) {
    return "☀️ 上午好！";
  } else if (hour >= 12 && hour < 14) {
    return "🌞 中午好！";
  } else if (hour >= 14 && hour < 18) {
    return "🌤️ 下午好！";
  } else if (hour >= 18 && hour < 22) {
    return "🌆 晚上好！";
  } else {
    return "🌙 夜深了，";
  }
}

// 😊 情感分析功能
function analyzeUserEmotion(message) {
  // 感谢情绪
  if (message.includes('谢谢') || message.includes('感谢') || message.includes('太好了') ||
      message.includes('非常感谢') || message.includes('多谢')) {
    return "😊 不客气！小乔很高兴为您服务，正在查询中...";
  }

  // 急迫情绪
  if (message.includes('急') || message.includes('快') || message.includes('马上') ||
      message.includes('立即') || message.includes('赶紧') || message.includes('速度')) {
    return "⚡ 小乔明白您很着急，正在优先为您处理，请稍等...";
  }

  // 困惑情绪
  if (message.includes('不懂') || message.includes('不明白') || message.includes('搞不清') ||
      message.includes('糊涂') || message.includes('不知道') || message.includes('迷茫')) {
    return "🤗 别担心，小乔来帮您解答疑惑，正在查询中...";
  }

  // 抱怨情绪
  if (message.includes('太贵') || message.includes('坑人') || message.includes('不满意') ||
      message.includes('差评') || message.includes('投诉') || message.includes('退款') ||
      message.includes('垃圾') || message.includes('骗人') || message.includes('失望') ||
      message.includes('糟糕') || message.includes('恶心') || message.includes('坑爹')) {
    return "😔 小乔理解您的感受，正在记录您反馈的信息...";
  }

  // 兴奋情绪
  if (message.includes('太棒了') || message.includes('好期待') || message.includes('超级') ||
      message.includes('太赞') || message.includes('amazing') || message.includes('哇')) {
    return "🎉 小乔也很兴奋！正在为您准备精彩信息...";
  }

  // 疑问情绪
  if (message.includes('真的吗') || message.includes('确定吗') || message.includes('靠谱吗') ||
      message.includes('可信吗') || message.includes('是否')) {
    return "🔍 小乔会为您提供准确信息，正在核实中...";
  }

  // 第一次来访
  if (message.includes('第一次') || message.includes('初次') || message.includes('新手') ||
      message.includes('没来过') || message.includes('不熟悉')) {
    return "👋 欢迎第一次来到吴都乔街！小乔正在为您准备新手指南...";
  }

  return null; // 没有检测到特殊情绪，继续常规处理
}

// 发送客服消息函数（使用动态Token）
async function sendCustomerServiceMessage(toUser, content) {
  const accessToken = getCurrentAccessToken();

  if (!accessToken) {
    console.error('[云托管中转] 缺少有效的Access Token，无法发送客服消息');
    return false;
  }

  try {
    const messageData = {
      touser: toUser,
      msgtype: 'text',
      text: {
        content: content
      }
    };

    const response = await axios.post(
      `${WECHAT_CUSTOMER_SERVICE_URL}?access_token=${accessToken}`,
      messageData,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000,
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false
        })
      }
    );

    console.log('[云托管中转] 客服消息发送结果:', response.data);

    // 如果Token过期，尝试刷新
    if (response.data.errcode === 40001 || response.data.errcode === 42001) {
      console.warn('[云托管中转] Token过期，尝试刷新后重试...');
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        // 重试发送
        const retryResponse = await axios.post(
          `${WECHAT_CUSTOMER_SERVICE_URL}?access_token=${currentAccessToken}`,
          messageData,
          {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000,
            httpsAgent: new (require('https').Agent)({
              rejectUnauthorized: false
            })
          }
        );
        console.log('[云托管中转] 重试发送结果:', retryResponse.data);
        return retryResponse.data.errcode === 0;
      }
    }

    return response.data.errcode === 0;
  } catch (error) {
    console.error('[云托管中转] 发送客服消息失败:', error.message);
    return false;
  }
}

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    mode: ASYNC_MODE ? 'async' : 'sync',
    hasAccessToken: !!currentAccessToken,
    tokenExpireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null,
    autoRefresh: !!(WECHAT_APPID && WECHAT_SECRET),
    localServer: LOCAL_SERVER_URL
  });
});

// Token管理接口
app.get('/admin/token-status', (req, res) => {
  res.json({
    hasToken: !!currentAccessToken,
    expireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null,
    isExpired: Date.now() > tokenExpireTime,
    autoRefreshEnabled: !!(WECHAT_APPID && WECHAT_SECRET),
    timeToExpire: tokenExpireTime ? Math.max(0, tokenExpireTime - Date.now()) : 0
  });
});

app.post('/admin/refresh-token', async (req, res) => {
  console.log('[Token管理] 手动刷新Token请求');
  const success = await refreshAccessToken();
  res.json({
    success,
    message: success ? 'Token刷新成功' : 'Token刷新失败',
    expireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null
  });
});

// 紧急强制刷新Token（用于Token泄露等紧急情况）
app.post('/admin/force-refresh-token', async (req, res) => {
  console.log('[Token管理] 🚨 紧急强制刷新Token请求');

  try {
    const response = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/stable_token',
      {
        grant_type: 'client_credential',
        appid: WECHAT_APPID,
        secret: WECHAT_SECRET,
        force_refresh: true // 强制刷新模式
      },
      {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' },
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false
        })
      }
    );

    if (response.data.access_token) {
      currentAccessToken = response.data.access_token;
      tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000;

      console.log('[Token管理] ✅ 强制刷新成功，旧Token已失效');

      res.json({
        success: true,
        message: '强制刷新成功，旧Token已立即失效',
        expireTime: new Date(tokenExpireTime).toISOString(),
        warning: '请确保已排查Token泄露原因'
      });
    } else {
      throw new Error('强制刷新失败: ' + JSON.stringify(response.data));
    }
  } catch (error) {
    console.error('[Token管理] ❌ 强制刷新失败:', error.message);
    res.status(500).json({
      success: false,
      message: '强制刷新失败',
      error: error.message
    });
  }
});

// 新增：接收本地服务器正式回复的接口
app.post('/wechat-final-reply', async (req, res) => {
  try {
    const { fromUserName, finalReply, sessionId } = req.body;

    console.log('[云托管中转] 收到正式回复请求:', {
      fromUserName: fromUserName ? fromUserName.substring(0, 10) + '...' : 'undefined',
      replyLength: finalReply ? finalReply.length : 0,
      sessionId
    });

    if (!fromUserName || !finalReply) {
      console.error('[云托管中转] 正式回复参数不完整');
      return res.status(400).json({
        success: false,
        error: '缺少必要参数：fromUserName 或 finalReply'
      });
    }

    console.log('[云托管中转] 发送正式回复:', finalReply.substring(0, 100) + (finalReply.length > 100 ? '...' : ''));

    const success = await sendCustomerServiceMessage(fromUserName, finalReply);

    if (success) {
      console.log('[云托管中转] ✅ 正式回复发送成功');
    } else {
      console.error('[云托管中转] ❌ 正式回复发送失败');
    }

    res.json({
      success,
      message: success ? '正式回复发送成功' : '正式回复发送失败',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[云托管中转] 处理正式回复异常:', error.message);
    res.status(500).json({
      success: false,
      error: '服务器内部错误',
      message: error.message
    });
  }
});

// 微信服务器验证接口 (GET)
app.get('/wechat-message', (req, res) => {
  try {
    const { signature, timestamp, nonce, echostr } = req.query;

    console.log('[云托管中转] 收到微信验证请求:', { signature, timestamp, nonce, echostr });

    if (!signature || !timestamp || !nonce || !echostr) {
      console.error('[云托管中转] 验证参数不完整');
      return res.status(400).send('Bad Request');
    }

    // 验证签名
    const token = WECHAT_TOKEN;
    const tmpArr = [token, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const sha1 = crypto.createHash('sha1').update(tmpStr).digest('hex');

    console.log('[云托管中转] 签名验证:', {
      expected: sha1,
      received: signature,
      match: sha1 === signature
    });

    if (sha1 === signature) {
      console.log('[云托管中转] 验证成功，返回echostr:', echostr);
      res.send(echostr);
    } else {
      console.error('[云托管中转] 签名验证失败');
      res.status(401).send('Unauthorized');
    }

  } catch (error) {
    console.error('[云托管中转] 验证过程出错:', error);
    res.status(500).send('Internal Server Error');
  }
});

// 微信消息推送接收接口 - 方案1混合模式
app.post('/wechat-message', async (req, res) => {
  const startTime = Date.now();
  let hasResponded = false;

  if (ASYNC_MODE) {
    // 异步模式：立即返回success，启动异步处理
    console.log('[云托管中转] 异步模式：立即返回success');
    res.send('success');

    // 异步处理消息，不阻塞响应
    processMessageAsync(req.body, startTime).catch(error => {
      console.error('[云托管中转] 异步处理出现未捕获错误:', error);
    });
  } else {
    // 同步模式：保持原有逻辑
    console.log('[云托管中转] 同步模式：等待AI处理');

    // 设置4.5秒超时保护
    const timeout = setTimeout(() => {
      if (!hasResponded) {
        hasResponded = true;
        console.log('[云托管中转] 处理超时，返回success');
        res.send('success');
      }
    }, 4500);

    try {
      hasResponded = await processSyncMessage(req, res, timeout, hasResponded, startTime);
    } catch (error) {
      console.error('[云托管中转] 同步处理失败:', error);
      clearTimeout(timeout);
      if (!hasResponded) {
        res.send('success');
      }
    }
  }
});

// 异步处理消息函数 - 双阶段回复实现
async function processMessageAsync(messageBody, startTime) {
  try {
    console.log('[云托管中转] 开始双阶段异步处理消息');

    // 检查是否是配置测试请求
    if (messageBody.action === 'CheckContainerPath') {
      console.log('[云托管中转] 配置测试请求，无需处理');
      return;
    }

    // 1. 构建转发数据（包含回调地址）
    const messageData = {
      toUserName: messageBody.ToUserName,
      fromUserName: messageBody.FromUserName,
      createTime: messageBody.CreateTime,
      msgType: messageBody.MsgType,
      content: messageBody.Content || messageBody.Text || '',
      msgId: messageBody.MsgId,
      originalData: messageBody,
      // 添加中转平台回调地址，让本地服务器知道如何发送正式回复
      callbackUrl: `${process.env.CLOUDRUN_SERVICE_URL || 'https://message-relay10-176329-6-1371697534.sh.run.tcloudbase.com'}/wechat-final-reply`
    };

    console.log('[云托管中转] 调用本地AI服务（双阶段模式）:', LOCAL_SERVER_URL + LOCAL_API_PATH);

    // 2. 调用本地AI服务，期望立即返回临时回复标识
    const response = await axios.post(LOCAL_SERVER_URL + LOCAL_API_PATH, messageData, {
      timeout: 5000, // 5秒超时，期望立即返回
      headers: {
        'Content-Type': 'application/json',
        'X-Wechat-Relay': 'true',
        'X-Async-Mode': 'true',
        'X-Two-Stage-Reply': 'true' // 标识双阶段回复模式
      }
    });

    console.log('[云托管中转] 本地AI服务响应:', response.status, response.data);

    // 3. 处理本地服务器的响应
    if (response.data && response.data.status === 'processing' && response.data.tempReply) {
      // 双阶段模式：立即发送临时回复
      const tempMessage = response.data.tempReply;
      const tempSent = await sendCustomerServiceMessage(messageBody.FromUserName, tempMessage);

      if (tempSent) {
        console.log('[云托管中转] 双阶段临时回复发送成功:', tempMessage);
      } else {
        console.warn('[云托管中转] 临时回复发送失败');
      }

      console.log('[云托管中转] 等待本地服务器发送正式回复...');
    } else if (response.data && response.data.reply) {
      // 兼容模式：本地服务器直接返回了最终回复
      const aiReply = response.data.reply;
      const success = await sendCustomerServiceMessage(messageBody.FromUserName, aiReply);

      if (success) {
        console.log('[云托管中转] 兼容模式AI回复发送成功:', aiReply.substring(0, 50) + '...');
      } else {
        console.error('[云托管中转] 兼容模式AI回复发送失败');
      }
    } else {
      // 降级模式：发送默认临时回复
      console.log('[云托管中转] 本地服务无有效回复，发送默认临时回复');
      const defaultTempMessage = generateThinkingMessage(messageBody.Content || messageBody.Text || '');
      await sendCustomerServiceMessage(messageBody.FromUserName, defaultTempMessage);
    }

    const totalTime = Date.now() - startTime;
    console.log(`[云托管中转] 异步处理完成，总耗时: ${totalTime}ms`);

    // 默认返回success
    if (!hasResponded) {
      hasResponded = true;
      res.send('success');
    }

  } catch (error) {
    console.error('[云托管中转] 异步处理失败:', error.message);

    // 发送错误回复
    try {
      await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，系统出现问题，请稍后再试或联系人工客服。');
    } catch (sendError) {
      console.error('[云托管中转] 发送错误回复也失败了:', sendError.message);
    }
  }
}

// 同步处理函数（保持向后兼容）
async function processSyncMessage(req, res, timeout, hasResponded, startTime) {
  try {
    console.log('[云托管中转] 收到微信消息:', JSON.stringify(req.body, null, 2));

    // 检查是否是配置测试请求
    if (req.body.action === 'CheckContainerPath') {
      console.log('[云托管中转] 配置测试请求');
      clearTimeout(timeout);
      if (!hasResponded) {
        res.send('success');
        return true;
      }
      return hasResponded;
    }

    // 构建转发数据
    const messageData = {
      toUserName: req.body.ToUserName,
      fromUserName: req.body.FromUserName,
      createTime: req.body.CreateTime,
      msgType: req.body.MsgType,
      content: req.body.Content || '',
      msgId: req.body.MsgId,
      originalData: req.body
    };

    console.log('[云托管中转] 转发消息到本地服务器:', LOCAL_SERVER_URL + LOCAL_API_PATH);

    // 转发到本地服务器
    const response = await axios.post(LOCAL_SERVER_URL + LOCAL_API_PATH, messageData, {
      timeout: 4000, // 4秒超时
      headers: {
        'Content-Type': 'application/json',
        'X-Wechat-Relay': 'true',
        'X-Source-IP': req.ip
      }
    });

    console.log('[云托管中转] 本地服务器响应:', response.status, response.data);

    clearTimeout(timeout);

    // 如果本地服务器返回了回复内容，则转发给微信
    if (response.data && response.data.reply && !hasResponded) {
      const replyData = response.data.reply;

      console.log('[云托管中转] 回复内容:', replyData);
      console.log('[云托管中转] 消息类型:', req.body.MsgType);

      // 构建被动回复消息的XML
      const replyXml = `<xml>
<ToUserName><![CDATA[${req.body.FromUserName}]]></ToUserName>
<FromUserName><![CDATA[${req.body.ToUserName}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${replyData}]]></Content>
</xml>`;

      hasResponded = true;
      res.set('Content-Type', 'application/xml');
      res.send(replyXml);

      const totalTime = Date.now() - startTime;
      console.log(`[云托管中转] 消息处理完成，总耗时: ${totalTime}ms`);
    } else if (!hasResponded) {
      hasResponded = true;
      res.send('success');
      console.log('[云托管中转] 本地服务器无回复内容，返回success');
    }

    return hasResponded;

  } catch (error) {
    console.error('[云托管中转] 同步处理失败:', error.message);
    clearTimeout(timeout);
    if (!hasResponded) {
      res.send('success');
      hasResponded = true;
    }
    return hasResponded;
  }
}

// 错误处理
app.use((error, req, res, next) => {
  console.error('[云托管中转] 服务错误:', error);
  res.status(500).send('success'); // 对微信始终返回success
});

// 启动Token自动管理
async function initializeTokenManager() {
  if (WECHAT_APPID && WECHAT_SECRET) {
    console.log('[Token管理] 🔄 启动自动Token管理...');

    // 立即获取Token
    await refreshAccessToken();

    // 设置定时刷新（每90分钟）
    setInterval(async () => {
      console.log('[Token管理] ⏰ 定时刷新Token...');
      await refreshAccessToken();
    }, 90 * 60 * 1000); // 90分钟

    console.log('[Token管理] ✅ 自动Token管理已启动');
  } else if (ASYNC_MODE) {
    console.warn('[Token管理] ⚠️ 异步模式需要配置WECHAT_APPID和WECHAT_SECRET以启用自动Token管理');
  }
}

const PORT = process.env.PORT || 80;
app.listen(PORT, async () => {
  console.log(`[云托管中转] 方案1混合模式服务启动成功！`);
  console.log(`[云托管中转] 端口: ${PORT}`);
  console.log(`[云托管中转] 本地服务器: ${LOCAL_SERVER_URL}${LOCAL_API_PATH}`);
  console.log(`[云托管中转] 运行模式: ${ASYNC_MODE ? '异步模式' : '同步模式'}`);
  console.log(`[云托管中转] 微信Token: ${WECHAT_TOKEN ? '已配置' : '未配置'}`);
  console.log(`[云托管中转] 自动Token管理: ${(WECHAT_APPID && WECHAT_SECRET) ? '已启用' : '未启用'}`);

  // 初始化Token管理
  await initializeTokenManager();

  console.log(`[云托管中转] 🚀 方案1已就绪，支持立即回复+AI智能回答！`);
  console.log(`[云托管中转] 📋 管理接口: /health, /admin/token-status, /admin/refresh-token`);
});
