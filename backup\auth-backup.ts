import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 完整的钉钉用户信息接口 - 基于您的成功实现
interface DingTalkUser {
  // 基本标识
  unionId: string;          // 钉钉全局唯一用户ID
  openId: string;           // 开放平台用户ID
  userId: string;           // 企业内用户ID

  // 基本信息
  name: string;             // 用户姓名
  nick?: string;            // 用户昵称
  avatarUrl?: string;       // 头像URL
  mobile: string;           // 手机号
  email?: string;           // 邮箱
  stateCode?: string;       // 国家码

  // 职业信息
  jobNumber?: string;       // 工号
  title?: string;           // 职位
  workPlace?: string;       // 工作地点
  hiredDate?: string;       // 入职日期
  remark?: string;          // 备注

  // 状态信息
  active: boolean;          // 是否在职
  admin: boolean;           // 是否管理员
  boss: boolean;            // 是否老板
  senior?: boolean;         // 是否高管
  realAuthed?: boolean;     // 是否实名认证

  // 组织信息
  departments: Department[];
  roles: Role[];
  leaderInDepts?: LeaderInfo[];
}

interface Department {
  deptId: number;           // 部门ID
  name: string;             // 部门名称
  parentId?: number;        // 父部门ID
  order?: number;           // 排序
}

interface Role {
  roleId: string;           // 角色ID
  roleName: string;         // 角色名称
  groupName?: string;       // 角色组名称
}

interface LeaderInfo {
  deptId: number;           // 部门ID
  leader: boolean;          // 是否为部门领导
}

// 兼容的用户接口
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
  // 扩展钉钉信息
  dingTalkInfo?: DingTalkUser;
}

interface AuthState {
  user: User | null;
  token: string | null;
  login: (authCode: string, state: string) => Promise<boolean>;
  loginDemo: () => void; // 临时演示登录
  loginFromStorage: () => boolean; // 从localStorage加载用户信息
  logout: () => void;
  isAuthenticated: () => boolean;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      
      login: async (authCode: string, state: string) => {
        try {
          console.log('🚀 开始钉钉登录，authCode:', authCode);

          // 使用您现有的钉钉登录API
          const response = await fetch('http://219.138.230.35:33018/api/auth/dingtalk/official-login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ authCode, state })
          });

          const data = await response.json();
          console.log('📝 后端响应:', data);

          if (data.success && data.user) {
            // 构建完整的钉钉用户信息
            const dingTalkInfo: DingTalkUser = {
              // 基本标识
              unionId: data.user.unionId || '',
              openId: data.user.openId || '',
              userId: data.user.userId || data.user.userid || '',

              // 基本信息
              name: data.user.name || '',
              nick: data.user.nick || '',
              avatarUrl: data.user.avatarUrl || data.user.avatar || '',
              mobile: data.user.mobile || '',
              email: data.user.email || '',
              stateCode: data.user.stateCode || '+86',

              // 职业信息
              jobNumber: data.user.jobNumber || '',
              title: data.user.title || '',
              workPlace: data.user.workPlace || '',
              hiredDate: data.user.hiredDate || '',
              remark: data.user.remark || '',

              // 状态信息
              active: data.user.active !== false, // 默认为true
              admin: data.user.admin === true,
              boss: data.user.boss === true,
              senior: data.user.senior === true,
              realAuthed: data.user.realAuthed === true,

              // 组织信息
              departments: Array.isArray(data.user.departments) ? data.user.departments.map((dept: any) => ({
                deptId: dept.deptId || dept.id,
                name: dept.name || '',
                parentId: dept.parentId,
                order: dept.order
              })) : [],

              roles: Array.isArray(data.user.roles) ? data.user.roles.map((role: any) => ({
                roleId: role.roleId || role.id || '',
                roleName: role.roleName || role.name || '',
                groupName: role.groupName || ''
              })) : [],

              leaderInDepts: Array.isArray(data.user.leaderInDepts) ? data.user.leaderInDepts : []
            };

            // 构建兼容的用户对象
            const user: User = {
              id: dingTalkInfo.userId || dingTalkInfo.unionId,
              name: dingTalkInfo.name,
              email: dingTalkInfo.email || '',
              avatar: dingTalkInfo.avatarUrl || '',
              roles: dingTalkInfo.roles.map(role => role.roleName).filter(Boolean),
              dingTalkInfo: dingTalkInfo
            };

            console.log('✅ 用户信息构建完成:', user);
            console.log('📊 部门信息:', dingTalkInfo.departments);
            console.log('🎭 角色信息:', dingTalkInfo.roles);
            console.log('🔐 权限状态:', {
              active: dingTalkInfo.active,
              admin: dingTalkInfo.admin,
              boss: dingTalkInfo.boss,
              senior: dingTalkInfo.senior
            });

            set({ user, token: data.token || 'dingtalk_' + Date.now() });
            return true;
          }

          console.error('❌ 登录失败:', data.message || '未知错误');
          return false;
        } catch (error) {
          console.error('❌ 登录异常:', error);
          return false;
        }
      },
      
      loginDemo: () => {
        // 临时演示登录功能
        const demoUser: User = {
          id: 'demo-user-1',
          name: '演示用户',
          email: '<EMAIL>',
          avatar: '',
          roles: ['admin']
        };
        set({ user: demoUser, token: 'demo-token-123' });
      },

      loginFromStorage: () => {
        // 从localStorage加载钉钉登录的用户信息
        try {
          const userInfo = localStorage.getItem('userInfo');
          const authToken = localStorage.getItem('authToken');

          if (userInfo && authToken) {
            const userData = JSON.parse(userInfo);
            const user: User = {
              id: userData.userId || userData.unionId,
              name: userData.name,
              email: userData.email || '',
              avatar: userData.avatar || '',
              roles: userData.admin ? ['admin'] : ['user']
            };

            set({ user, token: authToken });
            return true;
          }
          return false;
        } catch (error) {
          console.error('从localStorage加载用户信息失败:', error);
          return false;
        }
      },

      logout: () => {
        set({ user: null, token: null });
      },

      isAuthenticated: () => {
        const { token } = get();
        return !!token;
      }
    }),
    {
      name: 'auth-storage'
    }
  )
);
