import crypto from 'crypto';

// 加密密钥 - 在生产环境中应该从环境变量获取
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

/**
 * 加密文本
 * @param text 要加密的文本
 * @returns 加密后的文本 (格式: iv:encrypted)
 */
export function encrypt(text: string): string {
  if (!text) return '';

  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('加密失败:', error);
    return text; // 如果加密失败，返回原文本
  }
}

/**
 * 解密文本
 * @param encryptedText 加密的文本 (格式: iv:encrypted)
 * @returns 解密后的文本
 */
export function decrypt(encryptedText: string): string {
  if (!encryptedText) return '';

  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);

    const textParts = encryptedText.split(':');
    if (textParts.length !== 2) {
      // 如果格式不正确，可能是未加密的文本
      return encryptedText;
    }

    const iv = Buffer.from(textParts[0], 'hex');
    const encrypted = textParts[1];

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return ''; // 如果解密失败，返回空字符串
  }
}

/**
 * 检查文本是否已加密
 * @param text 要检查的文本
 * @returns 是否已加密
 */
export function isEncrypted(text: string): boolean {
  if (!text) return false;
  
  // 检查是否符合加密格式 (iv:encrypted)
  const parts = text.split(':');
  if (parts.length !== 2) return false;
  
  // 检查IV部分是否是有效的十六进制
  const ivPart = parts[0];
  if (ivPart.length !== 32) return false; // IV应该是16字节 = 32个十六进制字符
  
  return /^[0-9a-fA-F]+$/.test(ivPart);
}

/**
 * 生成随机密钥
 * @param length 密钥长度（字节）
 * @returns 十六进制格式的密钥
 */
export function generateKey(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 哈希文本（用于密码等）
 * @param text 要哈希的文本
 * @param salt 盐值（可选）
 * @returns 哈希值
 */
export function hash(text: string, salt?: string): string {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(text, actualSalt, 10000, 64, 'sha512');
  return actualSalt + ':' + hash.toString('hex');
}

/**
 * 验证哈希
 * @param text 原始文本
 * @param hashedText 哈希后的文本
 * @returns 是否匹配
 */
export function verifyHash(text: string, hashedText: string): boolean {
  try {
    const parts = hashedText.split(':');
    if (parts.length !== 2) return false;
    
    const salt = parts[0];
    const originalHash = parts[1];
    
    const hash = crypto.pbkdf2Sync(text, salt, 10000, 64, 'sha512');
    return hash.toString('hex') === originalHash;
  } catch (error) {
    console.error('验证哈希失败:', error);
    return false;
  }
}
