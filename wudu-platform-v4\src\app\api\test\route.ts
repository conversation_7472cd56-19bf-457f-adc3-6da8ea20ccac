import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const signature = url.searchParams.get('signature');
  const timestamp = url.searchParams.get('timestamp');
  const nonce = url.searchParams.get('nonce');
  const echostr = url.searchParams.get('echostr');

  return NextResponse.json({
    status: 'ok',
    message: 'NATAPP connection test successful',
    timestamp: new Date().toISOString(),
    url: request.url,
    params: { signature, timestamp, nonce, echostr },
    headers: Object.fromEntries(request.headers.entries())
  });
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    status: 'ok',
    message: 'POST request successful',
    timestamp: new Date().toISOString()
  });
}
