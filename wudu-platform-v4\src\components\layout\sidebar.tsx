'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Home,
  MessageSquare,
  BookOpen,
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';

interface SidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const navigation = [
  { name: '仪表板', href: '/dashboard', icon: Home },
  { name: 'AI客服', href: '/ai-chat', icon: MessageSquare },
  { name: '知识库', href: '/knowledge', icon: BookOpen },
  { name: '数据分析', href: '/analytics', icon: BarChart3 },
  { name: '系统设置', href: '/settings', icon: Settings },
];

export function Sidebar({ open, onOpenChange }: SidebarProps) {
  const pathname = usePathname();
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1024);
    };

    // 初始检查
    checkScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <>
      {/* Mobile backdrop */}
      {open && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => onOpenChange(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 flex w-64 flex-col bg-background border-r transition-transform duration-300 ease-in-out lg:translate-x-0",
        open ? "translate-x-0" : "-translate-x-full lg:w-16"
      )}>
        {/* Logo */}
        <div className="flex h-14 items-center border-b px-4">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded bg-primary flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">吴</span>
            </div>
            {(open || isLargeScreen) && (
              <span className="font-semibold">智能客服中控台</span>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 p-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link key={item.name} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    !open && "lg:justify-center lg:px-2"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {(open || isLargeScreen) && (
                    <span className="ml-2">{item.name}</span>
                  )}
                </Button>
              </Link>
            );
          })}
        </nav>

        {/* Toggle button for desktop */}
        <div className="hidden lg:flex border-t p-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(!open)}
            className="w-full"
          >
            {open ? (
              <ChevronLeft className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </>
  );
}
