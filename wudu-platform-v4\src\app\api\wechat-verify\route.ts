import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

const WECHAT_TOKEN = 'wuduqiaojie2025token';

export async function GET(request: NextRequest) {
  try {
    console.log('[WeChat Verify] Received verification request');
    
    const url = new URL(request.url);
    const signature = url.searchParams.get('signature');
    const timestamp = url.searchParams.get('timestamp');
    const nonce = url.searchParams.get('nonce');
    const echostr = url.searchParams.get('echostr');

    console.log('[WeChat Verify] Parameters:', {
      signature: signature?.substring(0, 10) + '...',
      timestamp,
      nonce: nonce?.substring(0, 10) + '...',
      echostr: echostr?.substring(0, 10) + '...'
    });

    if (!signature || !timestamp || !nonce || !echostr) {
      console.error('[WeChat Verify] Missing parameters');
      return new NextResponse('Bad Request', { status: 400 });
    }

    // 微信签名验证
    const tmpArr = [WECHAT_TOKEN, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const sha1 = crypto.createHash('sha1').update(tmpStr).digest('hex');

    console.log('[WeChat Verify] Signature check:', {
      expected: sha1,
      received: signature,
      match: sha1 === signature
    });

    if (sha1 === signature) {
      console.log('[WeChat Verify] Verification successful, returning echostr');
      return new NextResponse(echostr, { status: 200 });
    } else {
      console.error('[WeChat Verify] Signature verification failed');
      return new NextResponse('Unauthorized', { status: 401 });
    }
  } catch (error) {
    console.error('[WeChat Verify] Error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[WeChat Message] ========== 收到POST请求 ==========');
    console.log('[WeChat Message] URL:', request.url);
    console.log('[WeChat Message] Method:', request.method);
    console.log('[WeChat Message] Headers:', Object.fromEntries(request.headers.entries()));

    // 获取URL参数
    const url = new URL(request.url);
    const signature = url.searchParams.get('signature');
    const timestamp = url.searchParams.get('timestamp');
    const nonce = url.searchParams.get('nonce');
    const encrypt_type = url.searchParams.get('encrypt_type');
    const msg_signature = url.searchParams.get('msg_signature');

    console.log('[WeChat Message] URL参数:', {
      signature, timestamp, nonce, encrypt_type, msg_signature
    });

    const body = await request.text();
    console.log('[WeChat Message] Raw body length:', body.length);
    console.log('[WeChat Message] Raw body:', body);

    if (!timestamp || !nonce) {
      console.error('[WeChat Message] Missing required parameters');
      return new NextResponse('Bad Request', { status: 400 });
    }

    // 根据加密类型处理
    if (encrypt_type === 'aes') {
      console.log('[WeChat Message] 处理AES加密模式');

      if (!msg_signature) {
        console.error('[WeChat Message] Missing msg_signature for encrypted message');
        return new NextResponse('Bad Request', { status: 400 });
      }

      // 解析加密消息体
      let encryptData;
      try {
        if (body.startsWith('{')) {
          // JSON格式
          encryptData = JSON.parse(body);
        } else {
          // XML格式 - 简单解析
          const encryptMatch = body.match(/<Encrypt><!\[CDATA\[(.*?)\]\]><\/Encrypt>/);
          if (encryptMatch) {
            encryptData = { Encrypt: encryptMatch[1] };
          } else {
            throw new Error('Cannot parse XML');
          }
        }
      } catch (error) {
        console.error('[WeChat Message] Failed to parse encrypted body:', error);
        return new NextResponse('Bad Request', { status: 400 });
      }

      console.log('[WeChat Message] Encrypt字段:', encryptData.Encrypt?.substring(0, 50) + '...');

      // 验证msg_signature
      const token = WECHAT_TOKEN;
      const tmpArr = [token, timestamp, nonce, encryptData.Encrypt].sort();
      const tmpStr = tmpArr.join('');
      const expectedSignature = crypto.createHash('sha1').update(tmpStr).digest('hex');

      console.log('[WeChat Message] 签名验证:', {
        expected: expectedSignature,
        received: msg_signature,
        match: expectedSignature === msg_signature
      });

      if (expectedSignature !== msg_signature) {
        console.error('[WeChat Message] msg_signature verification failed');
        return new NextResponse('Unauthorized', { status: 401 });
      }

      console.log('[WeChat Message] 签名验证成功，暂时返回success（解密功能待实现）');
      return new NextResponse('success', { status: 200 });

    } else {
      // 明文模式
      console.log('[WeChat Message] 处理明文模式');

      if (!signature) {
        console.error('[WeChat Message] Missing signature for plain text message');
        return new NextResponse('Bad Request', { status: 400 });
      }

      // 验证signature
      const token = WECHAT_TOKEN;
      const tmpArr = [token, timestamp, nonce].sort();
      const tmpStr = tmpArr.join('');
      const expectedSignature = crypto.createHash('sha1').update(tmpStr).digest('hex');

      console.log('[WeChat Message] 明文签名验证:', {
        expected: expectedSignature,
        received: signature,
        match: expectedSignature === signature
      });

      if (expectedSignature !== signature) {
        console.error('[WeChat Message] signature verification failed');
        return new NextResponse('Unauthorized', { status: 401 });
      }

      console.log('[WeChat Message] 明文消息验证成功');
      console.log('[WeChat Message] 消息内容:', body);

      // 简单回复
      const replyXml = `<xml>
<ToUserName><![CDATA[sender]]></ToUserName>
<FromUserName><![CDATA[receiver]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[您好！我收到了您的消息，AI功能正在开发中...]]></Content>
</xml>`;

      console.log('[WeChat Message] Sending reply XML');
      return new NextResponse(replyXml, {
        status: 200,
        headers: { 'Content-Type': 'application/xml; charset=utf-8' }
      });
    }

  } catch (error) {
    console.error('[WeChat Message] Error:', error);
    return new NextResponse('success', { status: 200 });
  }
}
