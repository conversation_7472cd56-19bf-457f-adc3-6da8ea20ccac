/**
 * 简化的RAG系统测试脚本
 * 测试向量搜索和知识库检索功能
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 开始RAG系统基础测试');
  console.log('='.repeat(60));

  try {
    // 1. 检查数据库连接
    console.log('\n📋 步骤1: 检查数据库连接');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ 数据库连接正常');

    // 2. 检查知识库数据
    console.log('\n📋 步骤2: 检查知识库数据');
    const totalKnowledge = await prisma.knowledgeBase.count({
      where: { status: 'ACTIVE' }
    });
    console.log(`📚 活跃知识库条目: ${totalKnowledge} 个`);

    const withEmbedding = await prisma.knowledgeBase.count({
      where: { 
        status: 'ACTIVE',
        embedding: { not: null }
      }
    });
    console.log(`🔍 有向量的知识条目: ${withEmbedding} 个`);

    // 3. 测试知识库查询
    console.log('\n📋 步骤3: 测试知识库查询');
    const sampleKnowledge = await prisma.knowledgeBase.findMany({
      where: { status: 'ACTIVE' },
      take: 3,
      select: {
        id: true,
        title: true,
        content: true,
        category: {
          select: {
            name: true
          }
        },
        tags: {
          select: {
            tag: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    console.log('📖 样本知识库条目:');
    sampleKnowledge.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.title}`);
      console.log(`      分类: ${item.category?.name || '未分类'}`);
      console.log(`      标签: ${item.tags.map(t => t.tag.name).join(', ') || '无标签'}`);
      console.log(`      内容: ${item.content.substring(0, 100)}...`);
      console.log('');
    });

    // 4. 测试API接口状态
    console.log('\n📋 步骤4: 测试API接口');
    try {
      const response = await fetch('http://localhost:30002/api/ai/rag', {
        method: 'GET'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ RAG API接口正常');
        console.log(`   服务状态: ${data.data.status}`);
        console.log(`   支持的意图: ${data.data.supportedIntents.length} 个`);
      } else {
        console.log('❌ RAG API接口异常');
      }
    } catch (error) {
      console.log('❌ 无法连接到API接口:', error.message);
    }

    // 5. 测试简单的向量搜索
    console.log('\n📋 步骤5: 测试向量搜索功能');
    
    // 查找有向量的知识条目
    const vectorKnowledge = await prisma.knowledgeBase.findFirst({
      where: { 
        status: 'ACTIVE',
        embedding: { not: null }
      }
    });

    if (vectorKnowledge) {
      console.log('✅ 找到有向量的知识条目');
      console.log(`   标题: ${vectorKnowledge.title}`);
      console.log(`   向量维度: ${vectorKnowledge.embedding ? JSON.parse(vectorKnowledge.embedding).length : 0}`);
    } else {
      console.log('⚠️ 没有找到有向量的知识条目');
      console.log('   建议运行向量生成脚本');
    }

    // 6. 测试基础RAG功能（不依赖AI服务）
    console.log('\n📋 步骤6: 测试基础RAG功能');
    
    const testQueries = [
      '门票价格',
      '开放时间',
      '交通指南'
    ];

    for (const query of testQueries) {
      console.log(`\n🔍 测试查询: "${query}"`);
      
      // 简单的关键词匹配搜索
      const results = await prisma.knowledgeBase.findMany({
        where: {
          status: 'ACTIVE',
          OR: [
            { title: { contains: query } },
            { content: { contains: query } }
          ]
        },
        take: 3,
        select: {
          id: true,
          title: true,
          content: true
        }
      });

      if (results.length > 0) {
        console.log(`   ✅ 找到 ${results.length} 个相关结果`);
        results.forEach((result, index) => {
          console.log(`      ${index + 1}. ${result.title}`);
        });
      } else {
        console.log(`   ❌ 没有找到相关结果`);
      }
    }

    // 7. 生成测试报告
    console.log('\n📋 步骤7: 生成测试报告');
    
    const report = {
      timestamp: new Date().toISOString(),
      database: {
        connected: true,
        totalKnowledge,
        withEmbedding
      },
      api: {
        accessible: true // 基于前面的测试结果
      },
      recommendations: []
    };

    if (withEmbedding === 0) {
      report.recommendations.push('需要生成向量数据');
    }
    
    if (totalKnowledge < 10) {
      report.recommendations.push('建议增加更多知识库内容');
    }

    report.recommendations.push('配置AI服务API密钥以启用完整RAG功能');

    console.log('\n📊 测试报告:');
    console.log(`   数据库连接: ${report.database.connected ? '✅' : '❌'}`);
    console.log(`   知识库条目: ${report.database.totalKnowledge} 个`);
    console.log(`   向量数据: ${report.database.withEmbedding} 个`);
    console.log(`   API接口: ${report.api.accessible ? '✅' : '❌'}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 建议:');
      report.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    console.log('\n🎉 基础测试完成！');
    
    // 8. 下一步指导
    console.log('\n📋 下一步操作指导:');
    console.log('1. 配置AI服务API密钥 (.env.local)');
    console.log('2. 生成向量数据 (如果需要)');
    console.log('3. 通过Web界面测试RAG功能');
    console.log('4. 访问: http://localhost:30002/ai-rag-test');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
main().catch(console.error);
