import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { userService } from '@/lib/db';

// 钉钉配置 - 从原 test-no-proxy-backend.js 迁移
const DINGTALK_CONFIG = {
    CLIENT_ID: 'dinggai5cng27n76jvbq',
    CLIENT_SECRET: 'duaT4c-YiWDDS2OysZ2sZgWBFTSN5WgxBurruHuPYSTTpYtE4gLwLqYq1wZ3iNo7',
    CORP_ID: 'dinga4e1b27e70b3dc4b24f2f5cc6abecb85'
};

export async function POST(request: NextRequest) {
    try {
        const { authCode, state } = await request.json();
        
        console.log('🚀 开始钉钉第三方网站登录流程');
        console.log('📝 授权码:', authCode);
        console.log('📝 状态码:', state);

        // 1. 使用授权码获取用户访问令牌
        console.log('🔑 步骤1: 获取用户访问令牌...');
        const tokenResponse = await axios.post('https://api.dingtalk.com/v1.0/oauth2/userAccessToken', {
            clientId: DINGTALK_CONFIG.CLIENT_ID,
            clientSecret: DINGTALK_CONFIG.CLIENT_SECRET,
            code: authCode,
            grantType: 'authorization_code'
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const { accessToken, refreshToken } = tokenResponse.data;
        console.log('✅ 用户访问令牌获取成功');

        // 2. 使用访问令牌获取用户基本信息
        console.log('👤 步骤2: 获取用户基本信息...');
        const userInfoResponse = await axios.get('https://api.dingtalk.com/v1.0/contact/users/me', {
            headers: {
                'x-acs-dingtalk-access-token': accessToken
            }
        });

        const userInfo = userInfoResponse.data;
        console.log('✅ 用户基本信息获取成功:', userInfo);

        // 3. 获取企业访问令牌
        console.log('🏢 步骤3: 获取企业访问令牌...');
        const corpTokenResponse = await axios.get('https://oapi.dingtalk.com/gettoken', {
            params: {
                appkey: DINGTALK_CONFIG.CLIENT_ID,
                appsecret: DINGTALK_CONFIG.CLIENT_SECRET
            }
        });

        const corpAccessToken = corpTokenResponse.data.access_token;
        console.log('✅ 企业访问令牌获取成功');

        // 4. 通过unionId获取userId
        console.log('🔗 步骤4: 通过unionId获取userId...');
        const userIdResponse = await axios.post('https://oapi.dingtalk.com/topapi/user/getbyunionid', {
            unionid: userInfo.unionId
        }, {
            params: {
                access_token: corpAccessToken
            }
        });

        const userId = userIdResponse.data.result?.userid;
        if (!userId) {
            throw new Error('无法获取用户ID');
        }
        console.log('✅ 用户ID获取成功:', userId);

        // 5. 获取用户详细信息
        console.log('📋 步骤5: 获取用户详细信息...');
        const detailResponse = await axios.post('https://oapi.dingtalk.com/topapi/v2/user/get', {
            userid: userId
        }, {
            params: {
                access_token: corpAccessToken
            }
        });

        const detailInfo = detailResponse.data.result || {};
        console.log('✅ 用户详细信息获取成功:', detailInfo);

        // 6. 获取部门详细信息
        console.log('🏢 步骤6: 获取部门详细信息...');
        let departments = [];
        if (detailInfo.dept_id_list && Array.isArray(detailInfo.dept_id_list)) {
            for (const deptId of detailInfo.dept_id_list) {
                try {
                    const deptResponse = await axios.post('https://oapi.dingtalk.com/topapi/v2/department/get', {
                        dept_id: deptId
                    }, {
                        params: {
                            access_token: corpAccessToken
                        }
                    });

                    if (deptResponse.data.errcode === 0) {
                        const deptInfo = deptResponse.data.result;
                        departments.push({
                            deptId: deptInfo.dept_id,
                            name: deptInfo.name,
                            parentId: deptInfo.parent_id,
                            order: deptInfo.order
                        });
                    }
                } catch (deptError) {
                    console.warn('获取部门信息失败:', deptError.message);
                }
            }
        }
        console.log('✅ 部门信息获取成功:', departments);

        // 7. 获取用户角色信息
        console.log('🎭 步骤7: 获取用户角色信息...');
        let roles = [];
        try {
            // 使用用户详细信息中的角色列表
            if (detailInfo.role_list && Array.isArray(detailInfo.role_list)) {
                roles = detailInfo.role_list.map((role: any) => ({
                    roleId: String(role.id || role.role_id || ''),
                    roleName: role.name || role.role_name || '',
                    groupName: role.group_name || ''
                }));
            }

            // 如果没有角色信息，尝试获取企业角色列表
            if (roles.length === 0) {
                const roleResponse = await axios.post('https://oapi.dingtalk.com/topapi/role/list', {
                    size: 200,
                    offset: 0
                }, {
                    params: {
                        access_token: corpAccessToken
                    }
                });

                if (roleResponse.data.errcode === 0) {
                    const allRoles = roleResponse.data.result?.list || [];
                    // 简化处理：如果用户是管理员，给予默认角色
                    if (detailInfo.admin) {
                        roles.push({
                            roleId: 'admin',
                            roleName: '管理员',
                            groupName: '系统角色'
                        });
                    }

                    // 根据职位添加角色
                    if (detailInfo.title) {
                        roles.push({
                            roleId: 'position_' + Date.now(),
                            roleName: detailInfo.title,
                            groupName: '职位角色'
                        });
                    }
                }
            }
        } catch (roleError) {
            console.warn('获取角色信息失败:', roleError.message);
            // 提供默认角色
            if (detailInfo.admin) {
                roles.push({
                    roleId: 'admin',
                    roleName: '管理员',
                    groupName: '系统角色'
                });
            } else {
                roles.push({
                    roleId: 'user',
                    roleName: '普通用户',
                    groupName: '系统角色'
                });
            }
        }

        console.log('🎭 用户角色信息:', roles);

        // 8. 构建完整的用户信息
        const completeUserInfo = {
            // 基本标识
            unionId: userInfo.unionId || '',
            openId: userInfo.openId || '',
            userId: userId || userInfo.unionId || '',

            // 基本信息
            name: detailInfo.name || userInfo.nick || '',
            nick: userInfo.nick || detailInfo.name || '',
            avatarUrl: detailInfo.avatar || userInfo.avatarUrl || '',
            mobile: detailInfo.mobile || userInfo.mobile || '',
            email: detailInfo.email || userInfo.email || '',
            stateCode: userInfo.stateCode || '+86',

            // 职业信息
            jobNumber: detailInfo.job_number || '',
            title: detailInfo.title || '',
            workPlace: detailInfo.work_place || '',
            hiredDate: detailInfo.hired_date || '',
            remark: detailInfo.remark || '',

            // 状态信息
            active: detailInfo.active !== false,
            admin: detailInfo.admin === true,
            boss: detailInfo.boss === true,
            senior: detailInfo.senior === true,
            realAuthed: detailInfo.real_authed === true,

            // 组织信息
            departments: departments,
            roles: roles,
            leaderInDepts: detailInfo.leader_in_dept || []
        };

        console.log('🎉 钉钉第三方网站登录成功!');
        console.log('👤 完整用户信息:', completeUserInfo);

        // 9. 保存用户信息到数据库
        console.log('💾 步骤9: 保存用户信息到数据库...');
        try {
            const dbUser = await userService.upsertUser({
                unionId: completeUserInfo.unionId,
                openId: completeUserInfo.openId,
                userId: completeUserInfo.userId,
                name: completeUserInfo.name,
                nick: completeUserInfo.nick,
                avatar: completeUserInfo.avatarUrl,
                mobile: completeUserInfo.mobile,
                email: completeUserInfo.email,
                stateCode: completeUserInfo.stateCode,
                jobNumber: completeUserInfo.jobNumber,
                title: completeUserInfo.title,
                workPlace: completeUserInfo.workPlace,
                hiredDate: completeUserInfo.hiredDate,
                remark: completeUserInfo.remark,
                active: completeUserInfo.active,
                admin: completeUserInfo.admin,
                boss: completeUserInfo.boss,
                senior: completeUserInfo.senior,
                realAuthed: completeUserInfo.realAuthed
            });

            // 更新用户部门信息
            if (departments.length > 0) {
                await userService.updateUserDepartments(dbUser.id, departments);
            }

            // 更新用户角色信息
            if (roles.length > 0) {
                await userService.updateUserRoles(dbUser.id, roles);
            }

            // 创建用户会话
            const sessionToken = `dingtalk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期
            await userService.createUserSession(dbUser.id, sessionToken, expiresAt);

            console.log('✅ 用户信息已保存到数据库');

            return NextResponse.json({
                success: true,
                message: '钉钉第三方网站登录成功',
                user: completeUserInfo,
                token: sessionToken,
                timestamp: new Date().toISOString()
            });
        } catch (dbError) {
            console.error('❌ 数据库保存失败:', dbError);
            // 即使数据库保存失败，也返回成功，但不包含token
            return NextResponse.json({
                success: true,
                message: '钉钉第三方网站登录成功（数据库保存失败）',
                user: completeUserInfo,
                token: `dingtalk_${Date.now()}`,
                timestamp: new Date().toISOString(),
                warning: '用户信息未能保存到数据库'
            });
        }

    } catch (error: any) {
        console.error('❌ 钉钉登录失败:', error);
        
        return NextResponse.json({
            success: false,
            error: 'DINGTALK_LOGIN_FAILED',
            message: error.message || '钉钉登录失败',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// 健康检查
export async function GET() {
    return NextResponse.json({
        status: 'ok',
        message: '钉钉登录API服务正常',
        timestamp: new Date().toISOString(),
        config: {
            clientId: DINGTALK_CONFIG.CLIENT_ID,
            corpId: DINGTALK_CONFIG.CORP_ID,
            hasSecret: !!DINGTALK_CONFIG.CLIENT_SECRET
        }
    });
}
