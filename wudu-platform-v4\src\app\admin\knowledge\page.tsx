'use client';

import { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { EnhancedSearch } from '@/components/knowledge/enhanced-search';
import { EnhancedSearchResults } from '@/components/knowledge/enhanced-search-results';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  Tag,
  Folder,
  Filter
} from 'lucide-react';

interface KnowledgeItem {
  id: string;
  title: string;
  summary?: string;
  status: string;
  priority: number;
  viewCount: number;
  useCount: number;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
    color?: string;
  };
  creator: {
    id: string;
    name: string;
    avatar?: string;
  };
  tags: Array<{
    tag: {
      id: string;
      name: string;
      color?: string;
    };
  }>;
  _count: {
    versions: number;
  };
}

interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  _count?: {
    knowledgeItems: number;
    children: number;
  };
}

interface KnowledgeTag {
  id: string;
  name: string;
  description?: string;
  color?: string;
  useCount: number;
  _count?: {
    knowledgeBaseTags: number;
  };
}

// 搜索结果接口
interface SearchResult {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  category?: string;
  tags: string[];
  similarity?: number;
  relevanceScore?: number;
  priority: number;
  useCount: number;
  createdAt: string;
  updatedAt: string;
}

type SearchType = 'keyword' | 'vector' | 'hybrid';

export default function KnowledgePage() {
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<KnowledgeTag[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [currentSearchMode, setCurrentSearchMode] = useState<SearchType>('keyword');
  const [searchInfo, setSearchInfo] = useState<{
    query: string;
    searchType: SearchType;
    searchTime: number;
    totalCount: number;
  } | null>(null);

  // 处理搜索结果
  const handleSearchResults = (results: SearchResult[], searchInfo: {
    query: string;
    searchType: SearchType;
    searchTime: number;
    totalCount: number;
  }) => {
    setSearchResults(results);
    setSearchInfo(searchInfo);

    // 同时更新传统的knowledgeItems状态以保持兼容性
    const convertedItems: KnowledgeItem[] = results.map(result => ({
      id: result.id,
      title: result.title,
      content: result.content || '',
      summary: result.summary || '',
      priority: result.priority,
      useCount: result.useCount,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      status: 'ACTIVE' as any,
      categoryId: '',
      createdBy: '',
      version: 1,
      category: result.category ? { id: '', name: result.category } : null,
      tags: result.tags.map(tag => ({ tag: { id: '', name: tag } }))
    }));

    setKnowledgeItems(convertedItems);
  };

  // 处理搜索模式改变
  const handleSearchModeChange = (mode: SearchType) => {
    setCurrentSearchMode(mode);
  };

  // 加载分类列表
  const loadCategories = async () => {
    try {
      const response = await fetch('/api/knowledge/categories?includeCount=true');
      const data = await response.json();
      
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  // 加载标签列表
  const loadTags = async () => {
    try {
      const response = await fetch('/api/knowledge/tags?includeCount=true');
      const data = await response.json();
      
      if (data.success) {
        setTags(data.data);
      }
    } catch (error) {
      console.error('加载标签失败:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        loadCategories(),
        loadTags()
      ]);
      setLoading(false);
    };

    loadData();
  }, []);

  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return 'bg-green-100 text-green-800';
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800';
      case 'ARCHIVED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 状态文本映射
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PUBLISHED': return '已发布';
      case 'DRAFT': return '草稿';
      case 'ARCHIVED': return '已归档';
      default: return status;
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">知识库管理</h1>
            <p className="text-muted-foreground mt-2">
              管理AI客服的知识库内容，包括分类、标签和版本控制
            </p>
          </div>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            新建知识条目
          </Button>
        </div>

        <Tabs defaultValue="items" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="items" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              知识条目
            </TabsTrigger>
            <TabsTrigger value="categories" className="flex items-center gap-2">
              <Folder className="h-4 w-4" />
              分类管理
            </TabsTrigger>
            <TabsTrigger value="tags" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              标签管理
            </TabsTrigger>
          </TabsList>

          {/* 知识条目标签页 */}
          <TabsContent value="items" className="space-y-6">
            {/* 增强搜索组件 */}
            <EnhancedSearch
              onSearchResults={handleSearchResults}
              onSearchModeChange={handleSearchModeChange}
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
            />

            {/* 增强搜索结果 */}
            {loading ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">加载中...</p>
                </CardContent>
              </Card>
            ) : searchInfo ? (
              <EnhancedSearchResults
                results={searchResults}
                searchInfo={searchInfo}
                onEdit={(id) => console.log('编辑:', id)}
                onDelete={(id) => console.log('删除:', id)}
                onView={(id) => console.log('查看:', id)}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">请使用上方搜索功能查找知识条目</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 分类管理标签页 */}
          <TabsContent value="categories">
            <Card>
              <CardHeader>
                <CardTitle>分类管理</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">分类管理功能开发中...</p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 标签管理标签页 */}
          <TabsContent value="tags">
            <Card>
              <CardHeader>
                <CardTitle>标签管理</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">标签管理功能开发中...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
