import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { auth } from '@/lib/auth';

// GET /api/knowledge/categories - 获取分类列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeCount = searchParams.get('includeCount') === 'true';
    const parentId = searchParams.get('parentId');

    const where: any = {};
    if (parentId) {
      where.parentId = parentId;
    } else if (parentId === null) {
      where.parentId = null; // 只获取根分类
    }

    const categories = await prisma.kbCategory.findMany({
      where,
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' }
      ],
      include: {
        children: {
          orderBy: [
            { sortOrder: 'asc' },
            { name: 'asc' }
          ],
          include: includeCount ? {
            _count: {
              select: {
                knowledgeItems: true
              }
            }
          } : undefined
        },
        _count: includeCount ? {
          select: {
            knowledgeItems: true,
            children: true
          }
        } : undefined
      }
    });

    return NextResponse.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('❌ 获取分类列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取分类列表失败' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/categories - 创建分类
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, description, parentId, icon, color, sortOrder = 0 } = body;

    // 验证必需字段
    if (!name) {
      return NextResponse.json(
        { success: false, error: '分类名称不能为空' },
        { status: 400 }
      );
    }

    // 检查同级分类名称是否重复
    const existingCategory = await prisma.kbCategory.findFirst({
      where: {
        name,
        parentId
      }
    });

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: '同级分类中已存在相同名称' },
        { status: 400 }
      );
    }

    // 创建分类
    const category = await prisma.kbCategory.create({
      data: {
        name,
        description,
        parentId,
        icon,
        color,
        sortOrder,
        status: 'ACTIVE'
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            knowledgeItems: true,
            children: true
          }
        }
      }
    });

    console.log(`✅ 创建分类成功: ${category.id}`);

    return NextResponse.json({
      success: true,
      data: category
    });

  } catch (error) {
    console.error('❌ 创建分类失败:', error);
    return NextResponse.json(
      { success: false, error: '创建分类失败' },
      { status: 500 }
    );
  }
}
