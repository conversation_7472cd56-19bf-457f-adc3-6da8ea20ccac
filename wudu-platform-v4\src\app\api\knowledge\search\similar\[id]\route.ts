/**
 * 相似条目搜索API
 * 根据指定的知识条目ID查找相似内容
 */

import { NextRequest, NextResponse } from 'next/server';
import { vectorSearchService } from '@/lib/ai/vector-search';
import { auth } from '@/lib/auth';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const similarityThreshold = parseFloat(searchParams.get('threshold') || '0.7');
    const maxResults = parseInt(searchParams.get('limit') || '10');
    const includeContent = searchParams.get('content') === 'true';

    // 验证ID参数
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: '无效的条目ID' },
        { status: 400 }
      );
    }

    // 配置搜索参数
    const searchConfig = {
      similarityThreshold: Math.max(0, Math.min(1, similarityThreshold)),
      maxResults: Math.max(1, Math.min(50, maxResults)),
      includeContent
    };

    console.log(`🔍 查找相似条目: ID=${id}, 阈值=${similarityThreshold}`);

    // 执行相似条目搜索
    const searchResult = await vectorSearchService.findSimilarItems(id, searchConfig);

    console.log(`✅ 相似条目搜索完成: 找到 ${searchResult.results.length} 个相似条目，耗时 ${searchResult.searchTime}ms`);

    return NextResponse.json({
      success: true,
      data: searchResult,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ 相似条目搜索API错误:', error);
    
    // 处理特定错误
    if (error.message.includes('不存在')) {
      return NextResponse.json(
        { error: '指定的知识条目不存在或没有向量数据' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { 
        error: '相似条目搜索服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();
    
    const { 
      similarityThreshold = 0.7,
      maxResults = 10,
      includeContent = false,
      filters = {}
    } = body;

    // 验证ID参数
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: '无效的条目ID' },
        { status: 400 }
      );
    }

    // 配置搜索参数
    const searchConfig = {
      similarityThreshold: Math.max(0, Math.min(1, similarityThreshold)),
      maxResults: Math.max(1, Math.min(50, maxResults)),
      includeContent,
      ...filters
    };

    console.log(`🔍 POST相似条目搜索: ID=${id}, 配置=`, searchConfig);

    // 执行相似条目搜索
    const searchResult = await vectorSearchService.findSimilarItems(id, searchConfig);

    // 如果有额外的过滤条件，应用过滤
    let filteredResults = searchResult.results;
    
    if (filters.category) {
      filteredResults = filteredResults.filter(item => 
        item.category === filters.category
      );
    }
    
    if (filters.tags && filters.tags.length > 0) {
      filteredResults = filteredResults.filter(item =>
        filters.tags.some((tag: string) => item.tags.includes(tag))
      );
    }
    
    if (filters.minPriority !== undefined) {
      filteredResults = filteredResults.filter(item =>
        item.priority >= filters.minPriority
      );
    }

    // 更新结果
    const finalResult = {
      ...searchResult,
      results: filteredResults,
      totalCount: filteredResults.length
    };

    console.log(`✅ POST相似条目搜索完成: 找到 ${finalResult.results.length} 个相似条目`);

    return NextResponse.json({
      success: true,
      data: finalResult,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ POST相似条目搜索API错误:', error);
    
    // 处理特定错误
    if (error.message.includes('不存在')) {
      return NextResponse.json(
        { error: '指定的知识条目不存在或没有向量数据' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { 
        error: '相似条目搜索服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
