@echo off
echo 正在创建微信云托管部署包...

:: 删除旧的部署包
if exist "cloudrun-deploy.zip" del "cloudrun-deploy.zip"

:: 创建临时目录
if exist "temp-deploy" rmdir /s /q "temp-deploy"
mkdir "temp-deploy"

:: 复制必要文件
copy "index.js" "temp-deploy\"
copy "package.json" "temp-deploy\"
copy "Dockerfile" "temp-deploy\"

:: 创建ZIP包
powershell "Compress-Archive -Path 'temp-deploy\*' -DestinationPath 'cloudrun-deploy.zip' -Force"

:: 清理临时目录
rmdir /s /q "temp-deploy"

echo 部署包创建完成: cloudrun-deploy.zip
echo.
echo 接下来请：
echo 1. 登录微信云托管控制台: https://cloud.weixin.qq.com/
echo 2. 创建环境: wechat-relay
echo 3. 创建服务: message-relay
echo 4. 上传 cloudrun-deploy.zip
echo 5. 设置端口: 80
echo 6. 设置环境变量: LOCAL_SERVER_URL = http://s98a2526.natappfree.cc
echo.
pause
