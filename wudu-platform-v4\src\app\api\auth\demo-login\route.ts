import { NextRequest, NextResponse } from 'next/server';
import { userService } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action !== 'demo_login') {
      return NextResponse.json({
        success: false,
        message: '无效的演示登录请求'
      }, { status: 400 });
    }

    console.log('🎭 开始演示登录流程');

    // 查找数据库中的现有用户（使用最新的用户记录）
    const existingUser = await userService.getUserByUnionId('xiPiS42tE96iiHV8uRZiicezAAiEiE');
    
    if (existingUser) {
      console.log('✅ 找到现有用户，更新最后登录时间');
      
      // 更新用户的最后登录时间（通过更新updatedAt字段）
      const updatedUser = await userService.upsertUser({
        unionId: existingUser.unionId,
        openId: existingUser.openId,
        userId: existingUser.userId,
        name: existingUser.name,
        nick: existingUser.nick,
        avatar: existingUser.avatar,
        mobile: existingUser.mobile,
        email: existingUser.email,
        stateCode: existingUser.stateCode,
        jobNumber: existingUser.jobNumber,
        title: existingUser.title,
        workPlace: existingUser.workPlace,
        hiredDate: existingUser.hiredDate,
        remark: existingUser.remark,
        active: existingUser.active,
        admin: existingUser.admin,
        boss: existingUser.boss,
        senior: existingUser.senior,
        realAuthed: existingUser.realAuthed
      });

      // 构建返回的用户信息
      const userInfo = {
        id: updatedUser.id,
        unionId: updatedUser.unionId,
        openId: updatedUser.openId,
        userId: updatedUser.userId,
        name: updatedUser.name,
        nick: updatedUser.nick,
        avatar: updatedUser.avatar,
        mobile: updatedUser.mobile,
        email: updatedUser.email,
        title: updatedUser.title,
        active: updatedUser.active,
        admin: updatedUser.admin,
        departments: updatedUser.departments.map(ud => ({
          deptId: ud.department.deptId,
          name: ud.department.name,
          parentId: ud.department.parentId,
          order: ud.department.order
        })),
        roles: updatedUser.roles.map(ur => ({
          roleId: ur.role.roleId,
          roleName: ur.role.roleName,
          groupName: ur.role.groupName
        })),
        dingTalkInfo: {
          unionId: updatedUser.unionId,
          openId: updatedUser.openId,
          userId: updatedUser.userId,
          name: updatedUser.name,
          mobile: updatedUser.mobile,
          title: updatedUser.title,
          active: updatedUser.active,
          admin: updatedUser.admin,
          departments: updatedUser.departments.map(ud => ({
            deptId: ud.department.deptId,
            name: ud.department.name
          })),
          roles: updatedUser.roles.map(ur => ur.role.roleName)
        }
      };

      // 创建新的会话token
      const sessionToken = `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期
      
      try {
        await userService.createUserSession(updatedUser.id, sessionToken, expiresAt);
      } catch (sessionError) {
        console.warn('创建会话失败:', sessionError);
      }

      console.log('🎉 演示登录成功，用户信息已更新');

      return NextResponse.json({
        success: true,
        message: '演示登录成功',
        user: userInfo,
        token: sessionToken,
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('⚠️ 未找到现有用户，返回模拟数据');
      
      return NextResponse.json({
        success: false,
        message: '未找到用户数据，使用模拟登录',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error: any) {
    console.error('❌ 演示登录失败:', error);
    
    return NextResponse.json({
      success: false,
      error: 'DEMO_LOGIN_FAILED',
      message: error.message || '演示登录失败',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 健康检查
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: '演示登录API服务正常',
    timestamp: new Date().toISOString()
  });
}
