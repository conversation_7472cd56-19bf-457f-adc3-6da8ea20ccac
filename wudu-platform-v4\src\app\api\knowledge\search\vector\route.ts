/**
 * 向量搜索API接口
 * 提供语义搜索功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { vectorSearchService } from '@/lib/ai/vector-search';
import { auth } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      query, 
      searchType = 'vector',
      similarityThreshold = 0.7,
      maxResults = 10,
      includeContent = false
    } = body;

    // 验证必需参数
    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: '查询参数不能为空' },
        { status: 400 }
      );
    }

    if (query.trim().length < 2) {
      return NextResponse.json(
        { error: '查询内容至少需要2个字符' },
        { status: 400 }
      );
    }

    // 配置搜索参数
    const searchConfig = {
      similarityThreshold: Math.max(0, Math.min(1, similarityThreshold)),
      maxResults: Math.max(1, Math.min(50, maxResults)),
      includeContent
    };

    console.log(`🔍 开始向量搜索: "${query}", 类型: ${searchType}`);

    let searchResult;

    // 根据搜索类型执行不同的搜索
    switch (searchType) {
      case 'vector':
        searchResult = await vectorSearchService.vectorSearch(query, searchConfig);
        break;
      case 'hybrid':
        searchResult = await vectorSearchService.hybridSearch(query, searchConfig);
        break;
      default:
        return NextResponse.json(
          { error: '不支持的搜索类型' },
          { status: 400 }
        );
    }

    // 记录搜索日志
    console.log(`✅ 向量搜索完成: 找到 ${searchResult.results.length} 个结果，耗时 ${searchResult.searchTime}ms`);

    return NextResponse.json({
      success: true,
      data: searchResult,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ 向量搜索API错误:', error);
    
    return NextResponse.json(
      { 
        error: '搜索服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const searchType = searchParams.get('type') || 'vector';
    const similarityThreshold = parseFloat(searchParams.get('threshold') || '0.7');
    const maxResults = parseInt(searchParams.get('limit') || '10');
    const includeContent = searchParams.get('content') === 'true';

    if (!query) {
      return NextResponse.json(
        { error: '缺少查询参数 q' },
        { status: 400 }
      );
    }

    // 配置搜索参数
    const searchConfig = {
      similarityThreshold: Math.max(0, Math.min(1, similarityThreshold)),
      maxResults: Math.max(1, Math.min(50, maxResults)),
      includeContent
    };

    console.log(`🔍 GET向量搜索: "${query}", 类型: ${searchType}`);

    let searchResult;

    // 根据搜索类型执行搜索
    switch (searchType) {
      case 'vector':
        searchResult = await vectorSearchService.vectorSearch(query, searchConfig);
        break;
      case 'hybrid':
        searchResult = await vectorSearchService.hybridSearch(query, searchConfig);
        break;
      default:
        return NextResponse.json(
          { error: '不支持的搜索类型' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: searchResult,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ 向量搜索GET API错误:', error);
    
    return NextResponse.json(
      { 
        error: '搜索服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
