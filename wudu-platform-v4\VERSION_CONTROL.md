# 🔄 版本控制和回滚指南

## 📊 当前版本状态

### 🏷️ 版本标签
- **v2.0-pre-stable** - 知识库管理系统完成，向量搜索开发前的稳定版本
- **当前分支** - `feature/vector-search-system` (开发分支)
- **主分支** - `master` (稳定版本)

### 📈 版本历史
```
v2.0-pre-stable (2025-01-28)
├── 知识库管理系统完成
├── AI聊天界面优化  
├── 数据库模型完善
├── API接口体系完整
└── 所有基础功能测试通过

v1.1 (之前版本)
├── 钉钉登录系统
├── 用户管理系统
├── 基础界面框架
└── 数据库基础结构
```

## 🔒 稳定版本回滚

### 快速回滚到稳定版本
```bash
# 1. 保存当前工作（如果需要）
git stash push -m "保存当前开发进度"

# 2. 切换到稳定版本
git checkout v2.0-pre-stable

# 3. 创建新的工作分支（可选）
git checkout -b hotfix/emergency-fix

# 4. 或者直接回到主分支
git checkout master
```

### 完全重置到稳定版本
```bash
# ⚠️ 警告：这会丢失所有未提交的更改
git reset --hard v2.0-pre-stable
```

### 恢复保存的工作
```bash
# 查看保存的工作
git stash list

# 恢复最近保存的工作
git stash pop

# 恢复特定的保存
git stash apply stash@{0}
```

## 🚀 开发工作流

### 新功能开发流程
```bash
# 1. 从稳定版本创建功能分支
git checkout master
git pull origin master
git checkout -b feature/new-feature-name

# 2. 开发和提交
git add .
git commit -m "feat: 新功能描述"

# 3. 定期推送到远程
git push -u origin feature/new-feature-name

# 4. 完成后合并到主分支
git checkout master
git merge feature/new-feature-name
git tag -a v2.1 -m "版本描述"
```

### 紧急修复流程
```bash
# 1. 从稳定版本创建修复分支
git checkout v2.0-pre-stable
git checkout -b hotfix/critical-fix

# 2. 修复问题并测试
git add .
git commit -m "fix: 紧急修复描述"

# 3. 合并到主分支
git checkout master
git merge hotfix/critical-fix
git tag -a v2.0.1 -m "紧急修复版本"
```

## 📋 版本检查清单

### 创建新版本前的检查
- [ ] 所有功能测试通过
- [ ] 数据库迁移脚本完整
- [ ] 环境变量配置正确
- [ ] 依赖包版本锁定
- [ ] 文档更新完成
- [ ] 性能测试通过

### 回滚前的检查
- [ ] 确认回滚原因
- [ ] 备份当前数据库
- [ ] 通知相关人员
- [ ] 准备回滚后的修复计划

## 🛠️ 常用Git命令

### 查看版本信息
```bash
# 查看所有标签
git tag -l

# 查看分支
git branch -a

# 查看提交历史
git log --oneline -10

# 查看当前状态
git status
```

### 分支管理
```bash
# 创建并切换分支
git checkout -b branch-name

# 切换分支
git checkout branch-name

# 删除分支
git branch -d branch-name

# 强制删除分支
git branch -D branch-name
```

### 标签管理
```bash
# 创建标签
git tag -a v1.0 -m "版本描述"

# 推送标签
git push origin v1.0

# 删除标签
git tag -d v1.0
git push origin :refs/tags/v1.0
```

## 🔧 故障恢复

### 数据库回滚
```bash
# 1. 备份当前数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 重置数据库结构
npx prisma migrate reset --force

# 3. 应用稳定版本的迁移
npx prisma migrate deploy

# 4. 重新生成客户端
npx prisma generate
```

### 依赖包回滚
```bash
# 1. 删除当前依赖
rm -rf node_modules package-lock.json

# 2. 从稳定版本恢复package.json
git checkout v2.0-pre-stable -- package.json

# 3. 重新安装依赖
npm install
```

## 📞 紧急联系

### 回滚失败时的处理
1. **立即停止操作** - 避免进一步损坏
2. **记录错误信息** - 保存所有错误日志
3. **联系技术负责人** - 寻求专业帮助
4. **准备从备份恢复** - 最后的保险措施

### 数据恢复联系方式
- **数据库管理员**: [待定]
- **系统管理员**: [待定]
- **项目负责人**: [待定]

---

*版本控制指南 v1.0*  
*创建时间: 2025-01-28*  
*适用版本: v2.0-pre-stable 及以上*
