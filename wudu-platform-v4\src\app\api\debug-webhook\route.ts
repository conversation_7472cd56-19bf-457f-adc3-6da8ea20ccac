import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('[Debug Webhook] ========== GET请求 ==========');
  console.log('[Debug Webhook] URL:', request.url);
  console.log('[Debug Webhook] Headers:', Object.fromEntries(request.headers.entries()));
  
  return new NextResponse('Debug GET OK', { status: 200 });
}

export async function POST(request: NextRequest) {
  console.log('[Debug Webhook] ========== POST请求 ==========');
  console.log('[Debug Webhook] URL:', request.url);
  console.log('[Debug Webhook] Headers:', Object.fromEntries(request.headers.entries()));
  
  try {
    const body = await request.text();
    console.log('[Debug Webhook] Body length:', body.length);
    console.log('[Debug Webhook] Body:', body);
  } catch (error) {
    console.log('[Debug Webhook] Body read error:', error);
  }
  
  return new NextResponse('Debug POST OK', { status: 200 });
}

export async function PUT(request: NextRequest) {
  console.log('[Debug Webhook] ========== PUT请求 ==========');
  console.log('[Debug Webhook] URL:', request.url);
  return new NextResponse('Debug PUT OK', { status: 200 });
}

export async function DELETE(request: NextRequest) {
  console.log('[Debug Webhook] ========== DELETE请求 ==========');
  console.log('[Debug Webhook] URL:', request.url);
  return new NextResponse('Debug DELETE OK', { status: 200 });
}

export async function PATCH(request: NextRequest) {
  console.log('[Debug Webhook] ========== PATCH请求 ==========');
  console.log('[Debug Webhook] URL:', request.url);
  return new NextResponse('Debug PATCH OK', { status: 200 });
}
