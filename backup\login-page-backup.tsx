'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';

// 声明钉钉SDK全局函数
declare global {
  interface Window {
    DTFrameLogin: any;
  }
}

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const [qrStatus, setQrStatus] = useState<'loading' | 'active'>('loading');
  const [showAlert, setShowAlert] = useState(false);
  const { login, loginDemo } = useAuth();
  const router = useRouter();

  // 使用 useRef 来避免React DOM冲突
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitializedRef = useRef(false);

  useEffect(() => {
    // 防止重复初始化
    if (isInitializedRef.current) {
      return;
    }

    // 添加全局错误处理
    const handleError = (event: ErrorEvent) => {
      console.error('全局错误捕获:', event.error);
      console.error('错误堆栈:', event.error?.stack);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('未处理的Promise拒绝:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // 动态加载钉钉SDK
    const loadDingtalkSDK = () => {
      const script = document.createElement('script');
      script.src = 'https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js';
      script.onload = () => {
        console.log('钉钉SDK加载成功');
        // 使用 requestAnimationFrame 确保DOM更新完成
        requestAnimationFrame(() => {
          setTimeout(() => {
            try {
              initDingtalkLogin();
            } catch (error) {
              console.error('钉钉初始化错误:', error);
            }
          }, 100);
        });
      };
      script.onerror = (error) => {
        console.error('钉钉SDK加载失败:', error);
      };

      // 检查是否已经存在script
      const existingScript = document.querySelector('script[src="https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js"]');
      if (!existingScript) {
        document.head.appendChild(script);
      } else {
        // 如果已存在，直接初始化
        requestAnimationFrame(() => {
          setTimeout(() => {
            try {
              initDingtalkLogin();
            } catch (error) {
              console.error('钉钉初始化错误:', error);
            }
          }, 100);
        });
      }
    };

    // 确保DOM完全加载后再初始化
    if (document.readyState === 'complete') {
      loadDingtalkSDK();
    } else {
      window.addEventListener('load', loadDingtalkSDK);
    }

    isInitializedRef.current = true;

    // 清理函数
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('load', loadDingtalkSDK);

      // 清理钉钉容器，避免DOM冲突
      if (containerRef.current) {
        try {
          containerRef.current.innerHTML = '';
        } catch (error) {
          console.warn('清理容器时出错:', error);
        }
      }
    };
  }, []);

  const initDingtalkLogin = () => {
    console.log('🔄 开始初始化钉钉登录...');

    // 检查容器引用
    if (!containerRef.current) {
      console.error('❌ 容器引用不存在');
      return;
    }

    // 检查钉钉SDK是否已加载
    if (!window.DTFrameLogin) {
      console.log('❌ 钉钉SDK未加载');
      return;
    }

    try {
      // 清理容器，避免重复初始化
      const container = containerRef.current;
      container.innerHTML = '';

      // 创建一个独立的div来隔离钉钉SDK
      const dingtalkDiv = document.createElement('div');
      dingtalkDiv.id = 'dingtalk_login_container';
      dingtalkDiv.style.width = '300px';
      dingtalkDiv.style.height = '300px';
      dingtalkDiv.style.margin = '0 auto';
      dingtalkDiv.style.border = '2px dashed #e5e7eb';
      dingtalkDiv.style.borderRadius = '12px';
      dingtalkDiv.style.backgroundColor = '#f9fafb';
      dingtalkDiv.style.display = 'flex';
      dingtalkDiv.style.alignItems = 'center';
      dingtalkDiv.style.justifyContent = 'center';

      // 将钉钉容器添加到我们的ref容器中
      container.appendChild(dingtalkDiv);

      console.log('📝 DTFrameLogin配置 - 使用隔离容器');

      window.DTFrameLogin(
        {
          id: 'dingtalk_login_container',
          width: 300,
          height: 300,
        },
        {
          redirect_uri: 'http://219.138.230.35:30002/dingtalk-callback',
          client_id: 'dinggai5cng27n76jvbq',
          scope: 'openid',
          response_type: 'code',
          state: 'dingtalk_login_' + Date.now(),
          prompt: 'consent',
        },
        (loginResult: any) => {
          try {
            console.log('✅ 钉钉登录回调:', loginResult);

            // 简化处理，避免复杂的异步操作
            if (loginResult && loginResult.authCode) {
              console.log('🎉 获取到授权码，模拟登录成功');
              setShowAlert(true);
              setTimeout(() => {
                router.push('/dashboard');
              }, 1500);
            } else {
              console.warn('⚠️ 未获取到有效的授权码');
            }
          } catch (error) {
            console.error('❌ 登录回调处理错误:', error);
          }
        },
        // 登录失败回调
        (errorMsg: string) => {
          console.error('❌ 钉钉登录失败:', errorMsg);
          // 不设置错误状态，保持二维码可用
        }
      );

      console.log('✅ 钉钉SDK初始化成功');
      setQrStatus('active');

    } catch (error) {
      console.error('❌ 钉钉SDK初始化失败:', error);
      console.error('错误详情:', error);
      setQrStatus('active');
    }
  };

  // 方案2: iframe刷新方案
  const refreshQRCodeByIframe = () => {
    console.log('🔄 通过iframe刷新二维码...');

    // 查找钉钉登录的iframe
    const container = document.getElementById('dingtalk_login_container');
    if (container) {
      const iframe = container.querySelector('iframe');
      if (iframe) {
        const oldSrc = iframe.src;
        console.log('📝 刷新前iframe src:', oldSrc);

        // 刷新iframe - 添加时间戳确保真正刷新
        const newSrc = oldSrc.includes('?')
          ? `${oldSrc}&_refresh=${Date.now()}`
          : `${oldSrc}?_refresh=${Date.now()}`;

        iframe.src = newSrc;
        setQrStatus('loading');

        console.log('📝 刷新后iframe src:', newSrc);

        // 监听iframe加载完成
        iframe.onload = () => {
          console.log('✅ 二维码刷新成功 - iframe重新加载完成');
          console.log('📝 最终iframe src:', iframe.src);
          setQrStatus('active');
        };
      } else {
        console.warn('⚠️ 未找到iframe元素');
      }
    } else {
      console.warn('⚠️ 未找到钉钉登录容器');
    }
  };

  // 方案3: 重新初始化方案（谨慎使用）
  const refreshQRCodeByReinit = () => {
    console.log('🔄 通过重新初始化刷新二维码...');

    const container = document.getElementById('dingtalk_login_container');
    if (container) {
      try {
        // 记录刷新前的状态
        const oldContent = container.innerHTML;
        console.log('📝 刷新前容器内容长度:', oldContent.length);

        setQrStatus('loading');

        // 安全地清空容器内容
        while (container.firstChild) {
          container.removeChild(container.firstChild);
        }
        console.log('🗑️ 已安全清空容器内容');

        // 延迟重新初始化，避免DOM冲突
        setTimeout(() => {
          console.log('🔄 开始重新初始化钉钉登录...');
          try {
            initDingtalkLogin();

            // 验证新内容
            setTimeout(() => {
              const newContent = container.innerHTML;
              console.log('📝 刷新后容器内容长度:', newContent.length);
              console.log('✅ 内容是否发生变化:', oldContent !== newContent);
            }, 1000);
          } catch (error) {
            console.error('重新初始化钉钉登录失败:', error);
            setQrStatus('active'); // 恢复状态
          }
        }, 100);
      } catch (error) {
        console.error('清空容器时出错:', error);
        setQrStatus('active'); // 恢复状态
      }
    } else {
      console.warn('⚠️ 未找到钉钉登录容器');
    }
  };

  // 方案4: 页面刷新方案（最简单）
  const refreshQRCodeByPageReload = () => {
    console.log('🔄 通过页面刷新重新加载...');
    window.location.reload();
  };

  // 验证二维码是否真的更新了
  const verifyQRCodeUpdate = () => {
    console.log('🔍 开始验证二维码更新...');

    const container = document.getElementById('dingtalk_login_container');
    if (container) {
      const iframe = container.querySelector('iframe');
      if (iframe) {
        console.log('📝 当前iframe信息:');
        console.log('  - src:', iframe.src);
        console.log('  - 加载时间:', new Date().toLocaleTimeString());

        // 尝试检查iframe内的内容（受跨域限制）
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            console.log('  - iframe内容可访问');
            const qrImages = iframeDoc.querySelectorAll('img, canvas');
            console.log('  - 二维码图片/画布数量:', qrImages.length);
          } else {
            console.log('  - iframe内容受跨域限制，无法直接访问');
          }
        } catch (error) {
          console.log('  - iframe内容受跨域限制:', error instanceof Error ? error.message : String(error));
        }

        // 检查网络请求（通过时间戳判断）
        const urlParams = new URL(iframe.src).searchParams;
        const refreshParam = urlParams.get('_refresh');
        if (refreshParam) {
          const refreshTime = new Date(parseInt(refreshParam));
          console.log('  - 刷新时间戳:', refreshTime.toLocaleTimeString());
          console.log('  - 距离现在:', Date.now() - parseInt(refreshParam), 'ms');
        }
      } else {
        console.log('❌ 未找到iframe元素');
      }
    } else {
      console.log('❌ 未找到钉钉登录容器');
    }
  };

  // 智能刷新方案：优先使用真正有效的方法
  const smartRefreshQRCode = () => {
    console.log('🧠 智能刷新二维码...');
    console.log('⚠️ 注意：iframe刷新不会生成新的state参数，无法获取真正的新二维码');
    console.log('✅ 使用重新初始化方案，确保生成全新的二维码');

    try {
      // 直接使用重新初始化方案（唯一真正有效的方法）
      refreshQRCodeByReinit();
    } catch (error) {
      console.warn('重新初始化失败，尝试页面刷新...', error);
      try {
        // 备选方案：页面刷新
        refreshQRCodeByPageReload();
      } catch (error2) {
        console.error('所有刷新方案都失败了', error2);
      }
    }
  };





  const handleDemoLogin = () => {
    setShowAlert(true);
    loginDemo();
    setTimeout(() => {
      router.push('/dashboard');
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      {/* 原始的单栏登录界面 */}
      <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-md">
        {/* 系统标题 */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            吴都乔街景区
          </h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">智能客服中控平台</h2>
          <p className="text-gray-500">使用钉钉账号安全登录管理系统</p>
        </div>

        {/* 钉钉登录区域 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">钉钉扫码登录</h3>

          {/* 钉钉二维码容器 - 使用ref避免React DOM冲突 */}
          <div className="flex justify-center mb-4">
            <div
              ref={containerRef}
              className="w-[300px] h-[300px] border-2 border-dashed border-gray-200 rounded-xl bg-gray-50 flex items-center justify-center"
            >
              {qrStatus === 'loading' && (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
                  <p className="text-gray-600 text-sm">正在加载二维码...</p>
                </div>
              )}
            </div>
          </div>

          {/* 状态提示 */}
          <div className="text-center">
            {qrStatus === 'active' && (
              <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                请使用钉钉APP扫描二维码
              </div>
            )}
            {loading && (
              <div className="flex items-center justify-center gap-2 text-sm text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                正在处理登录...
              </div>
            )}
          </div>
        </div>

        {/* 分割线 */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">或</span>
          </div>
        </div>

        {/* 开发测试区域 */}
        <div className="space-y-4">
          <Button
            onClick={handleDemoLogin}
            className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium py-3"
          >
            演示登录 (开发测试)
          </Button>
        </div>

        {/* 安全提示 */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-400">
            🔒 安全登录 · 数据加密传输 · 符合企业安全标准
          </p>
        </div>
      </div>

      {/* 全局提示 */}
      {showAlert && (
        <div className="fixed top-6 right-6 bg-white border border-green-200 text-green-700 px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm">
          <div className="flex items-center">
            <span className="mr-3 text-lg">✅</span>
            <span className="font-medium">登录成功！正在跳转...</span>
          </div>
        </div>
      )}
    </div>
  );
}
