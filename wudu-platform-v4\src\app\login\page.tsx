'use client';

import { But<PERSON> } from '@/components/ui/button';
import { DingtalkLogin } from '@/components/features/auth/dingtalk-login';
import { useAuth } from '@/lib/auth';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function LoginPage() {
  const [showAlert, setShowAlert] = useState(false);
  const { loginDemo } = useAuth();
  const router = useRouter();

  // 演示登录处理函数
  const handleDemoLogin = () => {
    setShowAlert(true);
    loginDemo();
    setTimeout(() => {
      window.location.href = 'http://219.138.230.35:30002/dashboard';
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      <div className="w-full max-w-4xl mx-auto grid lg:grid-cols-2 gap-8 items-center">
        {/* 左侧：品牌信息 */}
        <div className="text-center lg:text-left space-y-6">
          <div className="flex items-center justify-center lg:justify-start space-x-3">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-800">吴都乔街景区</div>
              <h2 className="text-3xl font-bold text-gray-900">智能客服中控平台</h2>
            </div>
          </div>
          <p className="text-lg text-gray-600">使用钉钉账号安全登录管理系统</p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>✨ 基于 Shadcn-Admin 架构</p>
            <p>🔧 Next.js 15 + TypeScript</p>
            <p>🎨 现代化 UI/UX 设计</p>
          </div>
        </div>

        {/* 右侧：登录表单 */}
        <div className="space-y-6">
          {/* 钉钉登录组件 */}
          <DingtalkLogin />

          {/* 分隔线 */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">或</span>
            </div>
          </div>

          {/* 演示登录按钮 */}
          <Button 
            onClick={handleDemoLogin}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            演示登录 (开发测试)
          </Button>

          {/* 安全提示 */}
          <p className="text-center text-sm text-gray-500">
            🔒 安全登录 · 数据加密传输 · 符合企业安全标准
          </p>
        </div>

        {/* 登录成功提示 */}
        {showAlert && (
          <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg z-50">
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <span className="text-green-800">登录成功！正在跳转...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
