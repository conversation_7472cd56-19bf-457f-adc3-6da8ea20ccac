/**
 * Webhook统一消息接口定义
 * 用于多平台消息的标准化处理
 */

export interface UnifiedMessage {
  /** 消息唯一标识 */
  id: string;
  /** 平台类型 */
  platform: 'wechat' | 'douyin' | 'telegram';
  /** 用户ID */
  userId: string;
  /** 消息内容 */
  content: string;
  /** 消息类型 */
  messageType: 'text' | 'image' | 'voice' | 'video';
  /** 会话ID */
  sessionId: string;
  /** 时间戳 */
  timestamp: number;
  /** 平台特定的元数据 */
  metadata: {
    /** 原始消息数据 */
    original: Record<string, unknown>;
    /** 聊天ID（微信公众号） */
    chatId?: string;
    /** 群组ID（微信群） */
    groupId?: string;
    /** 视频ID（抖音评论场景） */
    videoId?: string;
    /** 评论ID（抖音） */
    commentId?: string;
    /** 是否@机器人（群聊场景） */
    isAtBot?: boolean;
  };
}

export interface WebhookResponse {
  /** 处理是否成功 */
  success: boolean;
  /** AI回复内容 */
  reply?: string;
  /** 错误信息 */
  error?: string;
  /** 响应元数据 */
  metadata?: {
    /** 处理时间（毫秒） */
    processingTime?: number;
    /** 置信度 */
    confidence?: number;
    /** 来源信息 */
    sources?: string[];
  };
}

export interface PlatformConfig {
  /** 平台名称 */
  platform: string;
  /** 是否启用 */
  enabled: boolean;
  /** 平台特定配置 */
  config: {
    /** API密钥 */
    apiKey?: string;
    /** App ID */
    appId?: string;
    /** App Secret */
    appSecret?: string;
    /** Webhook密钥 */
    webhookSecret?: string;
    /** 访问令牌 */
    accessToken?: string;
    /** 其他配置 */
    [key: string]: unknown;
  };
}

export interface WebhookMessageRecord {
  /** 消息ID */
  id: string;
  /** 平台类型 */
  platform: string;
  /** 用户ID */
  userId: string;
  /** 消息内容 */
  content: string;
  /** 消息类型 */
  messageType: string;
  /** 会话ID */
  sessionId: string;
  /** AI回复 */
  reply?: string;
  /** 处理状态 */
  status: 'pending' | 'processed' | 'failed';
  /** 元数据 */
  metadata?: Record<string, unknown>;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 平台集成配置接口
 */
export interface PlatformIntegration {
  /** 平台ID */
  id: string;
  /** 平台名称 */
  name: string;
  /** 平台类型 */
  type: 'wechat' | 'dingtalk' | 'telegram' | 'douyin';
  /** 平台图标 */
  icon: string;
  /** 是否启用 */
  enabled: boolean;
  /** 连接状态 */
  status: 'connected' | 'disconnected' | 'error' | 'testing';
  /** 配置参数 */
  config: {
    /** 应用ID */
    appId?: string;
    /** 应用密钥 */
    appSecret?: string;
    /** Webhook密钥 */
    webhookSecret?: string;
    /** 访问令牌 */
    accessToken?: string;
    /** Webhook URL */
    webhookUrl?: string;
    /** 其他平台特定配置 */
    [key: string]: unknown;
  };
  /** 统计信息 */
  stats?: {
    /** 今日消息数 */
    todayMessages: number;
    /** 总消息数 */
    totalMessages: number;
    /** 平均响应时间 */
    avgResponseTime: number;
    /** 最后活动时间 */
    lastActivity?: Date;
  };
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 平台配置表单数据
 */
export interface PlatformConfigForm {
  /** 微信公众号配置 */
  wechat: {
    appId: string;
    appSecret: string;
    token: string;
    encodingAESKey: string;
    enabled: boolean;
  };
  /** 钉钉配置 */
  dingtalk: {
    appKey: string;
    appSecret: string;
    agentId: string;
    enabled: boolean;
  };
  /** 抖音配置 */
  douyin: {
    appId: string;
    appSecret: string;
    webhookSecret: string;
    enabled: boolean;
  };
  /** Telegram配置 */
  telegram: {
    botToken: string;
    webhookSecret: string;
    enabled: boolean;
  };
}
