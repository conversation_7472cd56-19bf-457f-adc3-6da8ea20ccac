# 🔧 v1.1 关键代码文件备份

## 📋 备份说明
本文档包含项目的所有关键代码文件，用于快速恢复和参考。

---

## 📦 package.json
```json
{
  "name": "wudu-platform-v4",
  "version": "1.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio"
  },
  "dependencies": {
    "@prisma/client": "^5.22.0",
    "@radix-ui/react-avatar": "^1.1.1",
    "@radix-ui/react-dropdown-menu": "^2.1.2",
    "@radix-ui/react-icons": "^1.3.2",
    "@radix-ui/react-slot": "^1.1.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "lucide-react": "^0.460.0",
    "next": "15.1.3",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "tailwind-merge": "^2.5.4",
    "tailwindcss-animate": "^1.0.7",
    "zustand": "^5.0.2"
  },
  "devDependencies": {
    "@types/node": "^22.10.2",
    "@types/react": "^19.0.1",
    "@types/react-dom": "^19.0.1",
    "eslint": "^9.17.0",
    "eslint-config-next": "15.1.3",
    "postcss": "^8.5.1",
    "prisma": "^5.22.0",
    "tailwindcss": "^3.4.17",
    "typescript": "^5.7.2"
  }
}
```

---

## 🗄️ prisma/schema.prisma
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 钉钉用户信息
  unionId     String   @unique
  openId      String?
  userId      String?
  name        String
  nick        String?
  avatar      String?
  mobile      String?
  email       String?
  stateCode   String?
  jobNumber   String?
  title       String?
  workPlace   String?
  hiredDate   String?
  remark      String?
  
  // 用户状态
  active      Boolean  @default(true)
  admin       Boolean  @default(false)
  boss        Boolean  @default(false)
  senior      Boolean  @default(false)
  realAuthed  Boolean  @default(false)
  
  // 关联关系
  departments UserDepartment[]
  roles       UserRole[]
  sessions    UserSession[]

  @@map("users")
}

model Department {
  id        String   @id @default(cuid())
  deptId    String   @unique
  name      String
  parentId  String?
  order     BigInt
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  users     UserDepartment[]
  @@map("departments")
}

model Role {
  id        String   @id @default(cuid())
  roleId    String   @unique
  roleName  String
  groupName String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  users     UserRole[]
  @@map("roles")
}

model UserDepartment {
  id     String @id @default(cuid())
  userId String
  deptId String
  
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  department Department @relation(fields: [deptId], references: [deptId], onDelete: Cascade)

  @@unique([userId, deptId])
  @@map("user_departments")
}

model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [roleId], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  @@map("user_sessions")
}
```

---

## ⚙️ next.config.ts
```typescript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverActions: {
      allowedOrigins: ["localhost:3000", "**************:30002"]
    }
  },
  images: {
    domains: ['static-legacy.dingtalk.com']
  }
}

module.exports = nextConfig
```

---

## 🎨 tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

---

## 📝 tsconfig.json
```json
{
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

---

## 🔧 components.json
```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/app/globals.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
```

---

## 🌐 环境变量模板 (.env.local)
```bash
# 数据库连接
DATABASE_URL="mysql://username:password@localhost:3306/wudu_platform"

# 钉钉应用配置
DINGTALK_APP_KEY="your_app_key"
DINGTALK_APP_SECRET="your_app_secret"
DINGTALK_CORP_ID="your_corp_id"

# AI服务配置 (为下一阶段准备)
DEEPSEEK_API_KEY="your_deepseek_api_key"
OPENAI_API_KEY="your_openai_api_key"

# 系统配置
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"

# 告警配置 (为下一阶段准备)
DINGTALK_ALERT_WEBHOOK="your_dingtalk_webhook"

# 加密密钥 (为下一阶段准备)
ENCRYPTION_KEY="your_encryption_key"
```

---

## 📁 项目结构快照
```
wudu-platform-v4/
├── src/
│   ├── app/
│   │   ├── api/auth/dingtalk/official-login/route.ts
│   │   ├── api/auth/demo-login/route.ts
│   │   ├── dashboard/page.tsx
│   │   ├── login/page.tsx
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── globals.css
│   ├── components/
│   │   ├── ui/ (Shadcn组件)
│   │   └── layout/ (布局组件)
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   └── utils.ts
│   └── types/
├── prisma/
│   └── schema.prisma
├── docs/ (8个文档文件)
├── versions/ (版本备份)
├── public/ (静态资源)
├── package.json
├── next.config.ts
├── tailwind.config.js
├── tsconfig.json
└── components.json
```

---

## 🚀 快速恢复指令

### 1. 环境准备
```bash
# 安装Node.js 18+
# 安装MySQL 8.0
# 克隆或创建项目目录
```

### 2. 项目初始化
```bash
# 创建项目
npx create-next-app@latest wudu-platform-v4 --typescript --tailwind --eslint --app

# 进入项目目录
cd wudu-platform-v4

# 安装依赖
npm install @prisma/client @radix-ui/react-avatar @radix-ui/react-dropdown-menu @radix-ui/react-icons @radix-ui/react-slot class-variance-authority clsx lucide-react tailwind-merge tailwindcss-animate zustand

# 安装开发依赖
npm install -D prisma
```

### 3. 配置文件恢复
```bash
# 复制所有配置文件内容到对应位置
# package.json
# next.config.ts
# tailwind.config.js
# tsconfig.json
# components.json
# prisma/schema.prisma
```

### 4. 数据库设置
```bash
# 生成Prisma客户端
npx prisma generate

# 推送数据库模式
npx prisma db push

# 查看数据库
npx prisma studio
```

### 5. 启动项目
```bash
# 开发模式
npm run dev

# 访问 http://localhost:3000
```

---

## ✅ 恢复验证清单

### 基础环境 ✅
- [ ] Node.js 18+ 已安装
- [ ] MySQL 8.0 已安装并运行
- [ ] 项目目录已创建

### 依赖安装 ✅
- [ ] 所有npm包已安装
- [ ] Prisma客户端已生成
- [ ] 数据库连接正常

### 配置文件 ✅
- [ ] package.json 已恢复
- [ ] next.config.ts 已恢复
- [ ] tailwind.config.js 已恢复
- [ ] tsconfig.json 已恢复
- [ ] prisma/schema.prisma 已恢复

### 功能验证 ✅
- [ ] 项目可以正常启动
- [ ] 页面可以正常访问
- [ ] 数据库连接正常
- [ ] 钉钉登录功能正常

---

## 🔐 src/lib/auth.ts (认证状态管理)
```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 完整的钉钉用户信息接口 - 基于成功实现
interface DingTalkUser {
  // 基本标识
  unionId: string;          // 钉钉全局唯一用户ID
  openId: string;           // 开放平台用户ID
  userId: string;           // 企业内用户ID

  // 基本信息
  name: string;             // 用户姓名
  nick?: string;            // 用户昵称
  avatarUrl?: string;       // 头像URL
  mobile: string;           // 手机号
  email?: string;           // 邮箱
  stateCode?: string;       // 国家码

  // 职业信息
  jobNumber?: string;       // 工号
  title?: string;           // 职位
  workPlace?: string;       // 工作地点
  hiredDate?: string;       // 入职日期
  remark?: string;          // 备注

  // 状态信息
  active: boolean;          // 是否在职
  admin: boolean;           // 是否管理员
  boss: boolean;            // 是否老板
  senior?: boolean;         // 是否高管
  realAuthed?: boolean;     // 是否实名认证

  // 组织信息
  departments: Department[];
  roles: Role[];
  leaderInDepts?: LeaderInfo[];
}

interface Department {
  deptId: number;           // 部门ID
  name: string;             // 部门名称
  parentId?: number;        // 父部门ID
  order?: number;           // 排序
}

interface Role {
  roleId: string;           // 角色ID
  roleName: string;         // 角色名称
  groupName?: string;       // 角色组名称
}

interface LeaderInfo {
  deptId: number;           // 部门ID
  leader: boolean;          // 是否为部门领导
}

// 兼容的用户接口
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
  // 扩展钉钉信息
  dingTalkInfo?: DingTalkUser;
}

interface AuthState {
  user: User | null;
  token: string | null;
  login: (authCode: string, state: string) => Promise<boolean>;
  loginDemo: () => void; // 临时演示登录
  loginFromStorage: () => boolean; // 从localStorage加载用户信息
  logout: () => void;
  isAuthenticated: () => boolean;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,

      login: async (authCode: string, state: string) => {
        try {
          console.log('🚀 开始钉钉登录，authCode:', authCode);

          // 使用 Next.js API Routes (Shadcn-Admin 架构)
          const response = await fetch('/api/auth/dingtalk/official-login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ authCode, state })
          });

          const data = await response.json();
          console.log('📝 后端响应:', data);

          if (data.success && data.user) {
            // 构建完整的钉钉用户信息
            const dingTalkInfo: DingTalkUser = {
              // 基本标识
              unionId: data.user.unionId || '',
              openId: data.user.openId || '',
              userId: data.user.userId || data.user.userid || '',

              // 基本信息
              name: data.user.name || '',
              nick: data.user.nick || '',
              avatarUrl: data.user.avatarUrl || data.user.avatar || '',
              mobile: data.user.mobile || '',
              email: data.user.email || '',
              stateCode: data.user.stateCode || '+86',

              // 职业信息
              jobNumber: data.user.jobNumber || '',
              title: data.user.title || '',
              workPlace: data.user.workPlace || '',
              hiredDate: data.user.hiredDate || '',
              remark: data.user.remark || '',

              // 状态信息
              active: data.user.active !== false, // 默认为true
              admin: data.user.admin === true,
              boss: data.user.boss === true,
              senior: data.user.senior === true,
              realAuthed: data.user.realAuthed === true,

              // 组织信息
              departments: Array.isArray(data.user.departments) ? data.user.departments.map((dept: any) => ({
                deptId: dept.deptId || dept.id,
                name: dept.name || '',
                parentId: dept.parentId,
                order: dept.order
              })) : [],

              roles: Array.isArray(data.user.roles) ? data.user.roles.map((role: any) => ({
                roleId: role.roleId || role.id || '',
                roleName: role.roleName || role.name || '',
                groupName: role.groupName || ''
              })) : [],

              leaderInDepts: Array.isArray(data.user.leaderInDepts) ? data.user.leaderInDepts : []
            };

            // 构建兼容的用户对象
            const user: User = {
              id: dingTalkInfo.userId || dingTalkInfo.unionId,
              name: dingTalkInfo.name,
              email: dingTalkInfo.email || '',
              avatar: dingTalkInfo.avatarUrl || '',
              roles: dingTalkInfo.roles.map(role => role.roleName).filter(Boolean),
              dingTalkInfo: dingTalkInfo
            };

            console.log('✅ 用户信息构建完成:', user);
            set({ user, token: data.token || 'dingtalk_' + Date.now() });
            return true;
          }

          console.error('❌ 登录失败:', data.message || '未知错误');
          return false;
        } catch (error) {
          console.error('❌ 登录异常:', error);
          return false;
        }
      },

      loginDemo: async () => {
        try {
          // 演示登录：更新现有用户的最后登录时间
          const response = await fetch('/api/auth/demo-login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'demo_login' })
          });

          const data = await response.json();

          if (data.success && data.user) {
            // 使用数据库中的真实用户信息
            const user: User = {
              id: data.user.id,
              name: data.user.name,
              email: data.user.email || '<EMAIL>',
              avatar: data.user.avatar || '',
              roles: data.user.roles || ['user'],
              dingTalkInfo: data.user.dingTalkInfo
            };
            set({ user, token: data.token });
          } else {
            // 降级到模拟用户
            const demoUser: User = {
              id: 'demo-user-1',
              name: '演示用户',
              email: '<EMAIL>',
              avatar: '',
              roles: ['admin']
            };
            set({ user: demoUser, token: 'demo-token-123' });
          }
        } catch (error) {
          console.error('演示登录失败:', error);
          // 降级到模拟用户
          const demoUser: User = {
            id: 'demo-user-1',
            name: '演示用户',
            email: '<EMAIL>',
            avatar: '',
            roles: ['admin']
          };
          set({ user: demoUser, token: 'demo-token-123' });
        }
      },

      loginFromStorage: () => {
        // 从localStorage加载钉钉登录的用户信息
        try {
          const userInfo = localStorage.getItem('userInfo');
          const authToken = localStorage.getItem('authToken');

          if (userInfo && authToken) {
            const userData = JSON.parse(userInfo);
            const user: User = {
              id: userData.userId || userData.unionId,
              name: userData.name,
              email: userData.email || '',
              avatar: userData.avatar || '',
              roles: userData.admin ? ['admin'] : ['user']
            };

            set({ user, token: authToken });
            return true;
          }
          return false;
        } catch (error) {
          console.error('从localStorage加载用户信息失败:', error);
          return false;
        }
      },

      logout: () => {
        set({ user: null, token: null });
      },

      isAuthenticated: () => {
        const { token } = get();
        return !!token;
      }
    }),
    {
      name: 'auth-storage'
    }
  )
);
```

---

*关键代码备份版本：v1.1*
*备份时间：2025-01-28*
*恢复难度：简单*
*预计恢复时间：30分钟*
