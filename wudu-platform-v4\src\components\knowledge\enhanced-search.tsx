/**
 * 增强搜索组件
 * 支持关键词搜索和向量搜索的切换
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, Brain, Filter, Loader2, Sparkles } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useDebounce } from '@/hooks/use-debounce';

// 搜索类型
type SearchType = 'keyword' | 'vector' | 'hybrid';

// 搜索结果接口
interface SearchResult {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  category?: string;
  tags: string[];
  similarity?: number;
  relevanceScore?: number;
  priority: number;
  useCount: number;
  createdAt: string;
  updatedAt: string;
}

// 搜索响应接口
interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  searchTime: number;
  query: string;
  searchType: SearchType;
  metadata?: {
    similarityThreshold?: number;
    vectorDimensions?: number;
    processingSteps?: string[];
  };
}

interface EnhancedSearchProps {
  onSearchResults: (results: SearchResult[], searchInfo: {
    query: string;
    searchType: SearchType;
    searchTime: number;
    totalCount: number;
  }) => void;
  onSearchModeChange: (mode: SearchType) => void;
  categories: Array<{ id: string; name: string }>;
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
  selectedStatus: string;
  onStatusChange: (status: string) => void;
}

export function EnhancedSearch({
  onSearchResults,
  onSearchModeChange,
  categories,
  selectedCategory,
  onCategoryChange,
  selectedStatus,
  onStatusChange
}: EnhancedSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<SearchType>('keyword');
  const [isSearching, setIsSearching] = useState(false);
  const [similarityThreshold, setSimilarityThreshold] = useState([0.3]);
  const [maxResults, setMaxResults] = useState([10]);
  const [includeContent, setIncludeContent] = useState(false);
  const [lastSearchInfo, setLastSearchInfo] = useState<{
    query: string;
    searchType: SearchType;
    searchTime: number;
    totalCount: number;
  } | null>(null);

  // 防抖搜索查询
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // 执行搜索
  const performSearch = useCallback(async (
    query: string,
    type: SearchType,
    threshold: number = 0.3,
    maxRes: number = 10
  ) => {
    if (!query.trim()) {
      // 如果查询为空，执行传统的列表加载
      await loadTraditionalResults();
      return;
    }

    // 对于向量搜索，如果查询太短，直接返回空结果
    if ((type === 'vector' || type === 'hybrid') && query.trim().length < 2) {
      onSearchResults([], {
        query,
        searchType: type,
        searchTime: 0,
        totalCount: 0
      });
      return;
    }

    setIsSearching(true);
    
    try {
      let response: Response;
      
      if (type === 'keyword') {
        // 传统关键词搜索
        const params = new URLSearchParams();
        params.append('search', query);
        if (selectedCategory) params.append('categoryId', selectedCategory);
        if (selectedStatus) params.append('status', selectedStatus);
        
        response = await fetch(`/api/knowledge/items?${params}`);
      } else {
        // 向量搜索或混合搜索
        response = await fetch('/api/knowledge/search/vector', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            searchType: type,
            similarityThreshold: threshold,
            maxResults: maxRes,
            includeContent
          }),
        });
      }

      if (!response.ok) {
        throw new Error(`搜索失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (type === 'keyword') {
        // 传统搜索结果格式
        const results: SearchResult[] = (data.data.items || data.data || []).map((item: any) => ({
          id: item.id,
          title: item.title,
          content: item.content,
          summary: item.summary,
          category: item.category?.name,
          tags: item.tags?.map((t: any) => t.tag.name) || [],
          priority: item.priority,
          useCount: item.useCount,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }));

        const searchInfo = {
          query,
          searchType: type,
          searchTime: 0,
          totalCount: results.length
        };

        setLastSearchInfo(searchInfo);
        onSearchResults(results, searchInfo);
      } else {
        // 向量搜索结果格式
        const searchResponse: SearchResponse = data.data;
        const searchInfo = {
          query: searchResponse.query,
          searchType: searchResponse.searchType as SearchType,
          searchTime: searchResponse.searchTime,
          totalCount: searchResponse.totalCount
        };

        setLastSearchInfo(searchInfo);
        onSearchResults(searchResponse.results, searchInfo);
      }

    } catch (error) {
      console.error('搜索错误:', error);
      // 发生错误时回退到传统搜索
      await loadTraditionalResults();
    } finally {
      setIsSearching(false);
    }
  }, [selectedCategory, selectedStatus, includeContent, onSearchResults]);

  // 加载传统结果（无搜索查询时）
  const loadTraditionalResults = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (selectedCategory) params.append('categoryId', selectedCategory);
      if (selectedStatus) params.append('status', selectedStatus);

      const response = await fetch(`/api/knowledge/items?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      const results: SearchResult[] = (data.data.items || data.data || []).map((item: any) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        summary: item.summary,
        category: item.category?.name,
        tags: item.tags?.map((t: any) => t.tag.name) || [],
        priority: item.priority,
        useCount: item.useCount,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));

      const searchInfo = {
        query: '',
        searchType: 'keyword' as SearchType,
        searchTime: 0,
        totalCount: results.length
      };

      setLastSearchInfo(searchInfo);
      onSearchResults(results, searchInfo);
    } catch (error) {
      console.error('加载数据错误:', error);
      // 发生错误时，提供空结果而不是崩溃
      onSearchResults([], {
        query: '',
        searchType: 'keyword' as SearchType,
        searchTime: 0,
        totalCount: 0
      });
    }
  }, [selectedCategory, selectedStatus, onSearchResults]);

  // 手动搜索处理函数
  const handleManualSearch = useCallback(() => {
    if (searchQuery.trim() && searchQuery.trim().length >= 2) {
      performSearch(searchQuery, searchType, similarityThreshold[0], maxResults[0]);
    }
  }, [searchQuery, searchType, similarityThreshold, maxResults, performSearch]);

  // 当搜索查询改变时执行搜索（仅关键词搜索自动触发）
  useEffect(() => {
    if (searchType === 'keyword') {
      performSearch(debouncedSearchQuery, searchType, similarityThreshold[0], maxResults[0]);
    }
  }, [debouncedSearchQuery, searchType, similarityThreshold[0], maxResults[0]]);

  // 当分类或状态改变时重新搜索
  useEffect(() => {
    if (searchQuery.trim()) {
      // 关键词搜索自动触发，其他搜索类型需要手动触发
      if (searchType === 'keyword') {
        performSearch(searchQuery, searchType, similarityThreshold[0], maxResults[0]);
      }
    } else {
      loadTraditionalResults();
    }
  }, [selectedCategory, selectedStatus]);

  // 搜索类型改变处理
  const handleSearchTypeChange = (newType: SearchType) => {
    setSearchType(newType);
    onSearchModeChange(newType);
    
    if (searchQuery.trim()) {
      performSearch(searchQuery, newType, similarityThreshold[0], maxResults[0]);
    }
  };

  return (
    <Card>
      <CardContent className="p-6 space-y-4">
        {/* 搜索模式切换 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Label className="text-sm font-medium">搜索模式:</Label>
            <Tabs value={searchType} onValueChange={handleSearchTypeChange} className="w-auto">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="keyword" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  关键词
                </TabsTrigger>
                <TabsTrigger value="vector" className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  语义搜索
                </TabsTrigger>
                <TabsTrigger value="hybrid" className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  智能搜索
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          {/* 搜索状态指示器 */}
          {lastSearchInfo && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Badge variant="outline">
                {lastSearchInfo.totalCount} 个结果
              </Badge>
              {lastSearchInfo.searchTime > 0 && (
                <Badge variant="outline">
                  {lastSearchInfo.searchTime}ms
                </Badge>
              )}
              <Badge variant={searchType === 'keyword' ? 'secondary' : 'default'}>
                {searchType === 'keyword' ? '关键词' : searchType === 'vector' ? '语义' : '智能'}
              </Badge>
            </div>
          )}
        </div>

        {/* 搜索输入框 */}
        <div className="flex gap-4">
          <div className="flex-1">
            <div className="relative flex">
              <div className="relative flex-1">
                {searchType === 'keyword' ? (
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                ) : (
                  <Brain className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                )}
                <Input
                  placeholder={
                    searchType === 'keyword'
                      ? "搜索知识条目..."
                      : searchType === 'vector'
                      ? "输入自然语言进行语义搜索..."
                      : "智能搜索，支持关键词和语义理解..."
                  }
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`pl-10 ${searchType !== 'keyword' ? 'rounded-r-none' : ''}`}
                  disabled={isSearching}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && searchType !== 'keyword') {
                      handleManualSearch();
                    }
                  }}
                />
                {isSearching && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
                )}
              </div>

              {/* 查询按钮 - 仅在语义搜索和智能搜索模式下显示 */}
              {searchType !== 'keyword' && (
                <Button
                  onClick={handleManualSearch}
                  disabled={isSearching || !searchQuery.trim() || (searchQuery.trim().length < 2)}
                  className="rounded-l-none border-l-0"
                  size="default"
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  查询
                </Button>
              )}
            </div>
          </div>
          
          {/* 分类筛选 */}
          <select
            value={selectedCategory}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">所有分类</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          
          {/* 状态筛选 */}
          <select
            value={selectedStatus}
            onChange={(e) => onStatusChange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">所有状态</option>
            <option value="ACTIVE">已发布</option>
            <option value="DRAFT">草稿</option>
            <option value="ARCHIVED">已归档</option>
          </select>
        </div>

        {/* 向量搜索高级选项 */}
        {(searchType === 'vector' || searchType === 'hybrid') && (
          <div className="border-t pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 相似度阈值 */}
              <div className="space-y-2">
                <Label className="text-sm">相似度阈值: {similarityThreshold[0]}</Label>
                <Slider
                  value={similarityThreshold}
                  onValueChange={setSimilarityThreshold}
                  max={1}
                  min={0.1}
                  step={0.1}
                  className="w-full"
                />
              </div>
              
              {/* 最大结果数 */}
              <div className="space-y-2">
                <Label className="text-sm">最大结果数: {maxResults[0]}</Label>
                <Slider
                  value={maxResults}
                  onValueChange={setMaxResults}
                  max={50}
                  min={5}
                  step={5}
                  className="w-full"
                />
              </div>
              
              {/* 包含内容 */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="include-content"
                  checked={includeContent}
                  onCheckedChange={setIncludeContent}
                />
                <Label htmlFor="include-content" className="text-sm">
                  包含完整内容
                </Label>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
