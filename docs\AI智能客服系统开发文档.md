# 🤖 吴都乔街智能客服系统开发文档

## 📋 项目概述

### 项目目标
基于现有的钉钉登录中控平台，开发一套完整的AI智能客服系统，为吴都乔街景区提供智能化的游客咨询服务。

### 技术栈
- **前端**: Next.js 15 + React + TypeScript + Shadcn-Admin
- **后端**: Next.js API Routes (无需Express)
- **数据库**: MySQL 8.0 + Prisma ORM
- **AI服务**: DeepSeek API (主) + OpenAI API (备用)
- **向量搜索**: MySQL Vector (推荐) / Redis Vector (高性能) / Pinecone (企业级)
- **缓存**: Redis 7.0
- **部署**: Docker + 本地部署

### 向量搜索方案对比
| 方案 | 优势 | 劣势 | 适用场景 | 推荐度 |
|------|------|------|----------|--------|
| **MySQL Vector** | 集成简单，无额外部署 | 性能一般，功能有限 | 中小规模，快速开发 | ⭐⭐⭐⭐ |
| **Redis Vector** | 性能优秀，功能丰富 | 需要额外部署和维护 | 大规模，高性能要求 | ⭐⭐⭐ |
| **Pinecone** | 功能强大，免运维 | 成本高，依赖外部服务 | 企业级，预算充足 | ⭐⭐ |

**推荐方案**: 使用MySQL Vector，在应用层实现余弦相似度计算，简单高效。

---

## 🎯 核心功能需求

### 1. AI对话引擎
#### 功能特性
- **多轮对话**: 支持上下文理解和记忆
- **意图识别**: 自动分类用户咨询类型（门票、交通、路线等）
- **情感分析**: 识别用户情绪并调整回复策略
- **智能路由**: 根据问题类型自动分配处理方式
- **容错机制**: AI服务失败时的降级策略
- **多语言支持**: 中文、英文、日文、韩文

#### 核心业务场景
```typescript
enum BusinessScenario {
  TICKET_INQUIRY = 'ticket_inquiry',      // 门票咨询
  OPENING_HOURS = 'opening_hours',        // 开放时间
  TRANSPORTATION = 'transportation',      // 交通指南
  ROUTE_PLANNING = 'route_planning',      // 路线规划
  FACILITIES = 'facilities',              // 设施服务
  DINING = 'dining',                      // 餐饮信息
  ACCOMMODATION = 'accommodation',        // 住宿推荐
  WEATHER = 'weather',                    // 天气查询
  EMERGENCY = 'emergency',                // 紧急求助
  COMPLAINT = 'complaint'                 // 投诉建议
}
```

#### 技术实现
```typescript
interface ConversationContext {
  sessionId: string;
  userId: string;
  messages: Message[];
  intent: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  metadata: Record<string, any>;
}
```

### 2. 知识库管理系统
#### 功能特性
- **内容管理**: 景区介绍、路线规划、服务信息
- **智能问答**: 基于知识库的自动回复
- **内容更新**: 实时编辑、版本控制
- **搜索功能**: 全文检索、标签分类
- **导入功能**: 支持文档、网页、RSS导入

#### 数据结构
```typescript
interface KnowledgeBase {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  embedding: number[];
  createdAt: Date;
  updatedAt: Date;
}
```

### 3. 实时监控大屏
#### 功能特性
- **实时统计**: 游客流量、咨询量、满意度
- **可视化图表**: 趋势分析、热力图、统计报表
- **预警系统**: 异常流量、服务质量下降提醒
- **数据导出**: Excel、PDF报告生成

### 4. 客流分析模块
#### 功能特性
- **实时统计**: 在线用户数、咨询热点
- **行为分析**: 用户路径、停留时间、转化率
- **地理分布**: 访客来源地图
- **设备分析**: 移动端/PC端使用情况

### 5. 舆情分析功能
#### 功能特性
- **评论监控**: 社交媒体、评价平台
- **情感分析**: 正面/负面评价统计
- **关键词提取**: 热门话题、问题点
- **预警机制**: 负面舆情及时通知

---

## 🏗️ 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   API网关       │    │   AI服务层      │
│  Next.js App    │◄──►│  Express.js     │◄──►│  DeepSeek API   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   缓存层        │    │   数据存储层    │    │   向量搜索      │
│   Redis         │◄──►│   MySQL 8.0     │◄──►│  MySQL Vector   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据库设计

#### 核心表结构
```sql
-- AI对话会话表
CREATE TABLE ai_conversations (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36),
    session_id VARCHAR(100),
    title VARCHAR(200),
    status ENUM('active', 'closed', 'archived'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_session (user_id, session_id)
);

-- AI消息表
CREATE TABLE ai_messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36),
    role ENUM('user', 'assistant', 'system'),
    content TEXT,
    intent VARCHAR(100),
    sentiment VARCHAR(20),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id)
);

-- 知识库表
CREATE TABLE knowledge_base (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(500),
    content LONGTEXT,
    category VARCHAR(100),
    tags JSON,
    embedding JSON,
    status ENUM('active', 'draft', 'archived'),
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FULLTEXT(title, content)
);

-- 客服统计表
CREATE TABLE service_analytics (
    id VARCHAR(36) PRIMARY KEY,
    date DATE,
    total_conversations INT DEFAULT 0,
    total_messages INT DEFAULT 0,
    avg_response_time DECIMAL(10,2),
    satisfaction_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔧 技术实现方案

### 1. AI对话引擎实现

#### DeepSeek API集成
```typescript
// src/lib/ai/deepseek.ts
export class DeepSeekService {
  private apiKey: string;
  private baseURL = 'https://api.deepseek.com';

  async chat(messages: ChatMessage[], context?: ConversationContext) {
    const response = await fetch(`${this.baseURL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: this.formatMessages(messages, context),
        temperature: 0.7,
        max_tokens: 2000
      })
    });
    
    return response.json();
  }

  private formatMessages(messages: ChatMessage[], context?: ConversationContext) {
    const systemPrompt = this.buildSystemPrompt(context);
    return [
      { role: 'system', content: systemPrompt },
      ...messages
    ];
  }

  private buildSystemPrompt(context?: ConversationContext): string {
    return `你是吴都乔街景区的智能客服助手，专门为游客提供咨询服务。

你的职责：
1. 回答关于景区的各种问题（门票、开放时间、交通、景点介绍等）
2. 提供路线规划和游览建议
3. 协助解决游客遇到的问题
4. 保持友好、专业的服务态度

当前对话上下文：
- 用户意图：${context?.intent || '未知'}
- 情感状态：${context?.sentiment || '中性'}

请用简洁、友好的语言回复，如果不确定答案，请诚实说明并建议联系人工客服。`;
  }
}
```

### 2. 知识库检索系统

#### 向量搜索实现
```typescript
// src/lib/knowledge/vector-search.ts
export class VectorSearchService {
  async searchSimilar(query: string, limit: number = 5): Promise<KnowledgeItem[]> {
    // 1. 将查询转换为向量
    const queryEmbedding = await this.getEmbedding(query);
    
    // 2. 在数据库中搜索相似向量
    const results = await prisma.$queryRaw`
      SELECT id, title, content, category,
             JSON_EXTRACT(embedding, '$') as embedding_vector,
             (
               SELECT SUM(a.val * b.val) / (
                 SQRT(SUM(a.val * a.val)) * SQRT(SUM(b.val * b.val))
               )
               FROM JSON_TABLE(embedding, '$[*]' COLUMNS (val DOUBLE PATH '$')) a,
                    JSON_TABLE(${JSON.stringify(queryEmbedding)}, '$[*]' COLUMNS (val DOUBLE PATH '$')) b
               WHERE a.val IS NOT NULL AND b.val IS NOT NULL
             ) as similarity
      FROM knowledge_base
      WHERE status = 'active'
      ORDER BY similarity DESC
      LIMIT ${limit}
    `;
    
    return results;
  }

  private async getEmbedding(text: string): Promise<number[]> {
    // 调用DeepSeek或OpenAI的embedding API
    const response = await fetch('https://api.deepseek.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'text-embedding-ada-002',
        input: text
      })
    });
    
    const data = await response.json();
    return data.data[0].embedding;
  }
}
```

---

## 📅 开发进度规划

### Phase 1: 基础架构 (第1-2周)
#### 目标
- 搭建AI对话基础框架
- 实现简单的问答功能
- 建立知识库数据结构

#### 具体任务
- [ ] 创建数据库表结构
- [ ] 集成DeepSeek API
- [ ] 开发基础对话界面
- [ ] 实现会话管理
- [ ] 建立知识库管理后台

#### 交付物
- 可以进行基本AI对话的系统
- 知识库管理界面
- API接口文档

### Phase 2: 智能化功能 (第3-4周)
#### 目标
- 实现知识库检索
- 添加意图识别
- 优化对话体验

#### 具体任务
- [ ] 实现向量搜索功能
- [ ] 集成RAG（检索增强生成）
- [ ] 开发意图识别模块
- [ ] 添加情感分析
- [ ] 优化对话界面

#### 交付物
- 智能知识库问答系统
- 意图识别功能
- 改进的用户界面

### Phase 3: 监控分析 (第5周)
#### 目标
- 实现数据监控大屏
- 添加统计分析功能
- 完善系统功能

#### 具体任务
- [ ] 开发实时监控大屏
- [ ] 实现数据统计分析
- [ ] 添加报表导出功能
- [ ] 系统性能优化
- [ ] 用户体验优化

#### 交付物
- 完整的监控分析系统
- 数据报表功能
- 性能优化的系统

---

## 🎨 界面设计规范

### 主要页面结构
```
智能客服系统
├── 对话界面
│   ├── 聊天窗口
│   ├── 快捷回复
│   └── 历史记录
├── 知识库管理
│   ├── 内容编辑
│   ├── 分类管理
│   └── 搜索测试
├── 数据监控
│   ├── 实时大屏
│   ├── 统计报表
│   └── 性能监控
└── 系统设置
    ├── AI模型配置
    ├── 用户权限
    └── 系统参数
```

### UI组件规范
- 使用Shadcn-Admin组件库
- 保持与现有系统界面一致
- 响应式设计，支持移动端
- 深色/浅色主题切换

---

## 🔒 安全与性能

### 安全措施
- API密钥安全存储
- 用户输入内容过滤
- 对话内容加密存储
- 访问权限控制

### 性能优化
- Redis缓存常用回复
- 数据库查询优化
- 异步处理长时间任务
- CDN加速静态资源

---

## 🔌 API接口设计

### 1. 对话相关API

#### 创建对话会话
```typescript
POST /api/ai/conversations
Content-Type: application/json

{
  "title": "景区咨询",
  "userId": "user-123"
}

Response:
{
  "success": true,
  "data": {
    "id": "conv-456",
    "sessionId": "sess-789",
    "title": "景区咨询",
    "status": "active",
    "createdAt": "2025-01-28T10:00:00Z"
  }
}
```

#### 发送消息
```typescript
POST /api/ai/conversations/:id/messages
Content-Type: application/json

{
  "content": "请问景区的开放时间是什么？",
  "role": "user"
}

Response:
{
  "success": true,
  "data": {
    "userMessage": {
      "id": "msg-123",
      "content": "请问景区的开放时间是什么？",
      "role": "user",
      "createdAt": "2025-01-28T10:01:00Z"
    },
    "assistantMessage": {
      "id": "msg-124",
      "content": "吴都乔街景区的开放时间是每天上午8:00至下午18:00，全年无休。建议您在16:30之前入园，以确保有充足的游览时间。",
      "role": "assistant",
      "intent": "opening_hours",
      "knowledgeUsed": ["kb-001", "kb-002"],
      "createdAt": "2025-01-28T10:01:02Z"
    }
  }
}
```

#### 获取对话历史
```typescript
GET /api/ai/conversations/:id/messages?page=1&limit=20

Response:
{
  "success": true,
  "data": {
    "messages": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    }
  }
}
```

### 2. 知识库相关API

#### 搜索知识库
```typescript
POST /api/knowledge/search
Content-Type: application/json

{
  "query": "门票价格",
  "category": "ticket_info",
  "limit": 5
}

Response:
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "kb-001",
        "title": "门票价格说明",
        "content": "成人票：80元/人，儿童票：40元/人...",
        "category": "ticket_info",
        "similarity": 0.95,
        "tags": ["门票", "价格", "优惠"]
      }
    ]
  }
}
```

#### 创建知识库条目
```typescript
POST /api/knowledge/items
Content-Type: application/json

{
  "title": "景区交通指南",
  "content": "详细的交通信息...",
  "category": "transportation",
  "tags": ["交通", "路线", "停车"]
}
```

### 3. 统计分析API

#### 获取实时统计
```typescript
GET /api/analytics/realtime

Response:
{
  "success": true,
  "data": {
    "activeConversations": 15,
    "totalMessagesToday": 234,
    "avgResponseTime": 1.2,
    "satisfactionScore": 4.6,
    "topIntents": [
      {"intent": "opening_hours", "count": 45},
      {"intent": "ticket_price", "count": 38}
    ]
  }
}
```

---

## 🎯 关键功能实现细节

### 1. RAG（检索增强生成）实现

```typescript
// src/lib/ai/rag-service.ts
export class RAGService {
  constructor(
    private vectorSearch: VectorSearchService,
    private aiService: DeepSeekService
  ) {}

  async generateResponse(query: string, context: ConversationContext): Promise<string> {
    // 1. 检索相关知识
    const relevantKnowledge = await this.vectorSearch.searchSimilar(query, 3);

    // 2. 构建增强提示
    const enhancedPrompt = this.buildEnhancedPrompt(query, relevantKnowledge, context);

    // 3. 生成回复
    const response = await this.aiService.chat([
      { role: 'user', content: enhancedPrompt }
    ], context);

    // 4. 记录使用的知识库
    await this.logKnowledgeUsage(relevantKnowledge, context.sessionId);

    return response.choices[0].message.content;
  }

  private buildEnhancedPrompt(query: string, knowledge: KnowledgeItem[], context: ConversationContext): string {
    const knowledgeContext = knowledge.map(item =>
      `【${item.title}】\n${item.content}`
    ).join('\n\n');

    return `基于以下知识库信息回答用户问题：

知识库内容：
${knowledgeContext}

用户问题：${query}

请基于上述知识库内容回答问题，如果知识库中没有相关信息，请诚实说明并建议联系人工客服。`;
  }
}
```

### 2. 意图识别实现

```typescript
// src/lib/ai/intent-classifier.ts
export class IntentClassifier {
  private intentPatterns = {
    'opening_hours': ['开放时间', '营业时间', '几点开门', '几点关门'],
    'ticket_price': ['门票', '票价', '多少钱', '价格'],
    'transportation': ['怎么去', '交通', '路线', '停车'],
    'attractions': ['景点', '有什么好玩', '推荐', '必看'],
    'dining': ['餐厅', '吃饭', '美食', '小吃'],
    'facilities': ['设施', '服务', '洗手间', '休息'],
    'weather': ['天气', '下雨', '温度'],
    'complaint': ['投诉', '不满意', '问题', '差评']
  };

  async classifyIntent(message: string): Promise<string> {
    // 1. 基于关键词匹配
    for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
      if (patterns.some(pattern => message.includes(pattern))) {
        return intent;
      }
    }

    // 2. 使用AI进行意图分类
    const aiClassification = await this.aiClassifyIntent(message);
    return aiClassification || 'general';
  }

  private async aiClassifyIntent(message: string): Promise<string> {
    const prompt = `请分析以下用户消息的意图，从以下类别中选择最合适的一个：
${Object.keys(this.intentPatterns).join(', ')}

用户消息：${message}

请只返回意图类别名称，不要其他内容。`;

    // 调用AI服务进行分类
    // 实现细节...
  }
}
```

### 3. 情感分析实现

```typescript
// src/lib/ai/sentiment-analyzer.ts
export class SentimentAnalyzer {
  async analyzeSentiment(message: string): Promise<'positive' | 'neutral' | 'negative'> {
    const prompt = `请分析以下消息的情感倾向，返回 positive、neutral 或 negative 中的一个：

消息：${message}

请只返回情感类别，不要其他内容。`;

    const response = await this.aiService.chat([
      { role: 'user', content: prompt }
    ]);

    const sentiment = response.choices[0].message.content.trim().toLowerCase();

    if (['positive', 'neutral', 'negative'].includes(sentiment)) {
      return sentiment as 'positive' | 'neutral' | 'negative';
    }

    return 'neutral'; // 默认值
  }
}
```

---

## 📊 测试与部署

### 测试策略

#### 单元测试
```typescript
// tests/ai/rag-service.test.ts
describe('RAGService', () => {
  it('should generate response with relevant knowledge', async () => {
    const ragService = new RAGService(mockVectorSearch, mockAIService);
    const response = await ragService.generateResponse('门票多少钱', mockContext);

    expect(response).toContain('门票价格');
    expect(mockVectorSearch.searchSimilar).toHaveBeenCalledWith('门票多少钱', 3);
  });
});
```

#### 集成测试
```typescript
// tests/api/conversations.test.ts
describe('Conversations API', () => {
  it('should create conversation and send message', async () => {
    const response = await request(app)
      .post('/api/ai/conversations')
      .send({ title: '测试对话', userId: 'test-user' })
      .expect(200);

    const conversationId = response.body.data.id;

    const messageResponse = await request(app)
      .post(`/api/ai/conversations/${conversationId}/messages`)
      .send({ content: '你好', role: 'user' })
      .expect(200);

    expect(messageResponse.body.data.assistantMessage).toBeDefined();
  });
});
```

### 部署方案

#### Docker配置
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  ai-customer-service:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/wudu_platform
      - REDIS_URL=redis://redis:6379
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: wudu_platform
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

---

## 📈 后续扩展计划

### Phase 4: 高级功能 (第6-8周)
- **多语言支持**: 英文、日文、韩文客服
- **语音交互**: 语音输入和语音回复
- **图像识别**: 识别景区照片并提供信息
- **视频客服**: 真人客服视频通话

### Phase 5: 移动端扩展 (第9-10周)
- **微信小程序**: 景区官方小程序集成
- **钉钉机器人**: 企业内部客服机器人
- **企业微信**: 员工内部咨询系统
- **APP集成**: 原生移动应用集成

### Phase 6: 智能化升级 (第11-12周)
- **个性化推荐**: 基于用户历史的个性化服务
- **预测分析**: 预测游客需求和流量
- **自动学习**: 从对话中自动学习新知识
- **多模态交互**: 文字、语音、图像综合交互

---

## 📞 技术支持与维护

### 开发团队角色
- **项目负责人**: 整体规划和进度管理
- **前端开发**: React/Next.js界面开发
- **后端开发**: Node.js/TypeScript API开发
- **AI算法工程师**: AI模型集成和优化
- **测试工程师**: 功能测试和性能测试
- **运维工程师**: 部署和监控维护

### 文档维护规范
- 每个功能模块都需要对应的技术文档
- API变更必须同步更新接口文档
- 每周进行技术方案评审会议
- 每月更新项目进度和技术债务报告

### 代码质量标准
- TypeScript严格模式
- ESLint + Prettier代码规范
- 单元测试覆盖率 > 80%
- 代码审查制度
- 自动化CI/CD流程

---

*文档版本：v1.1*
*创建时间：2025-01-28*
*最后更新：2025-01-28*
*下次更新：开发启动后每周更新*
