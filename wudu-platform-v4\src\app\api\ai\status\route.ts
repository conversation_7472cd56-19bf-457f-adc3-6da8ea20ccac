import { NextRequest, NextResponse } from 'next/server';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';
import { conversationManager } from '@/lib/ai/conversation-manager';

// GET /api/ai/status - 获取AI服务状态
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 检查AI服务状态...');

    // 获取AI服务状态
    const servicesStatus = await aiServiceManager.getServicesStatus();
    
    // 获取对话统计（最近24小时）
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const stats = await conversationManager.getConversationStats({
      start: yesterday,
      end: new Date()
    });

    // 计算服务健康度
    const availableServices = servicesStatus.filter(s => s.available).length;
    const totalServices = servicesStatus.length;
    const healthScore = totalServices > 0 ? (availableServices / totalServices) * 100 : 0;

    const status = {
      healthy: healthScore >= 50, // 至少50%的服务可用才算健康
      healthScore,
      services: servicesStatus,
      stats: {
        last24Hours: {
          conversations: stats.totalConversations,
          messages: stats.totalMessages,
          avgResponseTime: Math.round(stats.avgResponseTime)
        }
      },
      timestamp: new Date().toISOString()
    };

    console.log('✅ AI服务状态检查完成:', {
      healthy: status.healthy,
      healthScore: status.healthScore,
      availableServices: `${availableServices}/${totalServices}`
    });

    return NextResponse.json({
      success: true,
      status
    });

  } catch (error: any) {
    console.error('❌ AI服务状态检查失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || '服务状态检查失败',
      status: {
        healthy: false,
        healthScore: 0,
        services: [],
        stats: {
          last24Hours: {
            conversations: 0,
            messages: 0,
            avgResponseTime: 0
          }
        },
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}


