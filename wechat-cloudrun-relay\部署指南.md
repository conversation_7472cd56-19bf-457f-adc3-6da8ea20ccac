# 微信云托管消息中转服务部署指南

## 🎯 架构说明

```
用户消息 → 微信服务器 → 微信云托管(中转) → 本地服务器(AI处理) → 返回回复
```

## 📋 部署步骤

### 1. 准备云托管服务

#### 1.1 登录微信云托管
- 访问：https://cloud.weixin.qq.com/
- 使用公众号管理员微信扫码登录

#### 1.2 创建环境
- 点击"创建环境"
- 选择"按量计费"
- 环境名称：`wechat-relay`
- 地域：选择就近地域

#### 1.3 创建服务
- 进入环境后，点击"新建服务"
- 服务名称：`message-relay`
- 选择"上传代码包"方式

### 2. 上传代码

#### 2.1 打包代码
将以下文件打包成zip：
- index.js
- package.json
- Dockerfile

#### 2.2 上传配置
- 上传代码包zip文件
- 端口：80
- 环境变量：
  - `LOCAL_SERVER_URL`: `http://g98a2526.natappfree.cc`
  - `NODE_ENV`: `production`

#### 2.3 部署发布
- 点击"部署"
- 等待构建完成
- 点击"发布"，流量比例100%

### 3. 配置消息推送

#### 3.1 进入消息推送配置
- 在云托管控制台，点击"设置" → "全局设置" → "消息推送"

#### 3.2 配置参数
- 环境ID：选择刚创建的环境
- 服务名称：`message-relay`
- 路径：`/wechat-message`
- 推送模式：`JSON`

#### 3.3 确认配置
- 扫码确认（需要公众号管理员）
- 等待配置生效

### 4. 测试验证

#### 4.1 检查服务状态
- 在云托管控制台查看服务是否正常运行
- 查看服务日志确认启动成功

#### 4.2 测试消息推送
- 向公众号发送测试消息
- 查看云托管日志，确认收到消息
- 查看本地服务器日志，确认消息转发成功

## 🔧 环境变量配置

在云托管服务中配置以下环境变量：

| 变量名 | 值 | 说明 |
|--------|-----|------|
| `LOCAL_SERVER_URL` | `http://g98a2526.natappfree.cc` | 本地服务器地址 |
| `LOCAL_API_PATH` | `/api/wechat-message` | 本地接收接口路径 |
| `NODE_ENV` | `production` | 运行环境 |
| `PORT` | `80` | 服务端口 |

## 📊 监控和日志

### 查看日志
- 云托管控制台 → 服务管理 → 日志
- 可以实时查看消息转发情况

### 监控指标
- 请求量：查看消息接收频率
- 响应时间：监控转发延迟
- 错误率：监控转发失败情况

## 🚨 故障排查

### 常见问题

1. **消息收不到**
   - 检查消息推送配置是否正确
   - 确认服务是否正常运行
   - 查看云托管日志

2. **转发失败**
   - 检查本地服务器是否可访问
   - 确认NATAPP是否正常
   - 查看网络连接状态

3. **回复不成功**
   - 检查回复格式是否正确
   - 确认消息类型处理逻辑
   - 查看微信服务器响应

### 日志关键字
- `[云托管中转] 收到微信消息` - 消息接收成功
- `[云托管中转] 转发消息到本地服务器` - 开始转发
- `[云托管中转] 本地服务器响应` - 转发成功
- `[云托管中转] 转发消息失败` - 转发失败

## 💰 费用说明

- 按实际使用量计费
- 免费额度：每月一定量的免费请求
- 预估费用：轻量级中转服务，费用很低

## 🔄 更新部署

当需要更新代码时：
1. 修改代码
2. 重新打包上传
3. 创建新版本
4. 部署发布

## 📞 技术支持

如遇问题可以：
- 查看微信云托管官方文档
- 在微信开发者社区提问
- 联系技术支持
