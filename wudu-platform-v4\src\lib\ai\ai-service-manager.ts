// AI服务管理器 - 支持多服务商和容错机制
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import { httpClient } from './http-client';
import { decrypt } from '../encryption';

const prisma = new PrismaClient();

// 注意：解密函数已移至 ../encryption.ts 文件中

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ConversationContext {
  sessionId: string;
  userId?: string;
  messages: ChatMessage[];
  intent?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  metadata?: Record<string, any>;
}

export interface AIService {
  name: string;
  chat(messages: ChatMessage[], context?: ConversationContext): Promise<string>;
  getEmbedding?(text: string): Promise<number[]>;
  isAvailable(): Promise<boolean>;
}

export class AIServiceManager {
  private services: AIService[] = [];
  private currentServiceIndex = 0;
  private initialized = false;
  private fallbackRules = new Map([
    ['门票', '门票相关信息请咨询景区工作人员，电话：0571-12345'],
    ['开放时间', '景区开放时间为8:00-18:00，具体信息请以官方公告为准'],
    ['交通', '交通路线信息请查看景区官网或咨询工作人员'],
    ['路线', '推荐游览路线请参考景区导览图或咨询现场工作人员'],
    ['餐饮', '景区内有多个餐饮点，详情请咨询服务中心'],
    ['停车', '景区提供免费停车场，位置请参考指示牌'],
    ['天气', '请关注天气预报，建议携带雨具'],
    ['紧急', '如遇紧急情况，请立即联系景区安保：0571-12345']
  ]);

  constructor() {
    // 异步初始化
    this.initializeServices().catch(console.error);
  }

  private async initializeServices() {
    try {
      // 从数据库读取AI配置
      const configs = await prisma.aiConfig.findMany({
        where: { isActive: true }
      });

      const configMap = configs.reduce((acc, config) => {
        const value = config.isEncrypted ? decrypt(config.configValue || '') : config.configValue;
        acc[config.configKey] = value;
        return acc;
      }, {} as Record<string, string>);

      // 按优先级添加AI服务
      if (configMap.deepseek_api_key) {
        this.services.push(new DeepSeekService(configMap.deepseek_api_key));
      }
      if (configMap.openai_api_key) {
        this.services.push(new OpenAIService(configMap.openai_api_key));
      }

      console.log(`🤖 AI服务管理器初始化完成，可用服务: ${this.services.length}个`);
    } catch (error) {
      console.error('❌ AI服务管理器初始化失败:', error);
      // 降级到环境变量
      if (process.env.DEEPSEEK_API_KEY) {
        this.services.push(new DeepSeekService(process.env.DEEPSEEK_API_KEY));
      }
      if (process.env.OPENAI_API_KEY) {
        this.services.push(new OpenAIService(process.env.OPENAI_API_KEY));
      }
      console.log(`🤖 AI服务管理器降级初始化完成，可用服务: ${this.services.length}个`);
    }
    this.initialized = true;
  }

  private async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeServices();
    }
  }

  async chat(messages: ChatMessage[], context?: ConversationContext): Promise<{
    response: string;
    serviceUsed: string;
    responseTime: number;
    success: boolean;
  }> {
    const startTime = Date.now();

    // 确保已初始化
    await this.ensureInitialized();

    // 如果没有可用的AI服务，直接使用降级策略
    if (this.services.length === 0) {
      console.warn('⚠️ 没有可用的AI服务，使用降级策略');
      return {
        response: this.getFallbackResponse(messages[messages.length - 1]?.content || ''),
        serviceUsed: 'fallback',
        responseTime: Date.now() - startTime,
        success: false
      };
    }

    // 尝试所有可用的AI服务
    for (let i = 0; i < this.services.length; i++) {
      const serviceIndex = (this.currentServiceIndex + i) % this.services.length;
      const service = this.services[serviceIndex];
      
      try {
        console.log(`🔄 尝试使用AI服务: ${service.name}`);
        
        // 检查服务可用性（优先使用上次成功的服务，跳过可用性检查）
        if (serviceIndex !== this.currentServiceIndex) {
          if (!(await service.isAvailable())) {
            console.warn(`⚠️ AI服务 ${service.name} 不可用，跳过`);
            continue;
          }
        } else {
          console.log(`⚡ 使用上次成功的服务 ${service.name}，跳过可用性检查`);
        }

        // 设置超时保护
        const response = await Promise.race([
          service.chat(messages, context),
          this.timeoutPromise(20000) // 20秒超时
        ]);

        // 记录成功的服务，下次优先使用
        this.currentServiceIndex = serviceIndex;
        
        const responseTime = Date.now() - startTime;
        console.log(`✅ AI服务 ${service.name} 响应成功，耗时: ${responseTime}ms`);
        
        // 记录性能指标
        await this.recordMetrics(service.name, responseTime, true);
        
        return {
          response,
          serviceUsed: service.name,
          responseTime,
          success: true
        };
      } catch (error: any) {
        const responseTime = Date.now() - startTime;
        console.warn(`❌ AI服务 ${service.name} 失败:`, error.message);
        await this.recordMetrics(service.name, responseTime, false);
        continue;
      }
    }

    // 所有AI服务都失败，使用降级策略
    console.warn('❌ 所有AI服务都失败，使用降级策略');
    return {
      response: this.getFallbackResponse(messages[messages.length - 1]?.content || ''),
      serviceUsed: 'fallback',
      responseTime: Date.now() - startTime,
      success: false
    };
  }

  private async timeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI服务响应超时')), ms);
    });
  }

  private getFallbackResponse(userMessage: string): string {
    // 基于关键词的简单回复规则
    for (const [keyword, response] of this.fallbackRules) {
      if (userMessage.includes(keyword)) {
        return `${response}\n\n抱歉，AI客服暂时不可用，以上是基础信息供参考。`;
      }
    }

    return '抱歉，AI客服暂时不可用，请联系人工客服获得帮助。\n客服电话：0571-12345\n工作时间：8:00-18:00';
  }

  private async recordMetrics(serviceName: string, responseTime: number, success: boolean) {
    // 记录AI服务性能指标
    try {
      // 这里可以记录到数据库或监控系统
      console.log(`📊 AI服务指标 - 服务: ${serviceName}, 响应时间: ${responseTime}ms, 成功: ${success}`);
    } catch (error) {
      console.error('记录AI服务指标失败:', error);
    }
  }

  // 获取服务状态
  async getServicesStatus(): Promise<Array<{name: string; available: boolean}>> {
    const status = [];
    for (const service of this.services) {
      try {
        const available = await service.isAvailable();
        status.push({ name: service.name, available });
      } catch {
        status.push({ name: service.name, available: false });
      }
    }
    return status;
  }
}

// DeepSeek服务实现
export class DeepSeekService implements AIService {
  name = 'DeepSeek';
  private apiKey: string;
  private baseURL = 'https://api.deepseek.com';
  private retryCount = 2; // 减少重试次数
  private retryDelay = 500; // 减少重试延迟到500ms
  private timeout = 15000; // 15秒超时

  // 可用性缓存
  private availabilityCache: { isAvailable: boolean; timestamp: number } | null = null;
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: ChatMessage[], context?: ConversationContext): Promise<string> {
    const systemPrompt = this.buildSystemPrompt(context);
    const formattedMessages = [
      { role: 'system' as const, content: systemPrompt },
      ...messages
    ];

    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      const attemptStartTime = Date.now();
      try {
        console.log(`🔄 DeepSeek API调用开始 (尝试 ${attempt}/${this.retryCount})`);

        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await httpClient.fetchStream(`${this.baseURL}/v1/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: formattedMessages,
            temperature: 0.7,
            max_tokens: 2000,
            stream: true  // 启用流式响应
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        const responseTime = Date.now() - attemptStartTime;

        if (!response.ok) {
          throw new Error(`DeepSeek API错误: ${response.status} ${response.statusText}`);
        }

        // 处理流式响应
        let fullContent = '';
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error('无法获取响应流');
        }

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    fullContent += content;
                  }
                } catch (e) {
                  // 忽略解析错误的行
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        console.log(`✅ DeepSeek API流式调用成功，耗时: ${responseTime}ms，内容长度: ${fullContent.length}`);
        return fullContent;

      } catch (error: any) {
        const attemptTime = Date.now() - attemptStartTime;
        console.warn(`❌ DeepSeek API调用失败 (尝试 ${attempt}/${this.retryCount})，耗时: ${attemptTime}ms，错误:`, error.message);

        if (attempt < this.retryCount) {
          const delayTime = this.retryDelay * attempt;
          console.log(`⏳ 等待 ${delayTime}ms 后重试...`);
          await this.delay(delayTime);
        } else {
          throw error;
        }
      }
    }

    throw new Error('DeepSeek服务调用失败');
  }

  async getEmbedding(text: string): Promise<number[]> {
    // DeepSeek的embedding API（如果支持）
    const response = await fetch(`${this.baseURL}/v1/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'text-embedding-ada-002',
        input: text
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek Embedding API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.data[0].embedding;
  }

  async isAvailable(): Promise<boolean> {
    // 检查缓存
    if (this.availabilityCache) {
      const now = Date.now();
      if (now - this.availabilityCache.timestamp < this.cacheTimeout) {
        console.log(`🔄 使用DeepSeek可用性缓存: ${this.availabilityCache.isAvailable}`);
        return this.availabilityCache.isAvailable;
      }
    }

    try {
      console.log('🔍 检查DeepSeek服务可用性...');
      const startTime = Date.now();

      // 使用更快的健康检查端点，而不是模型列表
      const response = await httpClient.fetch(`${this.baseURL}/v1/models`, {
        headers: { 'Authorization': `Bearer ${this.apiKey}` },
        signal: AbortSignal.timeout(3000) // 减少到3秒超时
      });

      const checkTime = Date.now() - startTime;
      const isAvailable = response.ok;

      // 更新缓存
      this.availabilityCache = {
        isAvailable,
        timestamp: Date.now()
      };

      console.log(`✅ DeepSeek可用性检查完成，耗时: ${checkTime}ms，结果: ${isAvailable}`);
      return isAvailable;
    } catch (error: any) {
      console.warn('⚠️ DeepSeek可用性检查失败:', error.message);

      // 缓存失败结果（较短时间）
      this.availabilityCache = {
        isAvailable: false,
        timestamp: Date.now()
      };

      return false;
    }
  }

  private buildSystemPrompt(context?: ConversationContext): string {
    const basePrompt = `你是吴都乔街景区的智能客服助手，专门为游客提供咨询服务。

你的职责：
1. 回答关于景区的各种问题（门票、开放时间、交通、景点介绍等）
2. 提供路线规划和游览建议
3. 协助解决游客遇到的问题
4. 保持友好、专业的服务态度

景区基本信息：
- 开放时间：8:00-18:00（全年无休）
- 门票价格：成人票80元，儿童票40元，老人票60元
- 客服电话：0571-12345
- 地址：浙江省杭州市吴都乔街

回复要求：
- 使用简洁、友好的语言，回复尽量控制在50字以内
- 直接回答问题，避免冗长的解释
- 如果不确定答案，请诚实说明并建议联系人工客服
- 对于紧急情况，立即提供联系方式
- 根据用户情绪调整回复语调

示例回复格式：
用户："门票多少钱？"
回复："成人票80元，儿童票40元，老人票60元。"

用户："怎么去景区？"
回复："地址：杭州市吴都乔街。建议乘坐地铁X号线到XX站。"`;

    if (context) {
      return `${basePrompt}

当前对话上下文：
- 用户意图：${context.intent || '未知'}
- 情感状态：${context.sentiment || '中性'}
- 对话轮次：${context.messages?.length || 0}`;
    }

    return basePrompt;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// OpenAI服务实现（备用）
export class OpenAIService implements AIService {
  name = 'OpenAI';
  private apiKey: string;
  private baseURL = 'https://api.openai.com';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: ChatMessage[], context?: ConversationContext): Promise<string> {
    // OpenAI API实现
    const response = await fetch(`${this.baseURL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages,
        temperature: 0.7,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/v1/models`, {
        headers: { 'Authorization': `Bearer ${this.apiKey}` },
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

// 导出单例实例
export const aiServiceManager = new AIServiceManager();
