// Prisma 枚举类型定义
export const MessageRole = {
  USER: 'USER',
  ASSISTANT: 'ASSISTANT', 
  SYSTEM: 'SYSTEM'
} as const;

export type MessageRoleType = typeof MessageRole[keyof typeof MessageRole];

export const ConversationStatus = {
  ACTIVE: 'ACTIVE',
  CLOSED: 'CLOSED',
  ARCHIVED: 'ARCHIVED'
} as const;

export type ConversationStatusType = typeof ConversationStatus[keyof typeof ConversationStatus];

export const FeedbackType = {
  LIKE: 'LIKE',
  DISLIKE: 'DISLIKE',
  REPORT: 'REPORT',
  SUGGESTION: 'SUGGESTION'
} as const;

export type FeedbackTypeType = typeof FeedbackType[keyof typeof FeedbackType];

export const FeedbackStatus = {
  PENDING: 'PENDING',
  PROCESSED: 'PROCESSED',
  CLOSED: 'CLOSED'
} as const;

export type FeedbackStatusType = typeof FeedbackStatus[keyof typeof FeedbackStatus];
