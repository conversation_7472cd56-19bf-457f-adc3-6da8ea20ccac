import { UnifiedMessage } from '@/types/webhook';

/**
 * 平台适配器基类
 * 定义了所有平台适配器必须实现的接口
 */
export abstract class PlatformAdapter {
  /** 平台名称 */
  abstract readonly platform: string;

  /**
   * 解析webhook请求，转换为统一消息格式
   * @param request HTTP请求对象
   * @returns 统一消息对象
   */
  abstract parseWebhook(request: Request): Promise<UnifiedMessage>;

  /**
   * 发送回复消息到原平台
   * @param message 原始消息
   * @param reply 回复内容
   * @returns 是否发送成功
   */
  abstract sendReply(message: UnifiedMessage, reply: string): Promise<boolean>;

  /**
   * 验证webhook请求的签名
   * @param request HTTP请求对象
   * @returns 签名是否有效
   */
  abstract verifySignature(request: Request): Promise<boolean>;

  /**
   * 生成会话ID
   * @param userId 用户ID
   * @returns 会话ID
   */
  protected generateSessionId(userId: string): string {
    return `${this.platform}:${userId}`;
  }

  /**
   * 记录错误日志
   * @param error 错误信息
   * @param context 上下文信息
   */
  protected logError(error: unknown, context?: Record<string, unknown>): void {
    console.error(`[${this.platform}] Webhook error:`, error, context);
  }

  /**
   * 记录信息日志
   * @param message 日志信息
   * @param context 上下文信息
   */
  protected logInfo(message: string, context?: Record<string, unknown>): void {
    console.log(`[${this.platform}] ${message}`, context);
  }
}
