import { NextResponse } from 'next/server';

export async function GET() {
    return NextResponse.json({
        status: 'ok',
        message: '吴都乔街智能客服中控平台 API 服务正常',
        timestamp: new Date().toISOString(),
        version: '4.0.0',
        framework: 'Shadcn-Admin (Next.js)',
        services: {
            auth: '钉钉登录服务',
            api: 'Next.js API Routes',
            ui: 'Shadcn/ui + Tailwind CSS'
        }
    });
}
