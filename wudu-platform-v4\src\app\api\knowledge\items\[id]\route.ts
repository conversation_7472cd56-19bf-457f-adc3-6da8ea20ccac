import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { auth } from '@/lib/auth';

// GET /api/knowledge/items/[id] - 获取单个知识条目
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const knowledgeItem = await prisma.knowledgeBase.findUnique({
      where: { id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          }
        },
        versions: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          }
        }
      }
    });

    if (!knowledgeItem) {
      return NextResponse.json(
        { success: false, error: '知识条目不存在' },
        { status: 404 }
      );
    }

    // 增加查看次数
    await prisma.knowledgeBase.update({
      where: { id },
      data: { viewCount: { increment: 1 } }
    });

    return NextResponse.json({
      success: true,
      data: knowledgeItem
    });

  } catch (error) {
    console.error('❌ 获取知识条目失败:', error);
    return NextResponse.json(
      { success: false, error: '获取知识条目失败' },
      { status: 500 }
    );
  }
}

// PUT /api/knowledge/items/[id] - 更新知识条目
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { title, content, summary, categoryId, tagIds, priority, status } = body;

    // 检查知识条目是否存在
    const existingItem = await prisma.knowledgeBase.findUnique({
      where: { id },
      include: {
        versions: {
          orderBy: { versionNumber: 'desc' },
          take: 1
        }
      }
    });

    if (!existingItem) {
      return NextResponse.json(
        { success: false, error: '知识条目不存在' },
        { status: 404 }
      );
    }

    // 如果内容有变化，创建新版本
    const contentChanged = existingItem.content !== content || existingItem.title !== title;
    let nextVersionNumber = 1;
    
    if (existingItem.versions.length > 0) {
      nextVersionNumber = existingItem.versions[0].versionNumber + 1;
    }

    // 开始事务
    const result = await prisma.$transaction(async (tx) => {
      // 更新主记录
      const updatedItem = await tx.knowledgeBase.update({
        where: { id },
        data: {
          title,
          content,
          summary,
          categoryId,
          priority,
          status,
          tags: tagIds ? {
            deleteMany: {},
            create: tagIds.map((tagId: string) => ({
              tagId
            }))
          } : undefined
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              avatar: true
            }
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  color: true
                }
              }
            }
          }
        }
      });

      // 如果内容有变化，创建版本记录
      if (contentChanged) {
        await tx.kbVersion.create({
          data: {
            knowledgeId: id,
            versionNumber: nextVersionNumber,
            title,
            content,
            changeLog: `版本 ${nextVersionNumber} - 内容更新`,
            createdBy: session.user.id
          }
        });
      }

      return updatedItem;
    });

    console.log(`✅ 更新知识条目成功: ${id}`);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('❌ 更新知识条目失败:', error);
    return NextResponse.json(
      { success: false, error: '更新知识条目失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/knowledge/items/[id] - 删除知识条目
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const { id } = params;

    // 检查知识条目是否存在
    const existingItem = await prisma.knowledgeBase.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return NextResponse.json(
        { success: false, error: '知识条目不存在' },
        { status: 404 }
      );
    }

    // 删除知识条目（级联删除相关数据）
    await prisma.knowledgeBase.delete({
      where: { id }
    });

    console.log(`✅ 删除知识条目成功: ${id}`);

    return NextResponse.json({
      success: true,
      message: '知识条目删除成功'
    });

  } catch (error) {
    console.error('❌ 删除知识条目失败:', error);
    return NextResponse.json(
      { success: false, error: '删除知识条目失败' },
      { status: 500 }
    );
  }
}
