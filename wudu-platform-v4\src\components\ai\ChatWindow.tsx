'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Loader2, Send, Bot, User, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '@/lib/auth';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  createdAt: string;
  responseTime?: number;
  intent?: string;
  sentiment?: string;
}

interface ChatWindowProps {
  sessionId?: string;
  className?: string;
  height?: string;
}

export function ChatWindow({ sessionId, className, height = '600px' }: ChatWindowProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const currentSessionId = sessionId || `session_${Date.now()}`;

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 加载对话历史
  useEffect(() => {
    loadConversationHistory();
  }, [currentSessionId]);

  const loadConversationHistory = async () => {
    try {
      setConnectionStatus('connecting');
      const response = await fetch(`/api/ai/chat?sessionId=${currentSessionId}`);
      const data = await response.json();

      if (data.success && data.conversation) {
        setMessages(data.conversation.messages);
        setConversationId(data.conversation.id);
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('connected');
      }
    } catch (error) {
      console.error('加载对话历史失败:', error);
      setConnectionStatus('disconnected');
      setError('加载对话历史失败');
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setError(null);
    setIsLoading(true);

    // 立即显示用户消息
    const tempUserMessage: Message = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content: userMessage,
      createdAt: new Date().toISOString()
    };
    setMessages(prev => [...prev, tempUserMessage]);

    try {
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: userMessage,
          sessionId: currentSessionId,
          userId: user?.id
        })
      });

      const data = await response.json();

      if (data.success) {
        // 更新用户消息ID
        const updatedUserMessage: Message = {
          id: data.userMessage.id,
          role: 'user',
          content: data.userMessage.content,
          createdAt: data.userMessage.createdAt
        };

        // 添加AI回复
        const newMessages = [updatedUserMessage];
        if (data.assistantMessage) {
          newMessages.push({
            id: data.assistantMessage.id,
            role: 'assistant',
            content: data.assistantMessage.content,
            createdAt: data.assistantMessage.createdAt,
            responseTime: data.assistantMessage.responseTime
          });
        }

        // 替换临时消息
        setMessages(prev => {
          const filtered = prev.filter(msg => msg.id !== tempUserMessage.id);
          return [...filtered, ...newMessages];
        });

        setConversationId(data.conversationId);
        setConnectionStatus('connected');
      } else {
        // 显示错误，但保留用户消息
        setError(data.error || '发送消息失败');
        setMessages(prev => prev.map(msg => 
          msg.id === tempUserMessage.id 
            ? { ...msg, id: data.userMessage?.id || msg.id }
            : msg
        ));
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setError('网络连接失败，请检查网络后重试');
      setConnectionStatus('disconnected');
    } finally {
      setIsLoading(false);
      inputRef.current?.focus();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Loader2 className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'disconnected':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'AI客服在线';
      case 'connecting':
        return '连接中...';
      case 'disconnected':
        return 'AI客服离线';
    }
  };

  return (
    <Card className={className} style={{ height }}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            🤖 吴都乔街智能客服
          </CardTitle>
          <div className="flex items-center gap-2">
            {getConnectionStatusIcon()}
            <span className="text-sm text-muted-foreground">
              {getConnectionStatusText()}
            </span>
          </div>
        </div>
        {error && (
          <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg p-4 animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-500" />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800 mb-1">
                  连接出现问题
                </h4>
                <p className="text-sm text-red-700">
                  {error}
                </p>
                <div className="mt-3 flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-7 px-3 text-xs border-red-300 text-red-700 hover:bg-red-50"
                    onClick={() => setError(null)}
                  >
                    关闭
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-7 px-3 text-xs border-blue-300 text-blue-700 hover:bg-blue-50"
                    onClick={() => {
                      setError(null);
                      loadConversationHistory();
                    }}
                  >
                    重试连接
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="flex flex-col h-full p-0">
        {/* 消息列表 */}
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-4 pb-4">
            {messages.length === 0 && (
              <div className="text-center py-12 animate-in fade-in duration-500">
                <div className="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <Bot className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  👋 您好！我是吴都乔街景区的智能客服
                </h3>
                <p className="text-gray-600 mb-6">
                  有什么可以帮助您的吗？我可以为您提供景区相关信息
                </p>
                <div className="grid grid-cols-2 gap-3 max-w-md mx-auto">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-left">
                    <div className="text-sm font-medium text-blue-800 mb-1">🎫 门票信息</div>
                    <div className="text-xs text-blue-600">价格、优惠政策</div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-left">
                    <div className="text-sm font-medium text-green-800 mb-1">🕐 开放时间</div>
                    <div className="text-xs text-green-600">营业时间查询</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-left">
                    <div className="text-sm font-medium text-purple-800 mb-1">🚗 交通指南</div>
                    <div className="text-xs text-purple-600">路线、停车信息</div>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-left">
                    <div className="text-sm font-medium text-orange-800 mb-1">🗺️ 游览推荐</div>
                    <div className="text-xs text-orange-600">景点、路线规划</div>
                  </div>
                </div>
              </div>
            )}
            
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/ai-avatar.png" />
                    <AvatarFallback>
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={`max-w-[80%] rounded-2xl px-4 py-3 shadow-sm transition-all duration-200 hover:shadow-md ${
                    message.role === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-md'
                      : 'bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-bl-md'
                  }`}
                >
                  <div className={`text-sm whitespace-pre-wrap leading-relaxed ${
                    message.role === 'user' ? 'text-white' : 'text-gray-800'
                  }`}>
                    {message.content}
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <span className={`text-xs ${
                      message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {formatTime(message.createdAt)}
                    </span>
                    {message.responseTime && (
                      <Badge
                        variant={message.role === 'user' ? 'secondary' : 'outline'}
                        className={`text-xs ${
                          message.role === 'user'
                            ? 'bg-blue-400/20 text-blue-100 border-blue-300'
                            : 'bg-green-50 text-green-700 border-green-200'
                        }`}
                      >
                        ⚡ {message.responseTime}ms
                      </Badge>
                    )}
                    {message.intent && (
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          message.role === 'user'
                            ? 'border-blue-300 text-blue-100'
                            : 'border-purple-200 text-purple-700 bg-purple-50'
                        }`}
                      >
                        🎯 {message.intent}
                      </Badge>
                    )}
                  </div>
                </div>
                
                {message.role === 'user' && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.avatar} />
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {isLoading && (
              <div className="flex gap-3 justify-start animate-in slide-in-from-left-2 duration-300">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-blue-100">
                    <Bot className="h-4 w-4 text-blue-600" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg px-4 py-3 shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                    <span className="text-sm text-blue-700 font-medium">
                      AI正在思考中...
                    </span>
                    <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                  </div>
                  <div className="mt-2 text-xs text-blue-600/70">
                    预计响应时间: 3-5秒
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
        
        {/* 输入区域 */}
        <div className="border-t bg-gradient-to-r from-gray-50 to-gray-100 p-4">
          <div className="flex gap-3">
            <div className="flex-1 relative">
              <Input
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  connectionStatus === 'disconnected'
                    ? "连接已断开，请重试..."
                    : isLoading
                    ? "AI正在回复中..."
                    : "请输入您的问题..."
                }
                disabled={isLoading || connectionStatus === 'disconnected'}
                className={`pr-12 transition-all duration-200 ${
                  connectionStatus === 'disconnected'
                    ? 'border-red-300 bg-red-50'
                    : isLoading
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                }`}
              />
              {inputMessage.trim() && !isLoading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                  {inputMessage.length}/500
                </div>
              )}
            </div>
            <Button
              onClick={sendMessage}
              disabled={!inputMessage.trim() || isLoading || connectionStatus === 'disconnected'}
              className={`transition-all duration-200 ${
                !inputMessage.trim() || isLoading || connectionStatus === 'disconnected'
                  ? 'opacity-50'
                  : 'hover:scale-105 shadow-md hover:shadow-lg'
              }`}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <div className="flex items-center justify-between mt-3">
            <div className="text-xs text-gray-500">
              按 <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Enter</kbd> 发送，
              <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Shift+Enter</kbd> 换行
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              {connectionStatus === 'connected' && (
                <>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>已连接</span>
                </>
              )}
              {connectionStatus === 'disconnected' && (
                <>
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>连接断开</span>
                </>
              )}
              {isLoading && (
                <>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>处理中</span>
                </>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
