# 🗄️ AI智能客服系统数据库设计文档

## 📋 数据库概述

### 设计原则
- **扩展性**: 支持大量对话和知识库数据
- **性能**: 优化查询速度和并发处理
- **一致性**: 保证数据完整性和事务安全
- **兼容性**: 与现有MySQL数据库无缝集成

### 技术栈
- **数据库**: MySQL 8.0
- **ORM**: Prisma
- **缓存**: Redis
- **全文搜索**: MySQL FULLTEXT索引
- **向量搜索**: JSON存储 + 自定义相似度计算

---

## 🏗️ 表结构设计

### 1. AI对话相关表

#### ai_conversations - 对话会话表
```sql
CREATE TABLE ai_conversations (
    id VARCHAR(36) PRIMARY KEY COMMENT '会话唯一标识',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID，关联users表',
    session_id VARCHAR(100) NOT NULL COMMENT '会话标识符',
    title VARCHAR(200) NOT NULL COMMENT '对话标题',
    status ENUM('active', 'closed', 'archived') DEFAULT 'active' COMMENT '会话状态',
    intent VARCHAR(100) COMMENT '主要意图分类',
    sentiment VARCHAR(20) COMMENT '整体情感倾向',
    satisfaction_score DECIMAL(3,2) COMMENT '满意度评分(1-5)',
    total_messages INT DEFAULT 0 COMMENT '消息总数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_session (user_id, session_id),
    INDEX idx_status_created (status, created_at),
    INDEX idx_intent (intent),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='AI对话会话表';
```

#### ai_messages - 消息记录表
```sql
CREATE TABLE ai_messages (
    id VARCHAR(36) PRIMARY KEY COMMENT '消息唯一标识',
    conversation_id VARCHAR(36) NOT NULL COMMENT '所属会话ID',
    role ENUM('user', 'assistant', 'system') NOT NULL COMMENT '消息角色',
    content TEXT NOT NULL COMMENT '消息内容',
    intent VARCHAR(100) COMMENT '意图分类',
    sentiment VARCHAR(20) COMMENT '情感倾向',
    confidence_score DECIMAL(3,2) COMMENT '置信度评分',
    response_time_ms INT COMMENT '响应时间(毫秒)',
    knowledge_used JSON COMMENT '使用的知识库ID列表',
    metadata JSON COMMENT '扩展元数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_conversation_created (conversation_id, created_at),
    INDEX idx_role_created (role, created_at),
    INDEX idx_intent (intent),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
) COMMENT='AI消息记录表';
```

### 2. 知识库相关表

#### knowledge_base - 知识库主表
```sql
CREATE TABLE knowledge_base (
    id VARCHAR(36) PRIMARY KEY COMMENT '知识条目唯一标识',
    title VARCHAR(500) NOT NULL COMMENT '知识标题',
    content LONGTEXT NOT NULL COMMENT '知识内容',
    summary TEXT COMMENT '内容摘要',
    category_id VARCHAR(36) COMMENT '分类ID',
    tags JSON COMMENT '标签列表',
    embedding JSON COMMENT '向量嵌入数据',
    status ENUM('active', 'draft', 'archived') DEFAULT 'draft' COMMENT '状态',
    priority INT DEFAULT 0 COMMENT '优先级(0-100)',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    use_count INT DEFAULT 0 COMMENT '使用次数',
    created_by VARCHAR(36) NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FULLTEXT(title, content, summary) COMMENT '全文搜索索引',
    INDEX idx_category_status (category_id, status),
    INDEX idx_status_priority (status, priority DESC),
    INDEX idx_created_by (created_by),
    INDEX idx_use_count (use_count DESC),
    FOREIGN KEY (category_id) REFERENCES kb_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='知识库主表';
```

#### kb_categories - 知识库分类表
```sql
CREATE TABLE kb_categories (
    id VARCHAR(36) PRIMARY KEY COMMENT '分类唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    parent_id VARCHAR(36) COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    icon VARCHAR(100) COMMENT '图标',
    color VARCHAR(20) COMMENT '颜色标识',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_name_parent (name, parent_id),
    INDEX idx_parent_sort (parent_id, sort_order),
    INDEX idx_status (status),
    FOREIGN KEY (parent_id) REFERENCES kb_categories(id) ON DELETE CASCADE
) COMMENT='知识库分类表';
```

#### kb_tags - 知识库标签表
```sql
CREATE TABLE kb_tags (
    id VARCHAR(36) PRIMARY KEY COMMENT '标签唯一标识',
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    color VARCHAR(20) COMMENT '标签颜色',
    description VARCHAR(200) COMMENT '标签描述',
    use_count INT DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_name (name),
    INDEX idx_use_count (use_count DESC)
) COMMENT='知识库标签表';
```

#### kb_versions - 知识库版本表
```sql
CREATE TABLE kb_versions (
    id VARCHAR(36) PRIMARY KEY COMMENT '版本唯一标识',
    knowledge_id VARCHAR(36) NOT NULL COMMENT '知识条目ID',
    version_number INT NOT NULL COMMENT '版本号',
    title VARCHAR(500) NOT NULL COMMENT '标题',
    content LONGTEXT NOT NULL COMMENT '内容',
    change_log TEXT COMMENT '变更日志',
    created_by VARCHAR(36) NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_knowledge_version (knowledge_id, version_number),
    INDEX idx_knowledge_created (knowledge_id, created_at DESC),
    FOREIGN KEY (knowledge_id) REFERENCES knowledge_base(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
) COMMENT='知识库版本表';
```

### 3. 统计分析相关表

#### service_analytics - 服务统计表
```sql
CREATE TABLE service_analytics (
    id VARCHAR(36) PRIMARY KEY COMMENT '统计记录唯一标识',
    date DATE NOT NULL COMMENT '统计日期',
    hour TINYINT COMMENT '小时(0-23)，NULL表示全天统计',
    total_conversations INT DEFAULT 0 COMMENT '总对话数',
    total_messages INT DEFAULT 0 COMMENT '总消息数',
    unique_users INT DEFAULT 0 COMMENT '独立用户数',
    avg_response_time DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(秒)',
    avg_conversation_length DECIMAL(10,2) DEFAULT 0 COMMENT '平均对话长度',
    satisfaction_score DECIMAL(3,2) COMMENT '平均满意度评分',
    intent_distribution JSON COMMENT '意图分布统计',
    sentiment_distribution JSON COMMENT '情感分布统计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_date_hour (date, hour),
    INDEX idx_date (date),
    INDEX idx_satisfaction (satisfaction_score DESC)
) COMMENT='服务统计表';
```

#### user_feedback - 用户反馈表
```sql
CREATE TABLE user_feedback (
    id VARCHAR(36) PRIMARY KEY COMMENT '反馈唯一标识',
    conversation_id VARCHAR(36) COMMENT '关联对话ID',
    message_id VARCHAR(36) COMMENT '关联消息ID',
    user_id VARCHAR(36) NOT NULL COMMENT '用户ID',
    feedback_type ENUM('like', 'dislike', 'report', 'suggestion') NOT NULL COMMENT '反馈类型',
    rating TINYINT COMMENT '评分(1-5)',
    content TEXT COMMENT '反馈内容',
    status ENUM('pending', 'processed', 'closed') DEFAULT 'pending' COMMENT '处理状态',
    processed_by VARCHAR(36) COMMENT '处理人ID',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_conversation (conversation_id),
    INDEX idx_user_created (user_id, created_at DESC),
    INDEX idx_type_status (feedback_type, status),
    INDEX idx_rating (rating),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE SET NULL,
    FOREIGN KEY (message_id) REFERENCES ai_messages(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
) COMMENT='用户反馈表';
```

### 4. 系统配置相关表

#### ai_config - AI配置表
```sql
CREATE TABLE ai_config (
    id VARCHAR(36) PRIMARY KEY COMMENT '配置唯一标识',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密存储',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_config_key (config_key)
) COMMENT='AI系统配置表';
```

#### intent_patterns - 意图模式表
```sql
CREATE TABLE intent_patterns (
    id VARCHAR(36) PRIMARY KEY COMMENT '模式唯一标识',
    intent VARCHAR(100) NOT NULL COMMENT '意图名称',
    pattern VARCHAR(500) NOT NULL COMMENT '匹配模式',
    pattern_type ENUM('keyword', 'regex', 'semantic') DEFAULT 'keyword' COMMENT '模式类型',
    weight DECIMAL(3,2) DEFAULT 1.0 COMMENT '权重',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_intent_active (intent, is_active),
    INDEX idx_pattern_type (pattern_type)
) COMMENT='意图识别模式表';
```

---

## 🔧 Prisma Schema 定义

```prisma
// prisma/schema.prisma

model AiConversation {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  sessionId         String   @map("session_id")
  title             String
  status            ConversationStatus @default(ACTIVE)
  intent            String?
  sentiment         String?
  satisfactionScore Decimal? @map("satisfaction_score") @db.Decimal(3,2)
  totalMessages     Int      @default(0) @map("total_messages")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  user     User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages AiMessage[]
  feedback UserFeedback[]

  @@index([userId, sessionId])
  @@index([status, createdAt])
  @@index([intent])
  @@map("ai_conversations")
}

model AiMessage {
  id               String   @id @default(cuid())
  conversationId   String   @map("conversation_id")
  role             MessageRole
  content          String   @db.Text
  intent           String?
  sentiment        String?
  confidenceScore  Decimal? @map("confidence_score") @db.Decimal(3,2)
  responseTimeMs   Int?     @map("response_time_ms")
  knowledgeUsed    Json?    @map("knowledge_used")
  metadata         Json?
  createdAt        DateTime @default(now()) @map("created_at")

  conversation AiConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  feedback     UserFeedback[]

  @@index([conversationId, createdAt])
  @@index([role, createdAt])
  @@index([intent])
  @@map("ai_messages")
}

model KnowledgeBase {
  id         String   @id @default(cuid())
  title      String   @db.VarChar(500)
  content    String   @db.LongText
  summary    String?  @db.Text
  categoryId String?  @map("category_id")
  tags       Json?
  embedding  Json?
  status     KnowledgeStatus @default(DRAFT)
  priority   Int      @default(0)
  viewCount  Int      @default(0) @map("view_count")
  useCount   Int      @default(0) @map("use_count")
  createdBy  String   @map("created_by")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  category  KbCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  creator   User        @relation(fields: [createdBy], references: [id], onDelete: Restrict)
  versions  KbVersion[]

  @@index([categoryId, status])
  @@index([status, priority(sort: Desc)])
  @@index([createdBy])
  @@index([useCount(sort: Desc)])
  @@fulltext([title, content, summary])
  @@map("knowledge_base")
}

enum ConversationStatus {
  ACTIVE  @map("active")
  CLOSED  @map("closed")
  ARCHIVED @map("archived")
}

enum MessageRole {
  USER      @map("user")
  ASSISTANT @map("assistant")
  SYSTEM    @map("system")
}

enum KnowledgeStatus {
  ACTIVE   @map("active")
  DRAFT    @map("draft")
  ARCHIVED @map("archived")
}
```

---

## 📊 索引优化策略

### 查询优化索引
```sql
-- 对话查询优化
CREATE INDEX idx_conversations_user_status_created ON ai_conversations(user_id, status, created_at DESC);

-- 消息查询优化
CREATE INDEX idx_messages_conversation_role_created ON ai_messages(conversation_id, role, created_at DESC);

-- 知识库搜索优化
CREATE INDEX idx_knowledge_status_category_priority ON knowledge_base(status, category_id, priority DESC);

-- 统计查询优化
CREATE INDEX idx_analytics_date_hour ON service_analytics(date DESC, hour);
```

### 分区策略
```sql
-- 按月分区消息表（适用于大量数据）
ALTER TABLE ai_messages PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- 继续添加分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

---

## 🔄 数据迁移脚本

### 初始化数据
```sql
-- 插入默认知识库分类
INSERT INTO kb_categories (id, name, description, sort_order) VALUES
('cat-001', '景区介绍', '景区基本信息和介绍', 1),
('cat-002', '门票信息', '门票价格和购买方式', 2),
('cat-003', '交通指南', '交通路线和停车信息', 3),
('cat-004', '游览路线', '推荐游览路线和景点', 4),
('cat-005', '服务设施', '餐饮、休息等服务设施', 5);

-- 插入默认AI配置
INSERT INTO ai_config (id, config_key, config_value, config_type, description) VALUES
('cfg-001', 'deepseek_api_key', '', 'string', 'DeepSeek API密钥'),
('cfg-002', 'max_conversation_length', '50', 'number', '最大对话长度'),
('cfg-003', 'response_timeout', '30', 'number', '响应超时时间(秒)'),
('cfg-004', 'enable_sentiment_analysis', 'true', 'boolean', '是否启用情感分析');
```

---

*数据库设计文档版本：v1.0*  
*创建时间：2025-01-28*  
*最后更新：2025-01-28*
