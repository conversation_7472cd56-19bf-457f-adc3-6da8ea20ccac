// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 钉钉信息
  unionId   String  @unique
  openId    String?
  userId    String? // 钉钉企业内用户ID

  // 基本信息
  name      String
  nick      String?
  avatar    String?
  mobile    String?
  email     String?
  stateCode String? @default("+86")

  // 职业信息
  jobNumber String?
  title     String?
  workPlace String?
  hiredDate String?
  remark    String?

  // 状态信息
  active     Boolean @default(true)
  admin      <PERSON> @default(false)
  boss       <PERSON><PERSON><PERSON> @default(false)
  senior     <PERSON><PERSON><PERSON> @default(false)
  realAuthed <PERSON><PERSON><PERSON> @default(false)

  // 关联
  departments UserDepartment[]
  roles       UserRole[]
  sessions    UserSession[]

  // AI客服相关关联
  conversations     AiConversation[]
  knowledgeItems    KnowledgeBase[]
  knowledgeVersions KbVersion[]
  knowledgeTags     KbTag[]
  feedback          UserFeedback[]
  processedFeedback UserFeedback[] @relation("ProcessedFeedback")

  @@map("users")
}

// 部门表
model Department {
  id       Int    @id @default(autoincrement())
  deptId   Int    @unique
  name     String
  parentId Int?
  order    Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  users UserDepartment[]

  @@map("departments")
}

// 角色表
model Role {
  id        String @id @default(cuid())
  roleId    String @unique
  roleName  String
  groupName String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  users UserRole[]

  @@map("roles")
}

// 用户部门关联表
model UserDepartment {
  id     String @id @default(cuid())
  userId String
  deptId Int

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  department Department @relation(fields: [deptId], references: [deptId], onDelete: Cascade)

  @@unique([userId, deptId])
  @@map("user_departments")
}

// 用户角色关联表
model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [roleId], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// 用户会话表
model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// AI客服系统相关模型

model AiConversation {
  id                String   @id @default(cuid())
  userId            String?  @map("user_id")
  sessionId         String   @map("session_id")
  title             String
  status            ConversationStatus @default(ACTIVE)
  intent            String?
  sentiment         String?
  satisfactionScore Decimal? @map("satisfaction_score") @db.Decimal(3,2)
  totalMessages     Int      @default(0) @map("total_messages")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  user     User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
  messages AiMessage[]
  feedback UserFeedback[]

  @@index([userId, sessionId])
  @@index([status, createdAt])
  @@index([intent])
  @@map("ai_conversations")
}

model AiMessage {
  id               String   @id @default(cuid())
  conversationId   String   @map("conversation_id")
  role             MessageRole
  content          String   @db.Text
  intent           String?
  sentiment        String?
  confidenceScore  Decimal? @map("confidence_score") @db.Decimal(3,2)
  responseTimeMs   Int?     @map("response_time_ms")
  knowledgeUsed    Json?    @map("knowledge_used")
  metadata         Json?
  createdAt        DateTime @default(now()) @map("created_at")

  conversation AiConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  feedback     UserFeedback[]

  @@index([conversationId, createdAt])
  @@index([role, createdAt])
  @@index([intent])
  @@map("ai_messages")
}

model KnowledgeBase {
  id         String   @id @default(cuid())
  title      String   @db.VarChar(500)
  content    String   @db.LongText
  summary    String?  @db.Text
  categoryId String?  @map("category_id")
  tagsJson   Json?    @map("tags_json")
  embedding  Json?
  embeddingModel String? @map("embedding_model") @db.VarChar(100)
  embeddingVersion String? @map("embedding_version") @db.VarChar(50)
  embeddingUpdatedAt DateTime? @map("embedding_updated_at")
  status     KnowledgeStatus @default(DRAFT)
  priority   Int      @default(0)
  viewCount  Int      @default(0) @map("view_count")
  useCount   Int      @default(0) @map("use_count")
  createdBy  String   @map("created_by")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  category  KbCategory?        @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  creator   User               @relation(fields: [createdBy], references: [id], onDelete: Restrict)
  versions  KbVersion[]
  tags      KnowledgeBaseTag[]

  @@index([categoryId, status])
  @@index([status, priority(sort: Desc)])
  @@index([createdBy])
  @@index([useCount(sort: Desc)])
  @@index([status, embeddingModel])
  @@fulltext([title, content, summary])
  @@map("knowledge_base")
}

model KbCategory {
  id          String   @id @default(cuid())
  name        String   @db.VarChar(100)
  description String?  @db.Text
  parentId    String?  @map("parent_id")
  sortOrder   Int      @default(0) @map("sort_order")
  icon        String?  @db.VarChar(100)
  color       String?  @db.VarChar(20)
  status      CategoryStatus @default(ACTIVE)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  parent       KbCategory?     @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children     KbCategory[]    @relation("CategoryHierarchy")
  knowledgeItems KnowledgeBase[]

  @@unique([name, parentId])
  @@index([parentId, sortOrder])
  @@index([status])
  @@map("kb_categories")
}

model KbVersion {
  id            String   @id @default(cuid())
  knowledgeId   String   @map("knowledge_id")
  versionNumber Int      @map("version_number")
  title         String   @db.VarChar(500)
  content       String   @db.LongText
  changeLog     String?  @db.Text @map("change_log")
  createdBy     String   @map("created_by")
  createdAt     DateTime @default(now()) @map("created_at")

  knowledge KnowledgeBase @relation(fields: [knowledgeId], references: [id], onDelete: Cascade)
  creator   User          @relation(fields: [createdBy], references: [id], onDelete: Restrict)

  @@unique([knowledgeId, versionNumber])
  @@index([knowledgeId, createdAt(sort: Desc)])
  @@map("kb_versions")
}

// 知识库标签表
model KbTag {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(50)
  description String?  @db.Text
  color       String?  @db.VarChar(20)
  useCount    Int      @default(0) @map("use_count")
  createdBy   String   @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  creator           User                @relation(fields: [createdBy], references: [id], onDelete: Restrict)
  knowledgeBaseTags KnowledgeBaseTag[]

  @@index([useCount(sort: Desc)])
  @@index([name])
  @@map("kb_tags")
}

// 知识库与标签关联表
model KnowledgeBaseTag {
  id            String @id @default(cuid())
  knowledgeId   String @map("knowledge_id")
  tagId         String @map("tag_id")
  createdAt     DateTime @default(now()) @map("created_at")

  knowledge KnowledgeBase @relation(fields: [knowledgeId], references: [id], onDelete: Cascade)
  tag       KbTag         @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([knowledgeId, tagId])
  @@index([knowledgeId])
  @@index([tagId])
  @@map("knowledge_base_tags")
}

model UserFeedback {
  id             String   @id @default(cuid())
  conversationId String?  @map("conversation_id")
  messageId      String?  @map("message_id")
  userId         String?  @map("user_id")
  feedbackType   FeedbackType @map("feedback_type")
  rating         Int?
  content        String?  @db.Text
  status         FeedbackStatus @default(PENDING)
  processedBy    String?  @map("processed_by")
  processedAt    DateTime? @map("processed_at")
  createdAt      DateTime @default(now()) @map("created_at")

  conversation AiConversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)
  message      AiMessage?      @relation(fields: [messageId], references: [id], onDelete: SetNull)
  user         User?           @relation(fields: [userId], references: [id], onDelete: SetNull)
  processor    User?           @relation("ProcessedFeedback", fields: [processedBy], references: [id], onDelete: SetNull)

  @@index([conversationId])
  @@index([userId, createdAt(sort: Desc)])
  @@index([feedbackType, status])
  @@index([rating])
  @@map("user_feedback")
}

model ServiceAnalytics {
  id                      String   @id @default(cuid())
  date                    DateTime @db.Date
  hour                    Int?     @db.TinyInt
  totalConversations      Int      @default(0) @map("total_conversations")
  totalMessages           Int      @default(0) @map("total_messages")
  uniqueUsers             Int      @default(0) @map("unique_users")
  avgResponseTime         Decimal  @default(0) @map("avg_response_time") @db.Decimal(10,2)
  avgConversationLength   Decimal  @default(0) @map("avg_conversation_length") @db.Decimal(10,2)
  satisfactionScore       Decimal? @map("satisfaction_score") @db.Decimal(3,2)
  intentDistribution      Json?    @map("intent_distribution")
  sentimentDistribution   Json?    @map("sentiment_distribution")
  createdAt               DateTime @default(now()) @map("created_at")

  @@unique([date, hour])
  @@index([date])
  @@index([satisfactionScore(sort: Desc)])
  @@map("service_analytics")
}

model AiConfig {
  id           String      @id @default(cuid())
  configKey    String      @unique @map("config_key") @db.VarChar(100)
  configValue  String?     @map("config_value") @db.Text
  configType   ConfigType  @default(STRING) @map("config_type")
  description  String?     @db.Text
  isEncrypted  Boolean     @default(false) @map("is_encrypted")
  isActive     Boolean     @default(true) @map("is_active")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  @@index([configKey])
  @@index([isActive])
  @@map("ai_config")
}

// 枚举类型定义

enum ConversationStatus {
  ACTIVE  @map("active")
  CLOSED  @map("closed")
  ARCHIVED @map("archived")
}

enum MessageRole {
  USER      @map("user")
  ASSISTANT @map("assistant")
  SYSTEM    @map("system")
}

enum KnowledgeStatus {
  ACTIVE   @map("active")
  DRAFT    @map("draft")
  ARCHIVED @map("archived")
}

enum CategoryStatus {
  ACTIVE   @map("active")
  INACTIVE @map("inactive")
}

enum FeedbackType {
  LIKE       @map("like")
  DISLIKE    @map("dislike")
  REPORT     @map("report")
  SUGGESTION @map("suggestion")
}

enum FeedbackStatus {
  PENDING   @map("pending")
  PROCESSED @map("processed")
  CLOSED    @map("closed")
}

enum ConfigType {
  STRING  @map("string")
  NUMBER  @map("number")
  BOOLEAN @map("boolean")
  JSON    @map("json")
}
