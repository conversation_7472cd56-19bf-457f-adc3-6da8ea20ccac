'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Settings,
  Key,
  Brain,
  Save,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Loader2,
  RefreshCw,
  Smartphone,
  Wifi,
  WifiOff,
  TestTube,
  ExternalLink
} from 'lucide-react';

interface AiConfig {
  id: string;
  configKey: string;
  configValue: string;
  configType: string;
  description: string;
  isEncrypted: boolean;
  isActive: boolean;
}

interface AiModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  maxTokens: number;
  costPer1k: number;
  available?: boolean;
}

interface PlatformIntegration {
  id: string;
  name: string;
  type: string;
  icon: string;
  enabled: boolean;
  status: 'connected' | 'disconnected' | 'error' | 'testing';
  config: Record<string, unknown>;
  stats?: {
    todayMessages: number;
    totalMessages: number;
    avgResponseTime: number;
  };
}

export default function SettingsPage() {
  const [configs, setConfigs] = useState<AiConfig[]>([]);
  const [models, setModels] = useState<AiModel[]>([]);
  const [platforms, setPlatforms] = useState<PlatformIntegration[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingPlatforms, setLoadingPlatforms] = useState(false);
  const [testingPlatform, setTestingPlatform] = useState<string | null>(null);
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
  const [showPlatformSecrets, setShowPlatformSecrets] = useState<Record<string, boolean>>({});
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  // 表单状态
  const [formData, setFormData] = useState({
    deepseekApiKey: '',
    openaiApiKey: '',
    selectedModel: 'deepseek-chat',
    maxTokens: '2048',
    temperature: '0.7',
    enableLogging: true
  });

  useEffect(() => {
    loadConfigs();
    loadModels();
    loadPlatforms();
  }, []);

  const loadConfigs = async () => {
    try {
      const response = await fetch('/api/ai/config');
      const data = await response.json();
      
      if (data.success) {
        setConfigs(data.configs || []);
        
        // 填充表单数据
        const configMap = data.configs.reduce((acc: any, config: AiConfig) => {
          acc[config.configKey] = config.configValue;
          return acc;
        }, {});
        
        setFormData(prev => ({
          ...prev,
          deepseekApiKey: configMap.deepseek_api_key || '',
          openaiApiKey: configMap.openai_api_key || '',
          selectedModel: configMap.selected_model || 'deepseek-chat',
          maxTokens: configMap.max_tokens || '2048',
          temperature: configMap.temperature || '0.7',
          enableLogging: configMap.enable_logging === 'true'
        }));
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      setMessage({ type: 'error', text: '加载配置失败' });
    } finally {
      setLoading(false);
    }
  };

  const saveConfigs = async () => {
    setSaving(true);
    setMessage(null);
    
    try {
      const configsToSave = [
        { key: 'deepseek_api_key', value: formData.deepseekApiKey, type: 'string', encrypted: true },
        { key: 'openai_api_key', value: formData.openaiApiKey, type: 'string', encrypted: true },
        { key: 'selected_model', value: formData.selectedModel, type: 'string', encrypted: false },
        { key: 'max_tokens', value: formData.maxTokens, type: 'number', encrypted: false },
        { key: 'temperature', value: formData.temperature, type: 'number', encrypted: false },
        { key: 'enable_logging', value: formData.enableLogging.toString(), type: 'boolean', encrypted: false }
      ];

      const response = await fetch('/api/ai/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ configs: configsToSave })
      });

      const data = await response.json();
      
      if (data.success) {
        const savedItems = [];
        if (formData.deepseekApiKey) savedItems.push('DeepSeek API密钥');
        if (formData.openaiApiKey) savedItems.push('OpenAI API密钥');
        if (formData.selectedModel) savedItems.push('AI模型');
        savedItems.push('参数配置');

        setMessage({
          type: 'success',
          text: `✅ 保存成功！已更新：${savedItems.join('、')}`
        });
        await loadConfigs(); // 重新加载配置

        // 3秒后自动隐藏成功消息
        setTimeout(() => setMessage(null), 3000);
      } else {
        setMessage({ type: 'error', text: data.message || '保存失败' });
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      setMessage({ type: 'error', text: '保存配置失败' });
    } finally {
      setSaving(false);
    }
  };

  const loadModels = async () => {
    try {
      setLoadingModels(true);
      const response = await fetch('/api/ai/models');
      const data = await response.json();

      if (data.success) {
        setModels(data.models || []);
        console.log(`📋 加载了 ${data.models?.length || 0} 个AI模型`);
      } else {
        console.error('❌ 加载模型失败:', data.message);
        // 使用默认模型列表
        setModels([
          {
            id: 'deepseek-chat',
            name: 'DeepSeek Chat',
            provider: 'DeepSeek',
            description: '高性能对话模型，适合客服场景',
            maxTokens: 32768,
            costPer1k: 0.14,
            available: false
          },
          {
            id: 'gpt-3.5-turbo',
            name: 'GPT-3.5 Turbo',
            provider: 'OpenAI',
            description: '经典对话模型，响应快速',
            maxTokens: 4096,
            costPer1k: 1.5,
            available: false
          }
        ]);
      }
    } catch (error) {
      console.error('❌ 加载模型失败:', error);
    } finally {
      setLoadingModels(false);
    }
  };

  const loadPlatforms = async () => {
    try {
      setLoadingPlatforms(true);
      const response = await fetch('/api/platforms');
      const data = await response.json();

      if (data.success) {
        setPlatforms(data.platforms || []);
        console.log(`📱 加载了 ${data.platforms?.length || 0} 个平台集成`);
      } else {
        console.error('❌ 加载平台失败:', data.message);
        setMessage({ type: 'error', text: '加载平台配置失败' });
      }
    } catch (error) {
      console.error('❌ 加载平台失败:', error);
      setMessage({ type: 'error', text: '加载平台配置失败' });
    } finally {
      setLoadingPlatforms(false);
    }
  };

  const refreshModels = async () => {
    await loadModels();
    setMessage({ type: 'success', text: '✅ 模型列表已刷新' });
    // 2秒后自动隐藏消息
    setTimeout(() => setMessage(null), 2000);
  };

  const refreshPlatforms = async () => {
    await loadPlatforms();
    setMessage({ type: 'success', text: '✅ 平台列表已刷新' });
    setTimeout(() => setMessage(null), 2000);
  };

  const toggleApiKeyVisibility = (key: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const testConnection = async (provider: string) => {
    try {
      // 获取当前输入的API密钥
      let apiKey = '';
      if (provider === 'deepseek') {
        apiKey = formData.deepseekApiKey;
      } else if (provider === 'openai') {
        apiKey = formData.openaiApiKey;
      }

      if (!apiKey) {
        setMessage({ type: 'error', text: `请先输入 ${provider} API密钥` });
        return;
      }

      const response = await fetch('/api/ai/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider,
          apiKey // 直接传递当前输入的密钥
        })
      });

      const data = await response.json();
      setMessage({
        type: data.success ? 'success' : 'error',
        text: data.message
      });
    } catch (error) {
      setMessage({ type: 'error', text: '连接测试失败' });
    }
  };

  const testPlatformConnection = async (platformId: string, config: Record<string, unknown>) => {
    try {
      setTestingPlatform(platformId);

      const response = await fetch('/api/platforms', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ platformId, config })
      });

      const data = await response.json();
      setMessage({
        type: data.success ? 'success' : 'error',
        text: data.message
      });

      // 更新平台状态
      if (data.success) {
        setPlatforms(prev => prev.map(p =>
          p.id === platformId
            ? { ...p, status: 'connected' as const }
            : p
        ));
      }
    } catch (error) {
      setMessage({ type: 'error', text: '平台连接测试失败' });
    } finally {
      setTestingPlatform(null);
    }
  };

  const togglePlatformSecret = (platformId: string, field: string) => {
    const key = `${platformId}-${field}`;
    setShowPlatformSecrets(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const updatePlatformConfig = async (platformId: string, config: Record<string, unknown>, enabled: boolean) => {
    try {
      const response = await fetch('/api/platforms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ platformId, config, enabled })
      });

      const data = await response.json();

      if (data.success) {
        setMessage({ type: 'success', text: data.message });
        await loadPlatforms(); // 重新加载平台配置
      } else {
        setMessage({ type: 'error', text: data.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: '更新平台配置失败' });
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载配置中...</span>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">⚙️ 系统设置</h1>
            <p className="text-muted-foreground mt-2">
              配置AI大模型和系统参数
            </p>
          </div>
        </div>

        {/* 消息提示 */}
        {message && (
          <Alert className={
            message.type === 'success'
              ? 'border-green-200 bg-green-50 shadow-md'
              : 'border-red-200 bg-red-50 shadow-md'
          }>
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            <AlertDescription className={
              message.type === 'success'
                ? 'text-green-800 font-medium'
                : 'text-red-800 font-medium'
            }>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="ai-models" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="ai-models" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI模型配置
            </TabsTrigger>
            <TabsTrigger value="platforms" className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              平台集成
            </TabsTrigger>
            <TabsTrigger value="api-keys" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              API密钥管理
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              系统参数
            </TabsTrigger>
          </TabsList>

          {/* AI模型配置 */}
          <TabsContent value="ai-models" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>🤖 选择AI模型</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshModels}
                    disabled={loadingModels}
                  >
                    {loadingModels ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        刷新中...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        刷新模型
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  {models.map((model) => (
                    <div
                      key={model.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        formData.selectedModel === model.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, selectedModel: model.id }))}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{model.name}</h3>
                            <Badge variant="outline">{model.provider}</Badge>
                            {model.available ? (
                              <Badge variant="secondary" className="bg-green-100 text-green-800">可用</Badge>
                            ) : (
                              <Badge variant="secondary" className="bg-gray-100 text-gray-600">未配置</Badge>
                            )}
                            {formData.selectedModel === model.id && (
                              <Badge variant="default">当前选择</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {model.description}
                          </p>
                          <div className="flex gap-4 mt-2 text-xs text-muted-foreground">
                            <span>最大令牌: {model.maxTokens.toLocaleString()}</span>
                            <span>成本: ¥{model.costPer1k}/1K tokens</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 平台集成配置 */}
          <TabsContent value="platforms" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>📱 多平台集成管理</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshPlatforms}
                    disabled={loadingPlatforms}
                  >
                    {loadingPlatforms ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        刷新中...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        刷新状态
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {platforms.map((platform) => (
                  <div
                    key={platform.id}
                    className="p-6 border rounded-lg space-y-4"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{platform.icon}</span>
                        <div>
                          <h3 className="font-medium">{platform.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {platform.type === 'wechat' && '微信公众号自动回复'}
                            {platform.type === 'dingtalk' && '钉钉群机器人'}
                            {platform.type === 'douyin' && '抖音评论自动回复'}
                            {platform.type === 'telegram' && 'Telegram机器人'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={platform.status === 'connected' ? 'default' : 'secondary'}
                          className={
                            platform.status === 'connected'
                              ? 'bg-green-100 text-green-800'
                              : platform.status === 'error'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-600'
                          }
                        >
                          {platform.status === 'connected' && <Wifi className="h-3 w-3 mr-1" />}
                          {platform.status === 'disconnected' && <WifiOff className="h-3 w-3 mr-1" />}
                          {platform.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                          {platform.status === 'testing' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                          {platform.status === 'connected' ? '已连接' :
                           platform.status === 'error' ? '连接错误' :
                           platform.status === 'testing' ? '测试中' : '未连接'}
                        </Badge>
                        {platform.enabled && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700">
                            已启用
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* 统计信息 */}
                    {platform.stats && (
                      <div className="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <div className="text-lg font-semibold">{platform.stats.todayMessages}</div>
                          <div className="text-xs text-muted-foreground">今日消息</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold">{platform.stats.totalMessages}</div>
                          <div className="text-xs text-muted-foreground">总消息数</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold">{platform.stats.avgResponseTime}ms</div>
                          <div className="text-xs text-muted-foreground">平均响应</div>
                        </div>
                      </div>
                    )}

                    {/* 重要提示 */}
                    {platform.type === 'wechat' && (
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                          <div className="text-sm">
                            <div className="font-medium text-yellow-800 mb-2">⚠️ 微信公众号配置重要提醒</div>
                            <ul className="text-yellow-700 space-y-1 list-disc list-inside">
                              <li><strong>IP白名单</strong>：必须在微信公众平台添加服务器IP到白名单</li>
                              <li><strong>AppSecret安全</strong>：如长期不用可在公众平台冻结AppSecret</li>
                              <li><strong>域名要求</strong>：生产环境需要公网可访问的HTTPS域名</li>
                              <li><strong>开发测试</strong>：本地开发需要使用内网穿透工具（如ngrok）</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* 配置表单 */}
                    <PlatformConfigForm
                      platform={platform}
                      onTest={(config) => testPlatformConnection(platform.id, config)}
                      onSave={(config, enabled) => updatePlatformConfig(platform.id, config, enabled)}
                      testing={testingPlatform === platform.id}
                      showSecrets={showPlatformSecrets}
                      onToggleSecret={(field) => togglePlatformSecret(platform.id, field)}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* API密钥管理 */}
          <TabsContent value="api-keys" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>🔑 API密钥配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* DeepSeek API Key */}
                <div className="space-y-2">
                  <Label htmlFor="deepseek-key">DeepSeek API Key</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        id="deepseek-key"
                        type={showApiKeys.deepseek ? 'text' : 'password'}
                        value={formData.deepseekApiKey}
                        onChange={(e) => setFormData(prev => ({ ...prev, deepseekApiKey: e.target.value }))}
                        placeholder="sk-..."
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                        onClick={() => toggleApiKeyVisibility('deepseek')}
                      >
                        {showApiKeys.deepseek ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => testConnection('deepseek')}
                      disabled={!formData.deepseekApiKey}
                    >
                      测试连接
                    </Button>
                  </div>
                </div>

                {/* OpenAI API Key */}
                <div className="space-y-2">
                  <Label htmlFor="openai-key">OpenAI API Key</Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        id="openai-key"
                        type={showApiKeys.openai ? 'text' : 'password'}
                        value={formData.openaiApiKey}
                        onChange={(e) => setFormData(prev => ({ ...prev, openaiApiKey: e.target.value }))}
                        placeholder="sk-..."
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                        onClick={() => toggleApiKeyVisibility('openai')}
                      >
                        {showApiKeys.openai ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => testConnection('openai')}
                      disabled={!formData.openaiApiKey}
                    >
                      测试连接
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 系统参数 */}
          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>🔧 系统参数</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="max-tokens">最大令牌数</Label>
                    <Input
                      id="max-tokens"
                      type="number"
                      value={formData.maxTokens}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxTokens: e.target.value }))}
                      min="100"
                      max="32768"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="temperature">创造性 (Temperature)</Label>
                    <Input
                      id="temperature"
                      type="number"
                      step="0.1"
                      value={formData.temperature}
                      onChange={(e) => setFormData(prev => ({ ...prev, temperature: e.target.value }))}
                      min="0"
                      max="2"
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="enable-logging"
                    checked={formData.enableLogging}
                    onChange={(e) => setFormData(prev => ({ ...prev, enableLogging: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="enable-logging">启用对话日志记录</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 保存按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            {formData.selectedModel && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span>当前模型: {formData.selectedModel}</span>
              </div>
            )}
            {(formData.deepseekApiKey || formData.openaiApiKey) && (
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                <span>API密钥已配置</span>
              </div>
            )}
          </div>
          <Button onClick={saveConfigs} disabled={saving} size="lg">
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                保存配置
              </>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}

/**
 * 平台配置表单组件
 */
interface PlatformConfigFormProps {
  platform: PlatformIntegration;
  onTest: (config: Record<string, unknown>) => void;
  onSave: (config: Record<string, unknown>, enabled: boolean) => void;
  testing: boolean;
  showSecrets: Record<string, boolean>;
  onToggleSecret: (field: string) => void;
}

function PlatformConfigForm({
  platform,
  onTest,
  onSave,
  testing,
  showSecrets,
  onToggleSecret
}: PlatformConfigFormProps) {
  const [config, setConfig] = useState(platform.config);
  const [enabled, setEnabled] = useState(platform.enabled);

  const updateConfig = (key: string, value: string) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleTest = () => {
    onTest(config);
  };

  const handleSave = () => {
    onSave(config, enabled);
  };

  const renderConfigFields = () => {
    switch (platform.type) {
      case 'wechat':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  App ID
                  <span className="text-xs text-red-500">*必填</span>
                </Label>
                <Input
                  value={config.appId as string || ''}
                  onChange={(e) => updateConfig('appId', e.target.value)}
                  placeholder="wx240c1390bc2ce317"
                />
                <p className="text-xs text-gray-500">
                  在微信公众平台"开发-基本配置"中获取
                </p>
              </div>
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  App Secret
                  <span className="text-xs text-red-500">*必填</span>
                </Label>
                <div className="relative">
                  <Input
                    type={showSecrets[`${platform.id}-appSecret`] ? 'text' : 'password'}
                    value={config.appSecret as string || ''}
                    onChange={(e) => updateConfig('appSecret', e.target.value)}
                    placeholder="请输入AppSecret"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                    onClick={() => onToggleSecret('appSecret')}
                  >
                    {showSecrets[`${platform.id}-appSecret`] ?
                      <EyeOff className="h-4 w-4" /> :
                      <Eye className="h-4 w-4" />
                    }
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  在微信公众平台"开发-基本配置"中获取，注意保密
                </p>
              </div>
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  Token
                  <span className="text-xs text-red-500">*必填</span>
                </Label>
                <Input
                  value={config.webhookSecret as string || ''}
                  onChange={(e) => updateConfig('webhookSecret', e.target.value)}
                  placeholder="wuduqiaojie2025token"
                />
                <p className="text-xs text-gray-500">
                  自定义Token，3-32个字符，只能包含英文和数字
                </p>
              </div>
              <div className="space-y-2">
                <Label>Webhook URL</Label>
                <div className="flex gap-2">
                  <Input
                    value={config.webhookUrl as string || ''}
                    readOnly
                    className="bg-gray-50"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => navigator.clipboard.writeText(config.webhookUrl as string)}
                    title="复制URL"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  将此URL配置到微信公众平台"开发-基本配置-服务器配置"中
                </p>
              </div>
            </div>

            {/* 配置步骤说明 */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm">
                <div className="font-medium text-blue-800 mb-2">📋 配置步骤</div>
                <ol className="text-blue-700 space-y-1 list-decimal list-inside">
                  <li>在微信公众平台获取AppID和AppSecret</li>
                  <li>设置自定义Token（如：wuduqiaojie2025token）</li>
                  <li>将上方Webhook URL复制到微信公众平台</li>
                  <li>添加服务器IP到微信公众平台白名单</li>
                  <li>点击"测试连接"验证配置</li>
                </ol>
              </div>
            </div>
          </div>
        );

      case 'dingtalk':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>App Key</Label>
              <Input
                value={config.appKey as string || ''}
                onChange={(e) => updateConfig('appKey', e.target.value)}
                placeholder="dingoa..."
              />
            </div>
            <div className="space-y-2">
              <Label>App Secret</Label>
              <div className="relative">
                <Input
                  type={showSecrets[`${platform.id}-appSecret`] ? 'text' : 'password'}
                  value={config.appSecret as string || ''}
                  onChange={(e) => updateConfig('appSecret', e.target.value)}
                  placeholder="..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                  onClick={() => onToggleSecret('appSecret')}
                >
                  {showSecrets[`${platform.id}-appSecret`] ?
                    <EyeOff className="h-4 w-4" /> :
                    <Eye className="h-4 w-4" />
                  }
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Agent ID</Label>
              <Input
                value={config.agentId as string || ''}
                onChange={(e) => updateConfig('agentId', e.target.value)}
                placeholder="应用AgentId"
              />
            </div>
          </div>
        );

      case 'douyin':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>App ID</Label>
              <Input
                value={config.appId as string || ''}
                onChange={(e) => updateConfig('appId', e.target.value)}
                placeholder="抖音应用ID"
              />
            </div>
            <div className="space-y-2">
              <Label>App Secret</Label>
              <div className="relative">
                <Input
                  type={showSecrets[`${platform.id}-appSecret`] ? 'text' : 'password'}
                  value={config.appSecret as string || ''}
                  onChange={(e) => updateConfig('appSecret', e.target.value)}
                  placeholder="..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                  onClick={() => onToggleSecret('appSecret')}
                >
                  {showSecrets[`${platform.id}-appSecret`] ?
                    <EyeOff className="h-4 w-4" /> :
                    <Eye className="h-4 w-4" />
                  }
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Webhook Secret</Label>
              <Input
                value={config.webhookSecret as string || ''}
                onChange={(e) => updateConfig('webhookSecret', e.target.value)}
                placeholder="Webhook验证密钥"
              />
            </div>
          </div>
        );

      case 'telegram':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Bot Token</Label>
              <div className="relative">
                <Input
                  type={showSecrets[`${platform.id}-botToken`] ? 'text' : 'password'}
                  value={config.botToken as string || ''}
                  onChange={(e) => updateConfig('botToken', e.target.value)}
                  placeholder="123456:ABC-DEF..."
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6"
                  onClick={() => onToggleSecret('botToken')}
                >
                  {showSecrets[`${platform.id}-botToken`] ?
                    <EyeOff className="h-4 w-4" /> :
                    <Eye className="h-4 w-4" />
                  }
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Webhook Secret</Label>
              <Input
                value={config.webhookSecret as string || ''}
                onChange={(e) => updateConfig('webhookSecret', e.target.value)}
                placeholder="可选的Webhook密钥"
              />
            </div>
          </div>
        );

      default:
        return <div>暂不支持此平台配置</div>;
    }
  };

  return (
    <div className="space-y-4">
      {renderConfigFields()}

      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={`enable-${platform.id}`}
            checked={enabled}
            onChange={(e) => setEnabled(e.target.checked)}
            className="rounded"
          />
          <Label htmlFor={`enable-${platform.id}`}>启用此平台集成</Label>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleTest}
            disabled={testing}
          >
            {testing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                测试中...
              </>
            ) : (
              <>
                <TestTube className="h-4 w-4 mr-2" />
                测试连接
              </>
            )}
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            保存配置
          </Button>
        </div>
      </div>
    </div>
  );
}
