# Webhook技术实现详细方案

## 🔧 核心接口设计

### 统一消息接口
```typescript
// src/types/webhook.ts
export interface UnifiedMessage {
  id: string;
  platform: 'wechat' | 'douyin' | 'telegram';
  userId: string;
  content: string;
  messageType: 'text' | 'image' | 'voice' | 'video';
  sessionId: string;
  timestamp: number;
  metadata: {
    original: any;
    chatId?: string;
    groupId?: string;
    videoId?: string; // 抖音评论场景
  };
}

export interface WebhookResponse {
  success: boolean;
  reply?: string;
  error?: string;
  metadata?: any;
}
```

### 平台适配器基类
```typescript
// src/lib/webhook/adapters/base.ts
export abstract class PlatformAdapter {
  abstract platform: string;
  abstract parseWebhook(request: Request): Promise<UnifiedMessage>;
  abstract sendReply(message: UnifiedMessage, reply: string): Promise<boolean>;
  abstract verifySignature(request: Request): Promise<boolean>;
  
  protected generateSessionId(userId: string): string {
    return `${this.platform}:${userId}`;
  }
}
```

## 📱 微信公众号实现

### 微信适配器
```typescript
// src/lib/webhook/adapters/wechat.ts
import xml2js from 'xml2js';
import crypto from 'crypto';

export class WeChatAdapter extends PlatformAdapter {
  platform = 'wechat';
  
  async verifySignature(request: Request): Promise<boolean> {
    const signature = request.headers.get('x-signature');
    const timestamp = request.headers.get('x-timestamp');
    const nonce = request.headers.get('x-nonce');
    
    if (!signature || !timestamp || !nonce) return false;
    
    const token = process.env.WECHAT_TOKEN!;
    const tmpArr = [token, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const sha1 = crypto.createHash('sha1').update(tmpStr).digest('hex');
    
    return sha1 === signature;
  }
  
  async parseWebhook(request: Request): Promise<UnifiedMessage> {
    const xmlBody = await request.text();
    const parser = new xml2js.Parser();
    const result = await parser.parseStringPromise(xmlBody);
    const msg = result.xml;
    
    return {
      id: msg.MsgId[0],
      platform: this.platform,
      userId: msg.FromUserName[0],
      content: msg.Content[0],
      messageType: 'text',
      sessionId: this.generateSessionId(msg.FromUserName[0]),
      timestamp: parseInt(msg.CreateTime[0]) * 1000,
      metadata: {
        original: msg,
        chatId: msg.ToUserName[0]
      }
    };
  }
  
  async sendReply(message: UnifiedMessage, reply: string): Promise<boolean> {
    // 微信公众号被动回复消息（XML格式）
    const replyXml = `
      <xml>
        <ToUserName><![CDATA[${message.userId}]]></ToUserName>
        <FromUserName><![CDATA[${message.metadata.chatId}]]></FromUserName>
        <CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
        <MsgType><![CDATA[text]]></MsgType>
        <Content><![CDATA[${reply}]]></Content>
      </xml>
    `;
    
    return true; // 微信使用被动回复
  }
}
```

### 微信Webhook路由
```typescript
// app/api/webhooks/wechat/route.ts
import { WeChatAdapter } from '@/lib/webhook/adapters/wechat';
import { MessageProcessor } from '@/lib/webhook/message-processor';

const adapter = new WeChatAdapter();
const processor = new MessageProcessor();

export async function GET(request: Request) {
  // 微信验证URL有效性
  const url = new URL(request.url);
  const signature = url.searchParams.get('signature');
  const echostr = url.searchParams.get('echostr');
  
  if (await adapter.verifySignature(request)) {
    return new Response(echostr);
  }
  
  return new Response('Unauthorized', { status: 401 });
}

export async function POST(request: Request) {
  try {
    if (!await adapter.verifySignature(request)) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    const message = await adapter.parseWebhook(request);
    const reply = await processor.process(message);
    const replyXml = await adapter.sendReply(message, reply);
    
    return new Response(replyXml, {
      headers: { 'Content-Type': 'application/xml' }
    });
  } catch (error) {
    console.error('WeChat webhook error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
```

## 🎵 抖音平台实现

### 抖音适配器
```typescript
// src/lib/webhook/adapters/douyin.ts
export class DouyinAdapter extends PlatformAdapter {
  platform = 'douyin';
  
  async verifySignature(request: Request): Promise<boolean> {
    const signature = request.headers.get('x-douyin-signature');
    const timestamp = request.headers.get('x-douyin-timestamp');
    const body = await request.text();
    
    if (!signature || !timestamp) return false;
    
    const secret = process.env.DOUYIN_WEBHOOK_SECRET!;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(timestamp + body)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }
  
  async parseWebhook(request: Request): Promise<UnifiedMessage> {
    const body = await request.json();
    const { event, data } = body;
    
    if (event === 'comment.create') {
      return {
        id: data.comment_id,
        platform: this.platform,
        userId: data.user_id,
        content: data.content,
        messageType: 'text',
        sessionId: this.generateSessionId(data.user_id),
        timestamp: data.create_time * 1000,
        metadata: {
          original: data,
          videoId: data.video_id
        }
      };
    }
    
    throw new Error(`Unsupported event type: ${event}`);
  }
  
  async sendReply(message: UnifiedMessage, reply: string): Promise<boolean> {
    const response = await fetch('https://open.douyin.com/api/comment/reply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DOUYIN_ACCESS_TOKEN}`
      },
      body: JSON.stringify({
        comment_id: message.id,
        content: reply
      })
    });
    
    return response.ok;
  }
}
```

## 🔄 消息处理器

### 核心处理逻辑
```typescript
// src/lib/webhook/message-processor.ts
export class MessageProcessor {
  async process(message: UnifiedMessage): Promise<string> {
    try {
      // 复用现有的RAG API
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: message.content,
          sessionId: message.sessionId,
          userId: message.userId,
          platform: message.platform
        })
      });
      
      if (!response.ok) {
        throw new Error(`RAG API error: ${response.status}`);
      }
      
      const result = await response.json();
      return result.assistantMessage?.content || '抱歉，我暂时无法回答这个问题。';
    } catch (error) {
      console.error('Message processing error:', error);
      return '系统暂时繁忙，请稍后再试。';
    }
  }
}
```

## 📊 数据库扩展

### Webhook消息记录表
```sql
CREATE TABLE webhook_messages (
  id VARCHAR(191) PRIMARY KEY,
  platform ENUM('wechat', 'douyin', 'telegram') NOT NULL,
  user_id VARCHAR(191) NOT NULL,
  content TEXT NOT NULL,
  message_type ENUM('text', 'image', 'voice', 'video') NOT NULL,
  session_id VARCHAR(191) NOT NULL,
  reply TEXT,
  status ENUM('pending', 'processed', 'failed') DEFAULT 'pending',
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_platform_user (platform, user_id),
  INDEX idx_session (session_id),
  INDEX idx_created_at (created_at)
);
```

### 平台配置表
```sql
CREATE TABLE platform_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  platform VARCHAR(50) NOT NULL UNIQUE,
  config JSON NOT NULL,
  is_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔧 环境变量配置

```env
# 微信公众号配置
WECHAT_TOKEN=your_wechat_token
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 抖音平台配置
DOUYIN_APP_ID=your_douyin_app_id
DOUYIN_APP_SECRET=your_douyin_app_secret
DOUYIN_WEBHOOK_SECRET=your_webhook_secret
DOUYIN_ACCESS_TOKEN=your_access_token

# Webhook安全
WEBHOOK_SECRET=your_global_webhook_secret
```

## 📈 监控和日志

### 监控服务
```typescript
// src/lib/webhook/monitor.ts
export class WebhookMonitor {
  static async logMessage(message: UnifiedMessage, reply: string, status: string) {
    await prisma.webhookMessage.create({
      data: {
        id: message.id,
        platform: message.platform,
        userId: message.userId,
        content: message.content,
        messageType: message.messageType,
        sessionId: message.sessionId,
        reply,
        status,
        metadata: message.metadata
      }
    });
  }
  
  static async getStats(platform?: string) {
    const where = platform ? { platform } : {};
    
    return await prisma.webhookMessage.groupBy({
      by: ['platform', 'status'],
      where,
      _count: true
    });
  }
}
```

---

*技术实现文档*
*版本：v1.0*
*更新时间：2025-01-28*
