/**
 * 向量搜索服务
 * 实现语义搜索、相似度计算和搜索结果优化
 */

import { PrismaClient } from '@prisma/client';
import { embeddingService } from './embedding-service';

const prisma = new PrismaClient();

// 搜索配置
interface SearchConfig {
  similarityThreshold: number;  // 相似度阈值 (0-1)
  maxResults: number;          // 最大结果数
  includeContent: boolean;     // 是否包含完整内容
  boostFactors: {             // 权重因子
    titleBoost: number;       // 标题权重
    categoryBoost: number;    // 分类权重
    priorityBoost: number;    // 优先级权重
    useCountBoost: number;    // 使用次数权重
  };
}

// 搜索结果
interface SearchResult {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  category?: string;
  tags: string[];
  similarity: number;
  relevanceScore: number;
  priority: number;
  useCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// 搜索响应
interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  searchTime: number;
  query: string;
  searchType: 'vector' | 'hybrid' | 'keyword';
  metadata: {
    similarityThreshold: number;
    vectorDimensions?: number;
    processingSteps: string[];
  };
}

export class VectorSearchService {
  private defaultConfig: SearchConfig = {
    similarityThreshold: 0.3, // 降低默认阈值，提高召回率
    maxResults: 10,
    includeContent: false,
    boostFactors: {
      titleBoost: 1.3, // 增加标题权重
      categoryBoost: 1.15, // 增加分类权重
      priorityBoost: 1.1, // 增加优先级权重
      useCountBoost: 1.05 // 增加使用次数权重
    }
  };

  /**
   * 向量搜索
   */
  async vectorSearch(
    query: string, 
    config: Partial<SearchConfig> = {}
  ): Promise<SearchResponse> {
    const startTime = Date.now();
    const searchConfig = { ...this.defaultConfig, ...config };
    const processingSteps: string[] = [];

    try {
      processingSteps.push('开始向量搜索');

      // 1. 生成查询向量
      processingSteps.push('生成查询向量');
      const queryEmbedding = await embeddingService.generateEmbedding(query);
      
      // 2. 获取所有有向量的知识条目
      processingSteps.push('获取知识库数据');
      const knowledgeItems = await prisma.knowledgeBase.findMany({
        where: {
          status: 'ACTIVE',
          embedding: { not: { equals: null } }
        },
        include: {
          category: true,
          tags: {
            include: {
              tag: true
            }
          }
        }
      });

      processingSteps.push(`找到 ${knowledgeItems.length} 个有向量的知识条目`);

      // 3. 计算相似度
      processingSteps.push('计算向量相似度');
      const similarities = knowledgeItems.map(item => {
        const itemEmbedding = item.embedding as number[];
        const similarity = this.cosineSimilarity(queryEmbedding.vector, itemEmbedding);
        
        return {
          item,
          similarity,
          relevanceScore: this.calculateRelevanceScore(item, similarity, searchConfig)
        };
      });

      // 4. 过滤和排序
      processingSteps.push('过滤和排序结果');
      const filteredResults = similarities
        .filter(result => result.similarity >= searchConfig.similarityThreshold)
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, searchConfig.maxResults);

      // 5. 格式化结果
      processingSteps.push('格式化搜索结果');
      const results: SearchResult[] = filteredResults.map(result => ({
        id: result.item.id,
        title: result.item.title,
        content: searchConfig.includeContent ? result.item.content : undefined,
        summary: result.item.summary || undefined,
        category: result.item.category?.name,
        tags: result.item.tags.map(t => t.tag.name),
        similarity: Math.round(result.similarity * 1000) / 1000,
        relevanceScore: Math.round(result.relevanceScore * 1000) / 1000,
        priority: result.item.priority,
        useCount: result.item.useCount,
        createdAt: result.item.createdAt,
        updatedAt: result.item.updatedAt
      }));

      const searchTime = Date.now() - startTime;
      processingSteps.push(`搜索完成，耗时 ${searchTime}ms`);

      console.log(`🔍 向量搜索完成: 查询="${query}", 结果=${results.length}个, 耗时=${searchTime}ms`);

      return {
        results,
        totalCount: results.length,
        searchTime,
        query,
        searchType: 'vector',
        metadata: {
          similarityThreshold: searchConfig.similarityThreshold,
          vectorDimensions: queryEmbedding.dimensions,
          processingSteps
        }
      };

    } catch (error) {
      console.error('❌ 向量搜索失败:', error);
      throw error;
    }
  }

  /**
   * 混合搜索（向量搜索 + 关键词搜索）
   */
  async hybridSearch(
    query: string,
    config: Partial<SearchConfig> = {}
  ): Promise<SearchResponse> {
    const startTime = Date.now();
    const processingSteps: string[] = [];

    try {
      processingSteps.push('开始混合搜索');

      // 1. 向量搜索
      const vectorResults = await this.vectorSearch(query, {
        ...config,
        maxResults: Math.ceil((config.maxResults || this.defaultConfig.maxResults) * 0.7)
      });

      // 2. 关键词搜索
      processingSteps.push('执行关键词搜索');
      const keywordResults = await this.keywordSearch(query, {
        ...config,
        maxResults: Math.ceil((config.maxResults || this.defaultConfig.maxResults) * 0.5)
      });

      // 3. 合并和去重
      processingSteps.push('合并搜索结果');
      const combinedResults = this.mergeSearchResults(
        vectorResults.results,
        keywordResults,
        config.maxResults || this.defaultConfig.maxResults
      );

      const searchTime = Date.now() - startTime;
      processingSteps.push(`混合搜索完成，耗时 ${searchTime}ms`);

      return {
        results: combinedResults,
        totalCount: combinedResults.length,
        searchTime,
        query,
        searchType: 'hybrid',
        metadata: {
          similarityThreshold: config.similarityThreshold || this.defaultConfig.similarityThreshold,
          vectorDimensions: vectorResults.metadata.vectorDimensions,
          processingSteps: [...vectorResults.metadata.processingSteps, ...processingSteps]
        }
      };

    } catch (error) {
      console.error('❌ 混合搜索失败:', error);
      throw error;
    }
  }

  /**
   * 查找相似条目
   */
  async findSimilarItems(
    itemId: string,
    config: Partial<SearchConfig> = {}
  ): Promise<SearchResponse> {
    const startTime = Date.now();
    const searchConfig = { ...this.defaultConfig, ...config };

    try {
      // 获取目标条目
      const targetItem = await prisma.knowledgeBase.findUnique({
        where: { id: itemId },
        include: {
          category: true,
          tags: { include: { tag: true } }
        }
      });

      if (!targetItem || !targetItem.embedding) {
        throw new Error('目标条目不存在或没有向量数据');
      }

      const targetEmbedding = targetItem.embedding as number[];

      // 获取其他条目
      const otherItems = await prisma.knowledgeBase.findMany({
        where: {
          status: 'ACTIVE',
          embedding: { not: { equals: null } },
          id: { not: itemId }
        },
        include: {
          category: true,
          tags: { include: { tag: true } }
        }
      });

      // 计算相似度
      const similarities = otherItems.map(item => {
        const itemEmbedding = item.embedding as number[];
        const similarity = this.cosineSimilarity(targetEmbedding, itemEmbedding);
        
        return {
          item,
          similarity,
          relevanceScore: this.calculateRelevanceScore(item, similarity, searchConfig)
        };
      });

      // 排序和过滤
      const results = similarities
        .filter(result => result.similarity >= searchConfig.similarityThreshold)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, searchConfig.maxResults)
        .map(result => ({
          id: result.item.id,
          title: result.item.title,
          content: searchConfig.includeContent ? result.item.content : undefined,
          summary: result.item.summary || undefined,
          category: result.item.category?.name,
          tags: result.item.tags.map(t => t.tag.name),
          similarity: Math.round(result.similarity * 1000) / 1000,
          relevanceScore: Math.round(result.relevanceScore * 1000) / 1000,
          priority: result.item.priority,
          useCount: result.item.useCount,
          createdAt: result.item.createdAt,
          updatedAt: result.item.updatedAt
        }));

      const searchTime = Date.now() - startTime;

      return {
        results,
        totalCount: results.length,
        searchTime,
        query: `相似于: ${targetItem.title}`,
        searchType: 'vector',
        metadata: {
          similarityThreshold: searchConfig.similarityThreshold,
          vectorDimensions: targetEmbedding.length,
          processingSteps: [`查找与条目 ${itemId} 相似的内容`]
        }
      };

    } catch (error) {
      console.error('❌ 相似条目搜索失败:', error);
      throw error;
    }
  }

  /**
   * 余弦相似度计算
   */
  private cosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('向量维度不匹配');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * 计算相关性评分
   */
  private calculateRelevanceScore(
    item: any,
    similarity: number,
    config: SearchConfig
  ): number {
    let score = similarity;

    // 1. 优先级权重
    if (item.priority > 5) {
      score *= config.boostFactors.priorityBoost;
    } else if (item.priority > 3) {
      score *= (config.boostFactors.priorityBoost - 0.05);
    }

    // 2. 使用次数权重
    if (item.useCount > 20) {
      score *= config.boostFactors.useCountBoost;
    } else if (item.useCount > 5) {
      score *= (config.boostFactors.useCountBoost - 0.02);
    }

    // 3. 分类权重（如果有分类）
    if (item.category?.name) {
      score *= config.boostFactors.categoryBoost;
    }

    // 4. 内容新鲜度权重
    const daysSinceUpdate = (Date.now() - new Date(item.updatedAt).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceUpdate < 30) {
      score *= 1.05; // 30天内更新的内容加权
    } else if (daysSinceUpdate > 365) {
      score *= 0.95; // 超过1年的内容降权
    }

    // 5. 内容长度权重（适中长度的内容更有价值）
    const contentLength = item.content?.length || 0;
    if (contentLength > 100 && contentLength < 2000) {
      score *= 1.02; // 适中长度的内容加权
    }

    return Math.min(score, 1.0); // 确保分数不超过1.0
  }

  /**
   * 关键词搜索（增强版）
   */
  private async keywordSearch(query: string, config: Partial<SearchConfig>): Promise<SearchResult[]> {
    // 1. 分词处理
    const keywords = this.extractKeywords(query);

    // 2. 构建搜索条件
    const searchConditions = [];

    // 精确匹配
    searchConditions.push(
      { title: { contains: query } },
      { content: { contains: query } },
      { summary: { contains: query } }
    );

    // 关键词匹配
    for (const keyword of keywords) {
      if (keyword.length > 1) { // 忽略单字符
        searchConditions.push(
          { title: { contains: keyword } },
          { content: { contains: keyword } },
          { summary: { contains: keyword } }
        );
      }
    }

    const items = await prisma.knowledgeBase.findMany({
      where: {
        status: 'ACTIVE',
        OR: searchConditions
      },
      include: {
        category: true,
        tags: { include: { tag: true } }
      },
      take: (config.maxResults || this.defaultConfig.maxResults) * 2 // 取更多结果用于排序
    });

    // 3. 计算关键词匹配分数并排序
    const scoredItems = items.map(item => {
      const keywordScore = this.calculateKeywordScore(item, query, keywords);
      return {
        id: item.id,
        title: item.title,
        content: config.includeContent ? item.content : undefined,
        summary: item.summary || undefined,
        category: item.category?.name,
        tags: item.tags.map(t => t.tag.name),
        similarity: keywordScore,
        relevanceScore: keywordScore,
        priority: item.priority,
        useCount: item.useCount,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      };
    });

    // 4. 排序并返回
    return scoredItems
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, config.maxResults || this.defaultConfig.maxResults);
  }

  /**
   * 提取关键词
   */
  private extractKeywords(query: string): string[] {
    // 简单的中文分词（可以后续集成更专业的分词库）
    const keywords = [];

    // 移除标点符号
    const cleaned = query.replace(/[，。！？；：""''（）【】\s]/g, ' ');

    // 按空格分割
    const words = cleaned.split(/\s+/).filter(word => word.length > 0);

    // 添加原始查询
    keywords.push(query);

    // 添加分词结果
    keywords.push(...words);

    // 添加常见组合
    if (query.includes('门票')) {
      keywords.push('门票', '价格', '购买', '优惠');
    }
    if (query.includes('开放')) {
      keywords.push('开放', '时间', '营业');
    }
    if (query.includes('交通')) {
      keywords.push('交通', '路线', '公交', '自驾');
    }

    return [...new Set(keywords)]; // 去重
  }

  /**
   * 计算关键词匹配分数
   */
  private calculateKeywordScore(item: any, originalQuery: string, keywords: string[]): number {
    let score = 0;
    let matches = 0;

    const title = item.title || '';
    const content = item.content || '';
    const summary = item.summary || '';

    // 1. 精确匹配原始查询
    if (title.includes(originalQuery)) {
      score += 0.8;
      matches++;
    }
    if (content.includes(originalQuery)) {
      score += 0.6;
      matches++;
    }
    if (summary.includes(originalQuery)) {
      score += 0.7;
      matches++;
    }

    // 2. 关键词匹配
    for (const keyword of keywords) {
      if (keyword === originalQuery) continue; // 避免重复计分

      if (title.includes(keyword)) {
        score += 0.4;
        matches++;
      }
      if (content.includes(keyword)) {
        score += 0.2;
        matches++;
      }
      if (summary.includes(keyword)) {
        score += 0.3;
        matches++;
      }
    }

    // 3. 标准化分数
    if (matches === 0) return 0;

    const normalizedScore = Math.min(score / keywords.length, 1.0);

    // 4. 应用权重因子
    return this.calculateRelevanceScore(item, normalizedScore, this.defaultConfig);
  }

  /**
   * 合并搜索结果
   */
  private mergeSearchResults(
    vectorResults: SearchResult[],
    keywordResults: SearchResult[],
    maxResults: number
  ): SearchResult[] {
    const resultMap = new Map<string, SearchResult>();

    // 添加向量搜索结果
    vectorResults.forEach(result => {
      resultMap.set(result.id, result);
    });

    // 添加关键词搜索结果，如果已存在则取较高的相关性分数
    keywordResults.forEach(result => {
      const existing = resultMap.get(result.id);
      if (existing) {
        existing.relevanceScore = Math.max(existing.relevanceScore, result.relevanceScore);
      } else {
        resultMap.set(result.id, result);
      }
    });

    // 排序并限制结果数量
    return Array.from(resultMap.values())
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, maxResults);
  }
}

// 单例实例
export const vectorSearchService = new VectorSearchService();
