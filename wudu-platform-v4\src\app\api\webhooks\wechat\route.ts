import { NextRequest, NextResponse } from 'next/server';
import { WeChatAdapter } from '@/lib/webhook/adapters/wechat';
import { MessageProcessor } from '@/lib/webhook/message-processor';

const adapter = new WeChatAdapter();
const processor = new MessageProcessor();

/**
 * 处理微信公众号URL验证（GET请求）
 * 微信服务器会发送GET请求来验证URL的有效性
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[WeChat Webhook] Received GET request for URL verification');
    
    const url = new URL(request.url);
    const signature = url.searchParams.get('signature');
    const timestamp = url.searchParams.get('timestamp');
    const nonce = url.searchParams.get('nonce');
    const echostr = url.searchParams.get('echostr');

    console.log('[WeChat Webhook] Verification parameters:', {
      signature: signature?.substring(0, 10) + '...',
      timestamp,
      nonce: nonce?.substring(0, 10) + '...',
      echostr: echostr?.substring(0, 10) + '...'
    });

    // 验证签名
    if (await adapter.verifySignature(request)) {
      console.log('[WeChat Webhook] Signature verification successful');
      return new NextResponse(echostr, { status: 200 });
    } else {
      console.error('[WeChat Webhook] Signature verification failed');
      return new NextResponse('Unauthorized', { status: 401 });
    }
  } catch (error) {
    console.error('[WeChat Webhook] GET request error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

/**
 * 处理微信公众号消息（POST请求）
 * 微信服务器会发送POST请求来推送用户消息
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('[WeChat Webhook] Received POST request for message processing');

    // 1. 验证签名
    if (!await adapter.verifySignature(request)) {
      console.error('[WeChat Webhook] Signature verification failed');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    console.log('[WeChat Webhook] Signature verification successful');

    // 2. 解析消息
    const message = await adapter.parseWebhook(request);
    
    console.log('[WeChat Webhook] Message parsed:', {
      messageId: message.id,
      userId: message.userId,
      platform: message.platform,
      messageType: message.messageType,
      contentPreview: message.content.substring(0, 50) + '...'
    });

    // 3. 处理特殊消息类型
    if (message.messageType !== 'text') {
      console.log('[WeChat Webhook] Non-text message, sending default reply');
      const defaultReply = '感谢您的消息！目前我只能处理文字消息，请发送文字描述您的问题。';
      const replyXml = adapter.generateReplyXML(message, defaultReply);
      
      return new NextResponse(replyXml, {
        status: 200,
        headers: { 'Content-Type': 'application/xml; charset=utf-8' }
      });
    }

    // 4. 处理事件消息
    if (message.content.startsWith('[')) {
      console.log('[WeChat Webhook] Event message detected');
      let eventReply = '';
      
      if (message.content.includes('[用户关注]')) {
        eventReply = '欢迎关注吴都乔街智能客服！🎉\n\n我是您的专属AI助手，可以为您提供：\n• 景区信息咨询\n• 门票购买指导\n• 交通路线查询\n• 开放时间查询\n\n请直接发送您的问题，我会尽快为您解答！';
      } else if (message.content.includes('[菜单点击]')) {
        eventReply = '感谢您的操作！请直接发送您想了解的问题，我会为您详细解答。';
      } else {
        eventReply = '感谢您的操作！有什么可以帮助您的吗？';
      }
      
      const replyXml = adapter.generateReplyXML(message, eventReply);
      
      return new NextResponse(replyXml, {
        status: 200,
        headers: { 'Content-Type': 'application/xml; charset=utf-8' }
      });
    }

    // 5. 调用RAG系统处理消息
    console.log('[WeChat Webhook] Processing message with RAG system');
    const reply = await processor.process(message);
    
    const processingTime = Date.now() - startTime;
    console.log('[WeChat Webhook] Message processed successfully:', {
      messageId: message.id,
      processingTime,
      replyLength: reply.length,
      replyPreview: reply.substring(0, 100) + '...'
    });

    // 6. 生成回复XML
    const replyXml = adapter.generateReplyXML(message, reply);

    // 7. 返回XML响应
    return new NextResponse(replyXml, {
      status: 200,
      headers: { 
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    console.error('[WeChat Webhook] POST request error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      processingTime
    });

    // 返回错误回复
    try {
      const errorMessage = {
        id: `error_${Date.now()}`,
        platform: 'wechat' as const,
        userId: 'unknown',
        content: '',
        messageType: 'text' as const,
        sessionId: 'error',
        timestamp: Date.now(),
        metadata: { original: {}, chatId: 'unknown' }
      };

      const errorReply = '抱歉，系统暂时繁忙，请稍后再试。如有紧急问题，请联系人工客服。';
      const errorXml = adapter.generateReplyXML(errorMessage, errorReply);

      return new NextResponse(errorXml, {
        status: 200,
        headers: { 'Content-Type': 'application/xml; charset=utf-8' }
      });
    } catch (xmlError) {
      console.error('[WeChat Webhook] Failed to generate error XML:', xmlError);
      return new NextResponse('Internal Server Error', { status: 500 });
    }
  }
}

// 配置Next.js API路由
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
