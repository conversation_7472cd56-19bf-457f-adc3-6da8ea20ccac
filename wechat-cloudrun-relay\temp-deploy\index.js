const express = require('express');
const axios = require('axios');
const crypto = require('crypto');

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// 配置
const LOCAL_SERVER_URL = process.env.LOCAL_SERVER_URL || 'http://219.138.230.35:30002';
const LOCAL_API_PATH = '/api/wechat-message';
const WECHAT_TOKEN = process.env.WECHAT_TOKEN || 'wuduqiaojie2025token';

// 方案1：混合模式配置
const ASYNC_MODE = process.env.WECHAT_ASYNC_MODE === 'true';
const WECHAT_CUSTOMER_SERVICE_URL = 'https://api.weixin.qq.com/cgi-bin/message/custom/send';

// Access Token自动管理
const WECHAT_APPID = process.env.WECHAT_APPID || '';
const WECHAT_SECRET = process.env.WECHAT_SECRET || '';
let currentAccessToken = process.env.WECHAT_ACCESS_TOKEN || '';
let tokenExpireTime = 0;

// Access Token自动刷新管理
async function refreshAccessToken() {
  if (!WECHAT_APPID || !WECHAT_SECRET) {
    console.warn('[Token管理] 缺少APPID或SECRET，无法自动刷新Token');
    return false;
  }

  try {
    console.log('[Token管理] 开始刷新Access Token...');
    const response = await axios.get(
      `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}`,
      {
        timeout: 10000,
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false // 忽略SSL证书验证
        })
      }
    );

    if (response.data.access_token) {
      currentAccessToken = response.data.access_token;
      tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000; // 提前5分钟刷新
      console.log('[Token管理] ✅ Access Token刷新成功，有效期至:', new Date(tokenExpireTime).toLocaleString());
      return true;
    } else {
      console.error('[Token管理] ❌ 刷新失败:', response.data);
      return false;
    }
  } catch (error) {
    console.error('[Token管理] ❌ 刷新异常:', error.message);
    return false;
  }
}

// 获取当前有效的Access Token
function getCurrentAccessToken() {
  if (Date.now() > tokenExpireTime) {
    console.warn('[Token管理] ⚠️ Token已过期，尝试刷新...');
    refreshAccessToken(); // 异步刷新
  }
  return currentAccessToken;
}

console.log('[云托管中转] 启动配置:');
console.log('[云托管中转] 本地服务器地址:', LOCAL_SERVER_URL);
console.log('[云托管中转] API路径:', LOCAL_API_PATH);
console.log('[云托管中转] 微信Token:', WECHAT_TOKEN);
console.log('[云托管中转] 异步模式:', ASYNC_MODE ? '启用' : '禁用');
console.log('[云托管中转] APPID配置:', WECHAT_APPID ? '已配置' : '未配置');
console.log('[云托管中转] SECRET配置:', WECHAT_SECRET ? '已配置' : '未配置');

// 发送客服消息函数（使用动态Token）
async function sendCustomerServiceMessage(toUser, content) {
  const accessToken = getCurrentAccessToken();

  if (!accessToken) {
    console.error('[云托管中转] 缺少有效的Access Token，无法发送客服消息');
    return false;
  }

  try {
    const messageData = {
      touser: toUser,
      msgtype: 'text',
      text: {
        content: content
      }
    };

    const response = await axios.post(
      `${WECHAT_CUSTOMER_SERVICE_URL}?access_token=${accessToken}`,
      messageData,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000,
        httpsAgent: new (require('https').Agent)({
          rejectUnauthorized: false
        })
      }
    );

    console.log('[云托管中转] 客服消息发送结果:', response.data);

    // 如果Token过期，尝试刷新
    if (response.data.errcode === 40001 || response.data.errcode === 42001) {
      console.warn('[云托管中转] Token过期，尝试刷新后重试...');
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        // 重试发送
        const retryResponse = await axios.post(
          `${WECHAT_CUSTOMER_SERVICE_URL}?access_token=${currentAccessToken}`,
          messageData,
          {
            headers: { 'Content-Type': 'application/json' },
            timeout: 5000,
            httpsAgent: new (require('https').Agent)({
              rejectUnauthorized: false
            })
          }
        );
        console.log('[云托管中转] 重试发送结果:', retryResponse.data);
        return retryResponse.data.errcode === 0;
      }
    }

    return response.data.errcode === 0;
  } catch (error) {
    console.error('[云托管中转] 发送客服消息失败:', error.message);
    return false;
  }
}

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    mode: ASYNC_MODE ? 'async' : 'sync',
    hasAccessToken: !!currentAccessToken,
    tokenExpireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null,
    autoRefresh: !!(WECHAT_APPID && WECHAT_SECRET),
    localServer: LOCAL_SERVER_URL
  });
});

// Token管理接口
app.get('/admin/token-status', (req, res) => {
  res.json({
    hasToken: !!currentAccessToken,
    expireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null,
    isExpired: Date.now() > tokenExpireTime,
    autoRefreshEnabled: !!(WECHAT_APPID && WECHAT_SECRET),
    timeToExpire: tokenExpireTime ? Math.max(0, tokenExpireTime - Date.now()) : 0
  });
});

app.post('/admin/refresh-token', async (req, res) => {
  console.log('[Token管理] 手动刷新Token请求');
  const success = await refreshAccessToken();
  res.json({
    success,
    message: success ? 'Token刷新成功' : 'Token刷新失败',
    expireTime: tokenExpireTime ? new Date(tokenExpireTime).toISOString() : null
  });
});

// 微信服务器验证接口 (GET)
app.get('/wechat-message', (req, res) => {
  try {
    const { signature, timestamp, nonce, echostr } = req.query;

    console.log('[云托管中转] 收到微信验证请求:', { signature, timestamp, nonce, echostr });

    if (!signature || !timestamp || !nonce || !echostr) {
      console.error('[云托管中转] 验证参数不完整');
      return res.status(400).send('Bad Request');
    }

    // 验证签名
    const token = WECHAT_TOKEN;
    const tmpArr = [token, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const sha1 = crypto.createHash('sha1').update(tmpStr).digest('hex');

    console.log('[云托管中转] 签名验证:', {
      expected: sha1,
      received: signature,
      match: sha1 === signature
    });

    if (sha1 === signature) {
      console.log('[云托管中转] 验证成功，返回echostr:', echostr);
      res.send(echostr);
    } else {
      console.error('[云托管中转] 签名验证失败');
      res.status(401).send('Unauthorized');
    }

  } catch (error) {
    console.error('[云托管中转] 验证过程出错:', error);
    res.status(500).send('Internal Server Error');
  }
});

// 微信消息推送接收接口 - 方案1混合模式
app.post('/wechat-message', async (req, res) => {
  const startTime = Date.now();
  let hasResponded = false;

  if (ASYNC_MODE) {
    // 异步模式：立即返回success，启动异步处理
    console.log('[云托管中转] 异步模式：立即返回success');
    res.send('success');

    // 异步处理消息，不阻塞响应
    processMessageAsync(req.body, startTime).catch(error => {
      console.error('[云托管中转] 异步处理出现未捕获错误:', error);
    });
  } else {
    // 同步模式：保持原有逻辑
    console.log('[云托管中转] 同步模式：等待AI处理');

    // 设置4.5秒超时保护
    const timeout = setTimeout(() => {
      if (!hasResponded) {
        hasResponded = true;
        console.log('[云托管中转] 处理超时，返回success');
        res.send('success');
      }
    }, 4500);

    try {
      hasResponded = await processSyncMessage(req, res, timeout, hasResponded, startTime);
    } catch (error) {
      console.error('[云托管中转] 同步处理失败:', error);
      clearTimeout(timeout);
      if (!hasResponded) {
        res.send('success');
      }
    }
  }
});

// 异步处理消息函数 - 方案1实现
async function processMessageAsync(messageBody, startTime) {
  try {
    console.log('[云托管中转] 开始异步处理消息');

    // 检查是否是配置测试请求
    if (messageBody.action === 'CheckContainerPath') {
      console.log('[云托管中转] 配置测试请求，无需处理');
      return;
    }

    // 1. 立即发送临时回复（方案1的核心特性）
    const tempMessage = "🤔 正在为您查询，请稍候...";
    const tempSent = await sendCustomerServiceMessage(messageBody.FromUserName, tempMessage);
    if (tempSent) {
      console.log('[云托管中转] 临时回复发送成功');
    } else {
      console.warn('[云托管中转] 临时回复发送失败，但继续处理');
    }

    // 2. 构建转发数据
    const messageData = {
      toUserName: messageBody.ToUserName,
      fromUserName: messageBody.FromUserName,
      createTime: messageBody.CreateTime,
      msgType: messageBody.MsgType,
      content: messageBody.Content || messageBody.Text || '',
      msgId: messageBody.MsgId,
      originalData: messageBody
    };
    
    console.log('[云托管中转] 调用本地AI服务:', LOCAL_SERVER_URL + LOCAL_API_PATH);

    // 3. 调用本地AI服务（异步模式，无时间限制）
    const response = await axios.post(LOCAL_SERVER_URL + LOCAL_API_PATH, messageData, {
      timeout: 30000, // 30秒超时，因为是异步处理
      headers: {
        'Content-Type': 'application/json',
        'X-Wechat-Relay': 'true',
        'X-Async-Mode': 'true' // 标识异步模式
      }
    });

    console.log('[云托管中转] 本地AI服务响应:', response.status);

    // 4. 发送AI回复（通过客服API）
    if (response.data && response.data.reply) {
      const aiReply = response.data.reply;
      const success = await sendCustomerServiceMessage(messageBody.FromUserName, aiReply);

      if (success) {
        console.log('[云托管中转] AI回复发送成功:', aiReply.substring(0, 50) + '...');
      } else {
        console.error('[云托管中转] AI回复发送失败');
        // 发送错误提示
        await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，系统暂时繁忙，请稍后再试。');
      }
    } else {
      console.log('[云托管中转] 本地服务无回复内容');
      await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，我暂时无法理解您的问题，请联系人工客服。');
    }

    const totalTime = Date.now() - startTime;
    console.log(`[云托管中转] 异步处理完成，总耗时: ${totalTime}ms`);

    // 默认返回success
    if (!hasResponded) {
      hasResponded = true;
      res.send('success');
    }

  } catch (error) {
    console.error('[云托管中转] 异步处理失败:', error.message);

    // 发送错误回复
    try {
      await sendCustomerServiceMessage(messageBody.FromUserName, '抱歉，系统出现问题，请稍后再试或联系人工客服。');
    } catch (sendError) {
      console.error('[云托管中转] 发送错误回复也失败了:', sendError.message);
    }
  }
}

// 同步处理函数（保持向后兼容）
async function processSyncMessage(req, res, timeout, hasResponded, startTime) {
  try {
    console.log('[云托管中转] 收到微信消息:', JSON.stringify(req.body, null, 2));

    // 检查是否是配置测试请求
    if (req.body.action === 'CheckContainerPath') {
      console.log('[云托管中转] 配置测试请求');
      clearTimeout(timeout);
      if (!hasResponded) {
        res.send('success');
        return true;
      }
      return hasResponded;
    }

    // 构建转发数据
    const messageData = {
      toUserName: req.body.ToUserName,
      fromUserName: req.body.FromUserName,
      createTime: req.body.CreateTime,
      msgType: req.body.MsgType,
      content: req.body.Content || '',
      msgId: req.body.MsgId,
      originalData: req.body
    };

    console.log('[云托管中转] 转发消息到本地服务器:', LOCAL_SERVER_URL + LOCAL_API_PATH);

    // 转发到本地服务器
    const response = await axios.post(LOCAL_SERVER_URL + LOCAL_API_PATH, messageData, {
      timeout: 4000, // 4秒超时
      headers: {
        'Content-Type': 'application/json',
        'X-Wechat-Relay': 'true',
        'X-Source-IP': req.ip
      }
    });

    console.log('[云托管中转] 本地服务器响应:', response.status, response.data);

    clearTimeout(timeout);

    // 如果本地服务器返回了回复内容，则转发给微信
    if (response.data && response.data.reply && !hasResponded) {
      const replyData = response.data.reply;

      console.log('[云托管中转] 回复内容:', replyData);
      console.log('[云托管中转] 消息类型:', req.body.MsgType);

      // 构建被动回复消息的XML
      const replyXml = `<xml>
<ToUserName><![CDATA[${req.body.FromUserName}]]></ToUserName>
<FromUserName><![CDATA[${req.body.ToUserName}]]></FromUserName>
<CreateTime>${Math.floor(Date.now() / 1000)}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${replyData}]]></Content>
</xml>`;

      hasResponded = true;
      res.set('Content-Type', 'application/xml');
      res.send(replyXml);

      const totalTime = Date.now() - startTime;
      console.log(`[云托管中转] 消息处理完成，总耗时: ${totalTime}ms`);
    } else if (!hasResponded) {
      hasResponded = true;
      res.send('success');
      console.log('[云托管中转] 本地服务器无回复内容，返回success');
    }

    return hasResponded;

  } catch (error) {
    console.error('[云托管中转] 同步处理失败:', error.message);
    clearTimeout(timeout);
    if (!hasResponded) {
      res.send('success');
      hasResponded = true;
    }
    return hasResponded;
  }
}

// 错误处理
app.use((error, req, res, next) => {
  console.error('[云托管中转] 服务错误:', error);
  res.status(500).send('success'); // 对微信始终返回success
});

// 启动Token自动管理
async function initializeTokenManager() {
  if (WECHAT_APPID && WECHAT_SECRET) {
    console.log('[Token管理] 🔄 启动自动Token管理...');

    // 立即获取Token
    await refreshAccessToken();

    // 设置定时刷新（每90分钟）
    setInterval(async () => {
      console.log('[Token管理] ⏰ 定时刷新Token...');
      await refreshAccessToken();
    }, 90 * 60 * 1000); // 90分钟

    console.log('[Token管理] ✅ 自动Token管理已启动');
  } else if (ASYNC_MODE) {
    console.warn('[Token管理] ⚠️ 异步模式需要配置WECHAT_APPID和WECHAT_SECRET以启用自动Token管理');
  }
}

const PORT = process.env.PORT || 80;
app.listen(PORT, async () => {
  console.log(`[云托管中转] 方案1混合模式服务启动成功！`);
  console.log(`[云托管中转] 端口: ${PORT}`);
  console.log(`[云托管中转] 本地服务器: ${LOCAL_SERVER_URL}${LOCAL_API_PATH}`);
  console.log(`[云托管中转] 运行模式: ${ASYNC_MODE ? '异步模式' : '同步模式'}`);
  console.log(`[云托管中转] 微信Token: ${WECHAT_TOKEN ? '已配置' : '未配置'}`);
  console.log(`[云托管中转] 自动Token管理: ${(WECHAT_APPID && WECHAT_SECRET) ? '已启用' : '未启用'}`);

  // 初始化Token管理
  await initializeTokenManager();

  console.log(`[云托管中转] 🚀 方案1已就绪，支持立即回复+AI智能回答！`);
  console.log(`[云托管中转] 📋 管理接口: /health, /admin/token-status, /admin/refresh-token`);
});
