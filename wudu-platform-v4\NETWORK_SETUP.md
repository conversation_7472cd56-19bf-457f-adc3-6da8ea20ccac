# 网络配置指南

## 🎯 目标
让 `http://**************:30002/login` 可以从外网访问，以支持钉钉扫码登录。

## ✅ 当前状态
- ✅ 本地访问正常: `http://localhost:30002/login`
- ✅ 服务器监听正确: `0.0.0.0:30002`
- ❌ 外网访问失败: `http://**************:30002/login`

## 🔧 解决方案

### 方案1：Windows防火墙配置
```cmd
# 添加入站规则允许30002端口
netsh advfirewall firewall add rule name="Next.js Dev Server" dir=in action=allow protocol=TCP localport=30002

# 查看规则是否添加成功
netsh advfirewall firewall show rule name="Next.js Dev Server"
```

### 方案2：使用Windows防火墙图形界面
1. 打开"Windows Defender 防火墙"
2. 点击"高级设置"
3. 选择"入站规则" → "新建规则"
4. 选择"端口" → "TCP" → "特定本地端口" → 输入"30002"
5. 选择"允许连接"
6. 应用到所有配置文件
7. 命名规则为"Next.js Dev Server 30002"

### 方案3：临时关闭防火墙测试
```cmd
# 临时关闭防火墙（仅用于测试）
netsh advfirewall set allprofiles state off

# 测试完成后重新开启
netsh advfirewall set allprofiles state on
```

## 🧪 测试步骤

1. **配置防火墙后测试**:
   ```bash
   # 在浏览器中访问
   http://**************:30002/login
   ```

2. **使用curl测试**:
   ```bash
   curl -I http://**************:30002
   ```

3. **检查端口监听**:
   ```cmd
   netstat -an | findstr :30002
   ```

## 🎯 预期结果

配置成功后应该看到：
- ✅ 外网可以访问登录页面
- ✅ 钉钉二维码正常显示
- ✅ 钉钉扫码登录功能正常工作

## 📱 钉钉登录测试

外网访问正常后：
1. 访问 `http://**************:30002/login`
2. 等待钉钉二维码加载
3. 使用钉钉APP扫描二维码
4. 完成登录流程

## 🔍 故障排除

如果仍然无法访问：
1. 检查路由器端口转发设置
2. 检查云服务器安全组配置
3. 检查ISP是否阻止了该端口
4. 尝试使用其他端口（如8080、8000等）
