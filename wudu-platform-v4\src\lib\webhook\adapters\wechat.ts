import { PlatformAdapter } from './base';
import { UnifiedMessage } from '@/types/webhook';
import xml2js from 'xml2js';
import crypto from 'crypto';

/**
 * 微信公众号适配器
 * 处理微信公众号的webhook消息
 */
export class WeChatAdapter extends PlatformAdapter {
  readonly platform = 'wechat';

  /**
   * 验证微信签名
   * @param request HTTP请求对象
   * @returns 签名是否有效
   */
  async verifySignature(request: Request): Promise<boolean> {
    try {
      const url = new URL(request.url);
      const signature = url.searchParams.get('signature');
      const timestamp = url.searchParams.get('timestamp');
      const nonce = url.searchParams.get('nonce');

      if (!signature || !timestamp || !nonce) {
        this.logError('Missing signature parameters', { signature, timestamp, nonce });
        return false;
      }

      const token = process.env.WECHAT_TOKEN;
      if (!token) {
        this.logError('WECHAT_TOKEN not configured');
        return false;
      }

      // 微信签名验证算法
      const tmpArr = [token, timestamp, nonce].sort();
      const tmpStr = tmpArr.join('');
      const sha1 = crypto.createHash('sha1').update(tmpStr).digest('hex');

      const isValid = sha1 === signature;
      
      if (!isValid) {
        this.logError('Signature verification failed', {
          expected: sha1,
          received: signature,
          timestamp,
          nonce
        });
      }

      return isValid;
    } catch (error) {
      this.logError('Signature verification error', error);
      return false;
    }
  }

  /**
   * 解析微信XML消息
   * @param request HTTP请求对象
   * @returns 统一消息对象
   */
  async parseWebhook(request: Request): Promise<UnifiedMessage> {
    try {
      const xmlBody = await request.text();
      
      if (!xmlBody) {
        throw new Error('Empty request body');
      }

      this.logInfo('Parsing WeChat XML message', { 
        bodyLength: xmlBody.length,
        bodyPreview: xmlBody.substring(0, 200) + '...'
      });

      const parser = new xml2js.Parser({
        explicitArray: false,
        ignoreAttrs: true,
        trim: true
      });

      const result = await parser.parseStringPromise(xmlBody);
      const msg = result.xml;

      if (!msg) {
        throw new Error('Invalid XML structure');
      }

      // 验证必要字段
      if (!msg.FromUserName || !msg.ToUserName || !msg.CreateTime) {
        throw new Error('Missing required fields in WeChat message');
      }

      const messageType = this.mapWeChatMessageType(msg.MsgType);
      const content = this.extractMessageContent(msg);

      const unifiedMessage: UnifiedMessage = {
        id: msg.MsgId || `wechat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        platform: this.platform,
        userId: msg.FromUserName,
        content,
        messageType,
        sessionId: this.generateSessionId(msg.FromUserName),
        timestamp: parseInt(msg.CreateTime) * 1000,
        metadata: {
          original: msg,
          chatId: msg.ToUserName,
          wechatMsgType: msg.MsgType
        }
      };

      this.logInfo('WeChat message parsed successfully', {
        messageId: unifiedMessage.id,
        userId: unifiedMessage.userId,
        messageType: unifiedMessage.messageType,
        contentLength: content.length
      });

      return unifiedMessage;
    } catch (error) {
      this.logError('Failed to parse WeChat message', error);
      throw new Error(`WeChat message parsing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 发送回复到微信
   * 微信公众号使用被动回复机制
   * @param message 原始消息
   * @param reply 回复内容
   * @returns 回复XML字符串
   */
  async sendReply(message: UnifiedMessage, reply: string): Promise<boolean> {
    try {
      // 微信公众号被动回复XML格式
      const replyXml = this.generateReplyXML(message, reply);
      
      this.logInfo('Generated WeChat reply XML', {
        userId: message.userId,
        replyLength: reply.length,
        xmlLength: replyXml.length
      });

      // 注意：这里返回true，实际的XML会在API路由中返回给微信
      return true;
    } catch (error) {
      this.logError('Failed to generate WeChat reply', error);
      return false;
    }
  }

  /**
   * 生成微信回复XML
   * @param message 原始消息
   * @param reply 回复内容
   * @returns XML字符串
   */
  generateReplyXML(message: UnifiedMessage, reply: string): string {
    const now = Math.floor(Date.now() / 1000);
    
    return `<xml>
<ToUserName><![CDATA[${message.userId}]]></ToUserName>
<FromUserName><![CDATA[${message.metadata.chatId}]]></FromUserName>
<CreateTime>${now}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[${reply}]]></Content>
</xml>`;
  }

  /**
   * 映射微信消息类型到统一类型
   * @param wechatType 微信消息类型
   * @returns 统一消息类型
   */
  private mapWeChatMessageType(wechatType: string): 'text' | 'image' | 'voice' | 'video' {
    const typeMap: Record<string, 'text' | 'image' | 'voice' | 'video'> = {
      'text': 'text',
      'image': 'image',
      'voice': 'voice',
      'video': 'video',
      'shortvideo': 'video'
    };

    return typeMap[wechatType] || 'text';
  }

  /**
   * 提取消息内容
   * @param msg 微信消息对象
   * @returns 消息内容
   */
  private extractMessageContent(msg: Record<string, unknown>): string {
    switch (msg.MsgType) {
      case 'text':
        return msg.Content || '';
      case 'image':
        return `[图片消息] ${msg.PicUrl || ''}`;
      case 'voice':
        return `[语音消息] ${msg.Recognition || '[语音]'}`;
      case 'video':
      case 'shortvideo':
        return `[视频消息] ${msg.ThumbMediaId || '[视频]'}`;
      case 'event':
        return this.handleEventMessage(msg);
      default:
        return `[${msg.MsgType}消息]`;
    }
  }

  /**
   * 处理事件消息
   * @param msg 微信事件消息
   * @returns 事件描述
   */
  private handleEventMessage(msg: Record<string, unknown>): string {
    switch (msg.Event) {
      case 'subscribe':
        return '[用户关注]';
      case 'unsubscribe':
        return '[用户取消关注]';
      case 'CLICK':
        return `[菜单点击] ${msg.EventKey || ''}`;
      default:
        return `[事件] ${msg.Event || ''}`;
    }
  }
}
