import { PrismaClient } from '@prisma/client';

// 全局 Prisma 客户端实例
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'error', 'warn'],
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// 数据库连接测试
export async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 用户相关数据库操作
export const userService = {
  // 创建或更新用户
  async upsertUser(userData: {
    unionId: string;
    openId?: string;
    userId?: string;
    name: string;
    nick?: string;
    avatar?: string;
    mobile?: string;
    email?: string;
    stateCode?: string;
    jobNumber?: string;
    title?: string;
    workPlace?: string;
    hiredDate?: string;
    remark?: string;
    active?: boolean;
    admin?: boolean;
    boss?: boolean;
    senior?: boolean;
    realAuthed?: boolean;
  }) {
    return await prisma.user.upsert({
      where: { unionId: userData.unionId },
      update: {
        openId: userData.openId,
        userId: userData.userId,
        name: userData.name,
        nick: userData.nick,
        avatar: userData.avatar,
        mobile: userData.mobile,
        email: userData.email,
        stateCode: userData.stateCode,
        jobNumber: userData.jobNumber,
        title: userData.title,
        workPlace: userData.workPlace,
        hiredDate: userData.hiredDate,
        remark: userData.remark,
        active: userData.active ?? true,
        admin: userData.admin ?? false,
        boss: userData.boss ?? false,
        senior: userData.senior ?? false,
        realAuthed: userData.realAuthed ?? false,
      },
      create: {
        unionId: userData.unionId,
        openId: userData.openId,
        userId: userData.userId,
        name: userData.name,
        nick: userData.nick,
        avatar: userData.avatar,
        mobile: userData.mobile,
        email: userData.email,
        stateCode: userData.stateCode || '+86',
        jobNumber: userData.jobNumber,
        title: userData.title,
        workPlace: userData.workPlace,
        hiredDate: userData.hiredDate,
        remark: userData.remark,
        active: userData.active ?? true,
        admin: userData.admin ?? false,
        boss: userData.boss ?? false,
        senior: userData.senior ?? false,
        realAuthed: userData.realAuthed ?? false,
      },
      include: {
        departments: {
          include: {
            department: true
          }
        },
        roles: {
          include: {
            role: true
          }
        }
      }
    });
  },

  // 获取用户信息
  async getUserByUnionId(unionId: string) {
    return await prisma.user.findUnique({
      where: { unionId },
      include: {
        departments: {
          include: {
            department: true
          }
        },
        roles: {
          include: {
            role: true
          }
        }
      }
    });
  },

  // 更新用户部门
  async updateUserDepartments(userId: string, departments: Array<{
    deptId: number;
    name: string;
    parentId?: number;
    order?: number;
  }>) {
    // 删除现有部门关联
    await prisma.userDepartment.deleteMany({
      where: { userId }
    });

    // 创建或更新部门信息
    for (const dept of departments) {
      await prisma.department.upsert({
        where: { deptId: dept.deptId },
        update: {
          name: dept.name,
          parentId: dept.parentId,
          order: dept.order
        },
        create: {
          deptId: dept.deptId,
          name: dept.name,
          parentId: dept.parentId,
          order: dept.order
        }
      });

      // 创建用户部门关联
      await prisma.userDepartment.create({
        data: {
          userId,
          deptId: dept.deptId
        }
      });
    }
  },

  // 更新用户角色
  async updateUserRoles(userId: string, roles: Array<{
    roleId: string;
    roleName: string;
    groupName?: string;
  }>) {
    // 删除现有角色关联
    await prisma.userRole.deleteMany({
      where: { userId }
    });

    // 创建或更新角色信息
    for (const role of roles) {
      // 确保 roleId 是字符串类型
      const roleIdStr = String(role.roleId);

      await prisma.role.upsert({
        where: { roleId: roleIdStr },
        update: {
          roleName: role.roleName,
          groupName: role.groupName
        },
        create: {
          roleId: roleIdStr,
          roleName: role.roleName,
          groupName: role.groupName
        }
      });

      // 创建用户角色关联
      await prisma.userRole.create({
        data: {
          userId,
          roleId: roleIdStr
        }
      });
    }
  },

  // 创建用户会话
  async createUserSession(userId: string, token: string, expiresAt: Date) {
    return await prisma.userSession.create({
      data: {
        userId,
        token,
        expiresAt
      }
    });
  },

  // 验证用户会话
  async validateUserSession(token: string) {
    return await prisma.userSession.findUnique({
      where: { token },
      include: {
        user: {
          include: {
            departments: {
              include: {
                department: true
              }
            },
            roles: {
              include: {
                role: true
              }
            }
          }
        }
      }
    });
  },

  // 删除过期会话
  async cleanupExpiredSessions() {
    return await prisma.userSession.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });
  }
};

// 数据库健康检查
export async function healthCheck() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
