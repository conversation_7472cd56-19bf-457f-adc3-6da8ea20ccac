#!/bin/bash

# 🚀 吴都乔街智能客服平台 Git 仓库初始化脚本
# 版本: v1.1
# 创建时间: 2025-01-28

echo "🚀 开始初始化 Git 仓库..."

# 检查是否在项目根目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查是否已经是 Git 仓库
if [ -d ".git" ]; then
    echo "⚠️  警告: 已存在 Git 仓库，是否重新初始化? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        exit 1
    fi
    rm -rf .git
fi

# 初始化 Git 仓库
echo "📁 初始化 Git 仓库..."
git init

# 创建 .gitignore 文件
echo "📝 创建 .gitignore 文件..."
cat > .gitignore << 'EOF'
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# prisma
/prisma/migrations/

# IDE
.vscode/
.idea/

# OS
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port
EOF

# 创建 README.md 文件
echo "📖 创建 README.md 文件..."
cat > README.md << 'EOF'
# 🤖 吴都乔街智能客服平台

## 📋 项目简介

基于钉钉登录的智能客服中控平台，为吴都乔街景区提供智能化的游客咨询服务。

## 🎯 功能特性

### ✅ 已完成功能 (v1.1)
- **钉钉第三方登录** - 完整的OAuth2.0认证流程
- **用户管理系统** - 用户信息、部门、角色管理
- **权限控制** - 基于角色的访问控制
- **响应式界面** - 支持桌面端和移动端
- **数据库集成** - MySQL + Prisma ORM

### 🚧 开发中功能 (v2.0)
- **AI智能对话** - 基于DeepSeek的智能问答
- **知识库管理** - 景区信息知识库
- **向量搜索** - 智能知识检索
- **实时监控** - 服务质量监控大屏
- **数据分析** - 用户行为和满意度分析

## 🏗️ 技术架构

```
前端: Next.js 15 + React + TypeScript + Shadcn-Admin
后端: Next.js API Routes
数据库: MySQL 8.0 + Prisma ORM
认证: 钉钉OAuth2.0
AI服务: DeepSeek API (计划)
部署: Docker + 本地部署
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0
- npm 或 yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd wudu-platform-v4
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填入相关配置
```

4. **初始化数据库**
```bash
npx prisma generate
npx prisma db push
```

5. **启动开发服务器**
```bash
npm run dev
```

6. **访问应用**
```
http://localhost:3000
```

## 📊 项目状态

- **当前版本**: v1.1
- **开发状态**: 基础平台完成，AI功能开发中
- **测试状态**: 钉钉登录功能已测试通过
- **部署状态**: 外网可访问 (http://**************:30002)

## 📚 文档

- [开发文档](docs/AI智能客服系统开发文档.md)
- [任务清单](docs/AI客服开发任务清单.md)
- [数据库设计](docs/数据库设计文档.md)
- [版本历史](versions/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目负责人: [待定]
- 技术支持: [待定]
- 邮箱: [待定]

---

*最后更新: 2025-01-28*
EOF

# 添加所有文件到暂存区
echo "📦 添加文件到暂存区..."
git add .

# 创建初始提交
echo "💾 创建初始提交..."
git commit -m "feat: 初始化吴都乔街智能客服平台项目

✨ 新功能:
- 钉钉第三方登录系统
- 用户管理和权限控制
- 响应式前端界面
- MySQL数据库集成
- Prisma ORM数据模型

📚 文档:
- 完整的开发文档
- AI客服系统设计方案
- 数据库设计文档
- 项目任务清单

🏗️ 技术栈:
- Next.js 15 + React + TypeScript
- Shadcn-Admin UI组件
- MySQL 8.0 + Prisma ORM
- Zustand状态管理
- Tailwind CSS样式

📊 项目状态:
- 基础平台: 100% 完成
- AI客服功能: 设计完成，待开发
- 外网部署: 已完成 (**************:30002)"

# 创建开发分支
echo "🌿 创建开发分支..."
git checkout -b develop

# 创建功能分支示例
git checkout -b feature/ai-customer-service
git checkout develop

# 创建版本标签
echo "🏷️  创建版本标签..."
git tag -a v1.1 -m "v1.1: 钉钉登录基础平台完成

🎯 主要功能:
- 钉钉OAuth2.0登录集成
- 用户信息管理系统
- 部门和角色权限控制
- 响应式前端界面
- 数据库模型设计

📈 技术指标:
- 代码行数: 3000+
- 组件数量: 25+
- API接口: 15+
- 数据库表: 6个

🚀 下一版本计划:
- AI智能对话功能
- 知识库管理系统
- 向量搜索实现
- 实时监控大屏"

# 显示仓库状态
echo "📊 Git 仓库状态:"
git status
echo ""
echo "🌿 分支列表:"
git branch -a
echo ""
echo "🏷️  标签列表:"
git tag -l
echo ""
echo "📝 最近提交:"
git log --oneline -5

echo ""
echo "✅ Git 仓库初始化完成!"
echo ""
echo "🎯 下一步操作建议:"
echo "1. 添加远程仓库: git remote add origin <repository-url>"
echo "2. 推送到远程仓库: git push -u origin main"
echo "3. 推送标签: git push origin --tags"
echo "4. 推送开发分支: git push -u origin develop"
echo ""
echo "🔧 开发工作流:"
echo "1. 切换到功能分支: git checkout feature/ai-customer-service"
echo "2. 开发新功能并提交"
echo "3. 合并到develop分支进行测试"
echo "4. 测试通过后合并到main分支"
echo ""
echo "📚 更多信息请查看项目文档: docs/"
EOF
