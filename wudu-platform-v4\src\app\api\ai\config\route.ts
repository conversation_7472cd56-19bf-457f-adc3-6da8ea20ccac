import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 加密密钥
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here';

// 加密函数
function encrypt(text: string): string {
  if (!text) return '';

  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipher('aes-256-cbc', key);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('加密失败:', error);
    return text;
  }
}

// 解密函数
function decrypt(encryptedText: string): string {
  if (!encryptedText) return '';

  try {
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);

    const textParts = encryptedText.split(':');
    if (textParts.length !== 2) {
      return encryptedText;
    }

    const iv = Buffer.from(textParts[0], 'hex');
    const encrypted = textParts[1];

    const decipher = crypto.createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    return '';
  }
}

// GET /api/ai/config - 获取AI配置
export async function GET() {
  try {
    const configs = await prisma.aiConfig.findMany({
      where: { isActive: true },
      orderBy: { configKey: 'asc' }
    });

    // 解密敏感配置
    const decryptedConfigs = configs.map(config => ({
      ...config,
      configValue: config.isEncrypted ? decrypt(config.configValue || '') : config.configValue
    }));

    return NextResponse.json({
      success: true,
      message: '获取AI配置成功',
      configs: decryptedConfigs,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 获取AI配置失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取AI配置失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// POST /api/ai/config - 保存AI配置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { configs } = body;

    if (!configs || !Array.isArray(configs)) {
      return NextResponse.json({
        success: false,
        message: '无效的配置数据'
      }, { status: 400 });
    }

    console.log('🔧 保存AI配置:', configs.length, '项');

    // 批量保存配置
    const results = [];
    
    for (const config of configs) {
      const { key, value, type, encrypted } = config;
      
      if (!key) continue;

      // 加密敏感数据
      const finalValue = encrypted ? encrypt(value) : value;
      
      // 使用 upsert 来创建或更新配置
      const result = await prisma.aiConfig.upsert({
        where: { configKey: key },
        update: {
          configValue: finalValue,
          configType: type.toUpperCase(),
          isEncrypted: encrypted,
          updatedAt: new Date()
        },
        create: {
          configKey: key,
          configValue: finalValue,
          configType: type.toUpperCase(),
          isEncrypted: encrypted,
          description: getConfigDescription(key),
          isActive: true
        }
      });
      
      results.push(result);
    }

    console.log('✅ AI配置保存成功:', results.length, '项');

    return NextResponse.json({
      success: true,
      message: `成功保存 ${results.length} 项配置`,
      count: results.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 保存AI配置失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '保存AI配置失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE /api/ai/config - 删除配置
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const configKey = searchParams.get('key');

    if (!configKey) {
      return NextResponse.json({
        success: false,
        message: '缺少配置键'
      }, { status: 400 });
    }

    await prisma.aiConfig.update({
      where: { configKey },
      data: { isActive: false }
    });

    return NextResponse.json({
      success: true,
      message: '配置删除成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ 删除AI配置失败:', error);
    
    return NextResponse.json({
      success: false,
      message: '删除AI配置失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 获取配置描述
function getConfigDescription(key: string): string {
  const descriptions: Record<string, string> = {
    'deepseek_api_key': 'DeepSeek API密钥',
    'openai_api_key': 'OpenAI API密钥',
    'selected_model': '当前选择的AI模型',
    'max_tokens': '最大令牌数限制',
    'temperature': '模型创造性参数',
    'enable_logging': '是否启用对话日志记录'
  };
  
  return descriptions[key] || '自定义配置项';
}
