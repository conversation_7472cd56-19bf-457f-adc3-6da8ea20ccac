/**
 * 批量向量生成API接口
 * 为多个知识条目批量生成embedding向量
 */

import { NextRequest, NextResponse } from 'next/server';
import { embeddingService } from '@/lib/ai/embedding-service';
import { PrismaClient } from '@prisma/client';
import { auth } from '@/lib/auth';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      knowledgeIds = [], 
      forceRegenerate = false,
      onlyMissing = true 
    } = body;

    console.log(`🔄 开始批量向量生成: ${knowledgeIds.length > 0 ? `指定${knowledgeIds.length}个条目` : '所有条目'}`);

    // 构建查询条件
    let whereCondition: any = {
      status: 'ACTIVE'
    };

    if (knowledgeIds.length > 0) {
      whereCondition.id = { in: knowledgeIds };
    }

    if (onlyMissing && !forceRegenerate) {
      whereCondition.embedding = { equals: null };
    }

    // 获取需要处理的知识条目
    const knowledgeItems = await prisma.knowledgeBase.findMany({
      where: whereCondition,
      select: {
        id: true,
        title: true,
        content: true,
        summary: true,
        embedding: true,
        embeddingModel: true,
        embeddingUpdatedAt: true
      }
    });

    if (knowledgeItems.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有需要处理的知识条目',
        data: {
          totalItems: 0,
          processedItems: 0,
          skippedItems: 0,
          errorItems: 0,
          processingTime: 0
        }
      });
    }

    console.log(`📋 找到 ${knowledgeItems.length} 个需要处理的知识条目`);

    // 初始化embedding服务
    await embeddingService.initialize();

    const startTime = Date.now();
    let processedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const results: Array<{
      id: string;
      title: string;
      status: 'success' | 'skipped' | 'error';
      dimensions?: number;
      error?: string;
      processingTime?: number;
    }> = [];

    // 批量处理
    for (const item of knowledgeItems) {
      try {
        // 检查是否跳过
        if (item.embedding && !forceRegenerate) {
          results.push({
            id: item.id,
            title: item.title,
            status: 'skipped'
          });
          skippedCount++;
          continue;
        }

        // 准备要向量化的文本
        const textToEmbed = [
          item.title,
          item.summary || '',
          item.content.substring(0, 1000)
        ].filter(Boolean).join(' ');

        // 生成向量
        const embeddingResult = await embeddingService.generateEmbedding(textToEmbed);

        // 更新数据库
        await prisma.knowledgeBase.update({
          where: { id: item.id },
          data: {
            embedding: embeddingResult.vector,
            embeddingModel: embeddingResult.model,
            embeddingVersion: '1.0',
            embeddingUpdatedAt: new Date()
          }
        });

        results.push({
          id: item.id,
          title: item.title,
          status: 'success',
          dimensions: embeddingResult.dimensions,
          processingTime: embeddingResult.processingTime
        });

        processedCount++;
        console.log(`✅ 处理完成 ${processedCount}/${knowledgeItems.length}: ${item.title}`);

        // 添加延迟避免API限制
        if (processedCount % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error: any) {
        console.error(`❌ 处理失败: ${item.title}`, error.message);
        
        results.push({
          id: item.id,
          title: item.title,
          status: 'error',
          error: error.message
        });
        
        errorCount++;
      }
    }

    const totalTime = Date.now() - startTime;

    console.log(`🎉 批量向量生成完成: 成功=${processedCount}, 跳过=${skippedCount}, 失败=${errorCount}, 总耗时=${totalTime}ms`);

    return NextResponse.json({
      success: true,
      message: '批量向量生成完成',
      data: {
        totalItems: knowledgeItems.length,
        processedItems: processedCount,
        skippedItems: skippedCount,
        errorItems: errorCount,
        processingTime: totalTime,
        results
      }
    });

  } catch (error: any) {
    console.error('❌ 批量向量生成API错误:', error);
    
    return NextResponse.json(
      { 
        error: '批量向量生成服务暂时不可用',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取向量化统计信息
    const stats = await prisma.knowledgeBase.groupBy({
      by: ['embeddingModel'],
      where: {
        status: 'ACTIVE'
      },
      _count: {
        id: true
      }
    });

    const totalItems = await prisma.knowledgeBase.count({
      where: { status: 'ACTIVE' }
    });

    const itemsWithEmbedding = await prisma.knowledgeBase.count({
      where: { 
        status: 'ACTIVE',
        embedding: { not: null }
      }
    });

    const itemsWithoutEmbedding = totalItems - itemsWithEmbedding;

    // 获取最近更新的向量
    const recentEmbeddings = await prisma.knowledgeBase.findMany({
      where: {
        status: 'ACTIVE',
        embeddingUpdatedAt: { not: null }
      },
      select: {
        id: true,
        title: true,
        embeddingModel: true,
        embeddingUpdatedAt: true
      },
      orderBy: {
        embeddingUpdatedAt: 'desc'
      },
      take: 10
    });

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalItems,
          itemsWithEmbedding,
          itemsWithoutEmbedding,
          embeddingCoverage: totalItems > 0 ? Math.round((itemsWithEmbedding / totalItems) * 100) : 0
        },
        modelStats: stats.map(stat => ({
          model: stat.embeddingModel || 'unknown',
          count: stat._count.id
        })),
        recentEmbeddings
      }
    });

  } catch (error: any) {
    console.error('❌ 批量向量统计API错误:', error);
    
    return NextResponse.json(
      { 
        error: '向量统计查询失败',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
