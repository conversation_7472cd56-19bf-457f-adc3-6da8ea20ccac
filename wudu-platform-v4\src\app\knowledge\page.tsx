'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Tag,
  Calendar,
  User
} from 'lucide-react';

// 模拟知识库数据
const mockKnowledgeItems = [
  {
    id: '1',
    title: '景区门票价格说明',
    summary: '详细介绍景区各类门票的价格、优惠政策和购买方式',
    category: '门票信息',
    tags: ['门票', '价格', '优惠'],
    status: 'active',
    viewCount: 156,
    useCount: 89,
    createdAt: '2025-01-20',
    updatedAt: '2025-01-25'
  },
  {
    id: '2',
    title: '景区开放时间和节假日安排',
    summary: '景区的日常开放时间、节假日特殊安排和临时闭园通知',
    category: '基础信息',
    tags: ['开放时间', '节假日', '通知'],
    status: 'active',
    viewCount: 234,
    useCount: 145,
    createdAt: '2025-01-18',
    updatedAt: '2025-01-24'
  },
  {
    id: '3',
    title: '交通指南和停车信息',
    summary: '如何到达景区的各种交通方式，包括公共交通和自驾路线',
    category: '交通指南',
    tags: ['交通', '停车', '路线'],
    status: 'active',
    viewCount: 189,
    useCount: 112,
    createdAt: '2025-01-15',
    updatedAt: '2025-01-22'
  },
  {
    id: '4',
    title: '景区游览路线推荐',
    summary: '根据不同游客需求推荐的最佳游览路线和时间安排',
    category: '游览指南',
    tags: ['路线', '推荐', '游览'],
    status: 'draft',
    viewCount: 67,
    useCount: 23,
    createdAt: '2025-01-10',
    updatedAt: '2025-01-20'
  },
  {
    id: '5',
    title: '餐饮服务和特色美食',
    summary: '景区内的餐饮设施、特色美食推荐和价格信息',
    category: '餐饮服务',
    tags: ['餐饮', '美食', '价格'],
    status: 'active',
    viewCount: 98,
    useCount: 56,
    createdAt: '2025-01-12',
    updatedAt: '2025-01-19'
  }
];

const categories = ['全部', '门票信息', '基础信息', '交通指南', '游览指南', '餐饮服务'];

export default function KnowledgePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [selectedStatus, setSelectedStatus] = useState('全部');

  const filteredItems = mockKnowledgeItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === '全部' || item.category === selectedCategory;
    const matchesStatus = selectedStatus === '全部' || item.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">已发布</Badge>;
      case 'draft':
        return <Badge variant="secondary">草稿</Badge>;
      case 'archived':
        return <Badge variant="outline">已归档</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">📚 知识库管理</h1>
          <p className="text-muted-foreground mt-2">
            管理AI客服的知识库内容，提供准确的景区信息
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          新建知识条目
        </Button>
      </div>

      <Tabs defaultValue="list" className="space-y-6">
        <TabsList>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            知识列表
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            分类管理
          </TabsTrigger>
        </TabsList>

        {/* 知识列表标签页 */}
        <TabsContent value="list" className="space-y-6">
          {/* 搜索和筛选 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索知识库内容..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                  >
                    <option value="全部">全部状态</option>
                    <option value="active">已发布</option>
                    <option value="draft">草稿</option>
                    <option value="archived">已归档</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{mockKnowledgeItems.length}</div>
                <p className="text-xs text-muted-foreground">总知识条目</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">
                  {mockKnowledgeItems.filter(item => item.status === 'active').length}
                </div>
                <p className="text-xs text-muted-foreground">已发布</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">
                  {mockKnowledgeItems.reduce((sum, item) => sum + item.viewCount, 0)}
                </div>
                <p className="text-xs text-muted-foreground">总浏览量</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">
                  {mockKnowledgeItems.reduce((sum, item) => sum + item.useCount, 0)}
                </div>
                <p className="text-xs text-muted-foreground">AI使用次数</p>
              </CardContent>
            </Card>
          </div>

          {/* 知识条目列表 */}
          <div className="space-y-4">
            {filteredItems.map((item) => (
              <Card key={item.id}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{item.title}</h3>
                        {getStatusBadge(item.status)}
                        <Badge variant="outline">{item.category}</Badge>
                      </div>
                      <p className="text-muted-foreground mb-3">{item.summary}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {item.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-6 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          浏览 {item.viewCount}
                        </div>
                        <div className="flex items-center gap-1">
                          <BookOpen className="h-4 w-4" />
                          使用 {item.useCount}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          更新于 {item.updatedAt}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <BookOpen className="h-16 w-16 mx-auto text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium mb-2">没有找到匹配的知识条目</h3>
                  <p className="text-muted-foreground">
                    尝试调整搜索条件或创建新的知识条目
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 分类管理标签页 */}
        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>📂 知识库分类管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Tag className="h-16 w-16 mx-auto text-muted-foreground/50 mb-4" />
                <h3 className="text-lg font-medium mb-2">分类管理功能开发中</h3>
                <p className="text-muted-foreground">
                  即将推出知识库分类的创建、编辑和管理功能
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </MainLayout>
  );
}
