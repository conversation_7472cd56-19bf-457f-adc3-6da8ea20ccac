# 吴都乔街景区智能客服中控平台 v3.4 文档中心

## 📚 文档概览

本文档中心包含了吴都乔街景区智能客服中控平台v3.4版本的完整开发文档，基于v3.2版本的基础架构，结合v3.3版本的实践经验，形成了完善的技术文档体系。

## 🎯 版本演进

### 技术架构演进历程
```
v3.2: 前端(3001) + 后端(33018) 
  ↓ 问题: 钉钉回调域名冲突
v3.3: 前端(3001) + 后端(3000) + 代理(33018)
  ↓ 解决: 统一代理架构
v3.4: 标准化三层架构 + 完整文档体系
  ↓ 优化: 生产就绪方案
```

### 核心改进点
- ✅ **架构统一**: 标准化前端+后端+代理三层架构
- ✅ **钉钉登录**: 完美解决回调域名问题
- ✅ **文档完善**: 基于v3.2文档提炼，补充v3.3实践经验
- ✅ **部署优化**: Docker + Nginx + PM2 生产级部署
- ✅ **监控体系**: 完整的日志、性能、错误监控

## 📖 文档结构

### 核心文档

#### 🏗️ [v3.4完整开发文档](./v3.4-complete-development-documentation.md)
**主要文档** - 包含完整的技术架构、实施方案和部署指南
- 系统架构设计
- 钉钉扫码登录详解
- 数据库设计
- Docker部署配置
- 开发流程规范
- 监控运维方案

### 功能模块文档

#### 🔐 钉钉认证模块
- **实施状态**: ✅ 已完成
- **核心特性**: 统一代理架构解决回调域名问题
- **文档位置**: [钉钉实现指南](./钉钉实现指南/)
  - [钉钉扫码登录完整实施文档](./钉钉实现指南/钉钉扫码登录完整实施文档.md)
  - [技术反思与优化方案](./钉钉实现指南/技术反思与优化方案.md)
  - [代码优化方案](./钉钉实现指南/代码优化方案.md)
  - [钉钉登录优化方案](./钉钉实现指南/钉钉登录优化方案.md)
  - [项目实施总结](./钉钉实现指南/项目实施总结.md)

#### 🤖 AI客服模块
- **实施状态**: 🔄 开发中
- **核心特性**: DeepSeek/豆包API集成，多平台支持
- **技术栈**: DeepSeek API + 知识库检索 + 多人设配置

#### 📚 知识库模块
- **实施状态**: 🔄 开发中
- **核心特性**: 分类管理、全文搜索、使用统计
- **技术栈**: MySQL全文索引 + 分类树结构

#### 📊 客流监控模块
- **实施状态**: 🔄 开发中
- **核心特性**: 7设备实时监控、历史数据分析
- **技术栈**: 设备API集成 + 实时数据处理

#### 📈 数据大屏模块
- **实施状态**: ⏳ 待开发
- **核心特性**: 实时数据展示、多维度分析
- **技术栈**: ECharts + WebSocket实时更新

#### 📰 舆情分析模块
- **实施状态**: ⏳ 待开发
- **核心特性**: 多平台数据采集、情感分析
- **技术栈**: 爬虫 + NLP情感分析

## 🔧 技术栈总览

### 前端技术栈
```yaml
框架: Next.js 15.4.2 (App Router)
语言: TypeScript 5.x
UI库: Ant Design 5.x
样式: CSS Modules
状态: Zustand + React Query
图表: ECharts 5.x
```

### 后端技术栈
```yaml
框架: Express.js 4.x
语言: TypeScript 5.x
数据库: MySQL 8.x
ORM: Prisma 5.x
缓存: Redis 7.x
认证: JWT + bcrypt
```

### 基础设施
```yaml
代理: Nginx 1.24+
容器: Docker + Docker Compose
进程: PM2 集群模式
监控: Winston + 自定义指标
```

## 🚀 快速开始

### 环境要求
- Node.js 18.x LTS
- MySQL 8.x
- Redis 7.x
- Docker 24.x (可选)
- Nginx 1.24+ (生产环境)

### 开发环境启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd wudu-qiaojie-platform-v3.4

# 2. 安装依赖
npm install
cd frontend && npm install && cd ..
cd backend && npm install && cd ..

# 3. 环境配置
cp .env.example .env.development
# 编辑 .env.development 配置参数

# 4. 数据库初始化
docker-compose up -d mysql redis
cd backend && npx prisma migrate dev && cd ..

# 5. 启动服务
npm run dev:all

# 6. 访问应用
open http://localhost:33018
```

### 生产环境部署
```bash
# Docker方式部署
docker-compose up -d

# 传统方式部署
npm run build
npm run start:prod
```

## 📋 开发规范

### Git工作流
```bash
# 功能分支
git checkout -b feature/module-name
git commit -m "feat: 功能描述"
git push origin feature/module-name

# 提交规范
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建工具
```

### 代码质量
```bash
npm run lint          # ESLint检查
npm run type-check     # TypeScript检查
npm run test           # 单元测试
npm run test:e2e       # 集成测试
```

## 🎯 项目里程碑

### 当前进度 (v3.4)
- ✅ **Phase 1**: 基础架构搭建 (Week 1-2)
- ✅ **Phase 2**: 钉钉登录完成 (Week 3)
- 🔄 **Phase 3**: 核心功能开发 (Week 4-6)
- ⏳ **Phase 4**: 高级功能开发 (Week 7-8)
- ⏳ **Phase 5**: 测试部署 (Week 9-10)

### 成功标准
- 系统可用性 > 99.5%
- API响应时间 < 500ms
- 钉钉登录成功率 > 98%
- AI客服准确率 > 90%

## 🔍 问题排查

### 常见问题
1. **钉钉登录失败**: 检查回调地址配置和代理服务状态
2. **端口冲突**: 确认33018端口未被占用
3. **数据库连接**: 检查MySQL服务状态和连接配置
4. **前端无法访问**: 检查Nginx代理配置

### 日志查看
```bash
# 应用日志
tail -f backend/logs/app.log

# Nginx日志
tail -f nginx/logs/access.log

# Docker日志
docker-compose logs -f
```

## 📞 技术支持

### 联系方式
- **项目负责人**: 开发团队
- **技术支持**: 通过项目Issue提交
- **文档维护**: 开发团队负责

### 更新记录
- **v3.4.0** (2025-07-21): 完整开发文档发布
- **v3.3.x** (2025-07-20): 钉钉登录功能完成
- **v3.2.x** (2025-01-15): 基础架构设计

---

**注意**: 本文档基于v3.2版本的原始设计文档，结合v3.3版本的实际开发经验，形成了v3.4版本的完整技术方案。所有架构设计和实施方案都经过实际验证，可直接用于生产环境部署。
