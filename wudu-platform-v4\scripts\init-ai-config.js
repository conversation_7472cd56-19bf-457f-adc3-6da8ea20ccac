// 初始化AI配置脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function initAiConfig() {
  console.log('🚀 初始化AI配置...');

  try {
    // 默认配置项
    const defaultConfigs = [
      {
        configKey: 'deepseek_api_key',
        configValue: '',
        configType: 'STRING',
        description: 'DeepSeek API密钥',
        isEncrypted: true,
        isActive: true
      },
      {
        configKey: 'openai_api_key',
        configValue: '',
        configType: 'STRING',
        description: 'OpenAI API密钥',
        isEncrypted: true,
        isActive: true
      },
      {
        configKey: 'selected_model',
        configValue: 'deepseek-chat',
        configType: 'STRING',
        description: '当前选择的AI模型',
        isEncrypted: false,
        isActive: true
      },
      {
        configKey: 'max_tokens',
        configValue: '2048',
        configType: 'NUMBER',
        description: '最大令牌数限制',
        isEncrypted: false,
        isActive: true
      },
      {
        configKey: 'temperature',
        configValue: '0.7',
        configType: 'NUMBER',
        description: '模型创造性参数',
        isEncrypted: false,
        isActive: true
      },
      {
        configKey: 'enable_logging',
        configValue: 'true',
        configType: 'BOOLEAN',
        description: '是否启用对话日志记录',
        isEncrypted: false,
        isActive: true
      }
    ];

    // 批量创建配置
    for (const config of defaultConfigs) {
      const existing = await prisma.aiConfig.findUnique({
        where: { configKey: config.configKey }
      });

      if (!existing) {
        await prisma.aiConfig.create({
          data: config
        });
        console.log(`✅ 创建配置: ${config.configKey}`);
      } else {
        console.log(`⚠️  配置已存在: ${config.configKey}`);
      }
    }

    console.log('🎉 AI配置初始化完成！');
  } catch (error) {
    console.error('❌ 初始化失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
initAiConfig();
