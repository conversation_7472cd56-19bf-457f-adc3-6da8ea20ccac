import { PrismaClient } from '@prisma/client';
import { aiServiceManager, type ChatMessage, type ConversationContext } from './ai-service-manager';
import { ragService } from './rag-service';
import { MessageRole, type MessageRoleType } from '@/types/prisma';

const prisma = new PrismaClient();

export interface CreateConversationParams {
  userId?: string;
  sessionId: string;
  title?: string;
}

export interface SendMessageParams {
  conversationId: string;
  content: string;
  role: MessageRoleType | 'user' | 'assistant' | 'system'; // 兼容旧的小写值
  intent?: string;
  sentiment?: string;
  metadata?: Record<string, any>;
}

export interface CreateMessageParams {
  conversationId: string;
  content: string;
  role: MessageRoleType | 'user' | 'assistant' | 'system';
  intent?: string;
  sentiment?: string;
  metadata?: Record<string, any>;
}

export interface ConversationWithMessages {
  id: string;
  sessionId: string;
  title: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  messages: Array<{
    id: string;
    role: string;
    content: string;
    intent?: string;
    sentiment?: string;
    createdAt: Date;
  }>;
}

export class ConversationManager {
  /**
   * 标准化角色值，确保使用正确的枚举值
   */
  private normalizeRole(role: string): MessageRoleType {
    const upperRole = role.toUpperCase();
    switch (upperRole) {
      case 'USER':
        return MessageRole.USER;
      case 'ASSISTANT':
        return MessageRole.ASSISTANT;
      case 'SYSTEM':
        return MessageRole.SYSTEM;
      default:
        throw new Error(`无效的角色值: ${role}`);
    }
  }

  // 创建新对话
  async createConversation(params: CreateConversationParams) {
    try {
      const conversation = await prisma.aiConversation.create({
        data: {
          userId: params.userId,
          sessionId: params.sessionId,
          title: params.title || '新对话',
          status: 'ACTIVE'
        }
      });

      console.log(`✅ 创建对话成功: ${conversation.id}`);
      return conversation;
    } catch (error) {
      console.error('❌ 创建对话失败:', error);
      throw new Error('创建对话失败');
    }
  }

  // 创建单个消息（不触发AI回复）
  async createMessage(params: CreateMessageParams) {
    try {
      const message = await prisma.aiMessage.create({
        data: {
          conversationId: params.conversationId,
          role: this.normalizeRole(params.role),
          content: params.content,
          intent: params.intent,
          sentiment: params.sentiment,
          metadata: params.metadata
        }
      });

      console.log(`✅ 消息创建成功: ${message.id}`);
      return message;
    } catch (error: any) {
      console.error('❌ 创建消息失败:', error);
      throw new Error(error.message || '创建消息失败');
    }
  }

  // 发送消息并获取AI回复
  async sendMessage(params: SendMessageParams): Promise<{
    userMessage: any;
    assistantMessage?: any;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // 1. 保存用户消息
      const userMessage = await prisma.aiMessage.create({
        data: {
          conversationId: params.conversationId,
          role: this.normalizeRole(params.role),
          content: params.content,
          intent: params.intent,
          sentiment: params.sentiment,
          metadata: params.metadata
        }
      });

      // 2. 如果是用户消息，获取AI回复
      if (params.role === 'USER' || params.role === 'user') {
        try {
          // 获取对话历史
          const conversation = await this.getConversationWithMessages(params.conversationId);
          if (!conversation) {
            throw new Error('对话不存在');
          }

          // 构建对话上下文
          const context: ConversationContext = {
            sessionId: conversation.sessionId,
            userId: conversation.userId || undefined,
            messages: conversation.messages.map(msg => ({
              role: msg.role.toLowerCase() as 'user' | 'assistant' | 'system',
              content: msg.content
            })),
            intent: params.intent,
            sentiment: params.sentiment as 'positive' | 'neutral' | 'negative',
            metadata: params.metadata
          };

          // 调用RAG服务（检索增强生成）
          console.log(`🚀 开始调用RAG服务，消息数量: ${context.messages.length}`);
          const ragStartTime = Date.now();

          const ragResponse = await ragService.generateResponse(
            params.content, // 用户当前问题
            context
          );

          const ragResponseTime = Date.now() - ragStartTime;
          const totalResponseTime = Date.now() - startTime;

          console.log(`⚡ RAG服务响应完成，RAG耗时: ${ragResponseTime}ms，总耗时: ${totalResponseTime}ms，置信度: ${ragResponse.confidence}`);

          // 3. 保存AI回复（包含RAG信息）
          const dbStartTime = Date.now();
          const assistantMessage = await prisma.aiMessage.create({
            data: {
              conversationId: params.conversationId,
              role: MessageRole.ASSISTANT,
              content: ragResponse.answer,
              responseTimeMs: totalResponseTime,
              confidenceScore: ragResponse.confidence,
              knowledgeUsed: ragResponse.knowledgeUsed,
              metadata: {
                ragMetadata: ragResponse.metadata,
                searchTime: ragResponse.searchTime,
                generateTime: ragResponse.generateTime,
                searchType: ragResponse.searchType,
                knowledgeCount: ragResponse.knowledgeUsed.length
              }
            }
          });
          const dbTime = Date.now() - dbStartTime;
          console.log(`💾 数据库保存完成，耗时: ${dbTime}ms`);

          // 4. 更新对话统计
          await this.updateConversationStats(params.conversationId);

          console.log(`✅ 消息处理成功，总响应时间: ${totalResponseTime}ms`);

          return {
            userMessage,
            assistantMessage
          };
        } catch (aiError: any) {
          console.error('❌ AI处理失败:', aiError);
          
          // 即使AI失败，也要保存用户消息
          return {
            userMessage,
            error: aiError.message || 'AI服务暂时不可用'
          };
        }
      }

      // 非用户消息直接返回
      return { userMessage };
    } catch (error: any) {
      console.error('❌ 发送消息失败:', error);
      throw new Error(error.message || '发送消息失败');
    }
  }

  // 获取对话及其消息
  async getConversationWithMessages(conversationId: string): Promise<ConversationWithMessages | null> {
    try {
      const conversation = await prisma.aiConversation.findUnique({
        where: { id: conversationId },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
            select: {
              id: true,
              role: true,
              content: true,
              intent: true,
              sentiment: true,
              createdAt: true
            }
          }
        }
      });

      if (!conversation) {
        return null;
      }

      return {
        id: conversation.id,
        sessionId: conversation.sessionId,
        title: conversation.title,
        status: conversation.status,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
        messages: conversation.messages
      };
    } catch (error) {
      console.error('❌ 获取对话失败:', error);
      return null;
    }
  }

  // 获取用户的对话列表
  async getUserConversations(userId: string, limit: number = 20) {
    try {
      const conversations = await prisma.aiConversation.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
        take: limit,
        include: {
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            select: {
              content: true,
              role: true,
              createdAt: true
            }
          }
        }
      });

      return conversations.map(conv => ({
        id: conv.id,
        title: conv.title,
        status: conv.status,
        lastMessage: conv.messages[0] || null,
        updatedAt: conv.updatedAt
      }));
    } catch (error) {
      console.error('❌ 获取用户对话列表失败:', error);
      return [];
    }
  }

  // 根据sessionId获取或创建对话
  async getOrCreateConversationBySession(sessionId: string, userId?: string) {
    try {
      // 先尝试查找现有对话
      let conversation = await prisma.aiConversation.findFirst({
        where: {
          sessionId,
          status: 'ACTIVE'
        }
      });

      // 如果没有找到，创建新对话
      if (!conversation) {
        conversation = await this.createConversation({
          sessionId,
          userId,
          title: `对话 ${new Date().toLocaleString()}`
        });
      }

      return conversation;
    } catch (error) {
      console.error('❌ 获取或创建对话失败:', error);
      throw new Error('获取对话失败');
    }
  }

  // 更新对话统计信息
  private async updateConversationStats(conversationId: string) {
    try {
      const messageCount = await prisma.aiMessage.count({
        where: { conversationId }
      });

      await prisma.aiConversation.update({
        where: { id: conversationId },
        data: {
          totalMessages: messageCount,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('❌ 更新对话统计失败:', error);
    }
  }

  // 关闭对话
  async closeConversation(conversationId: string) {
    try {
      await prisma.aiConversation.update({
        where: { id: conversationId },
        data: { status: 'CLOSED' }
      });
      console.log(`✅ 对话已关闭: ${conversationId}`);
    } catch (error) {
      console.error('❌ 关闭对话失败:', error);
      throw new Error('关闭对话失败');
    }
  }

  // 删除对话（软删除）
  async archiveConversation(conversationId: string) {
    try {
      await prisma.aiConversation.update({
        where: { id: conversationId },
        data: { status: 'ARCHIVED' }
      });
      console.log(`✅ 对话已归档: ${conversationId}`);
    } catch (error) {
      console.error('❌ 归档对话失败:', error);
      throw new Error('归档对话失败');
    }
  }

  // 获取对话统计
  async getConversationStats(timeRange?: { start: Date; end: Date }) {
    try {
      const whereClause = timeRange ? {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      } : {};

      const [totalConversations, totalMessages, avgResponseTime] = await Promise.all([
        prisma.aiConversation.count({ where: whereClause }),
        prisma.aiMessage.count({
          where: {
            conversation: whereClause,
            role: 'assistant'
          }
        }),
        prisma.aiMessage.aggregate({
          where: {
            conversation: whereClause,
            role: 'assistant',
            responseTimeMs: { not: null }
          },
          _avg: { responseTimeMs: true }
        })
      ]);

      return {
        totalConversations,
        totalMessages,
        avgResponseTime: avgResponseTime._avg.responseTimeMs || 0
      };
    } catch (error) {
      console.error('❌ 获取对话统计失败:', error);
      return {
        totalConversations: 0,
        totalMessages: 0,
        avgResponseTime: 0
      };
    }
  }
}

// 导出单例实例
export const conversationManager = new ConversationManager();
