'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { 
  Search, 
  Brain, 
  Clock, 
  Target, 
  BookOpen, 
  Settings,
  Play,
  RotateCcw
} from 'lucide-react';

interface RAGResponse {
  answer: string;
  knowledgeUsed: Array<{
    id: string;
    title: string;
    excerpt: string;
    similarity: number;
    category?: string;
    tags: string[];
  }>;
  confidence: number;
  searchTime: number;
  generateTime: number;
  totalTime: number;
  searchType: 'vector' | 'hybrid';
  metadata: {
    queryProcessed: string;
    knowledgeCount: number;
  };
}

interface RAGConfig {
  maxKnowledgeItems: number;
  similarityThreshold: number;
  enableHybridSearch: boolean;
  includeSourceReferences: boolean;
}

export function RAGTestPanel() {
  const [query, setQuery] = useState('');
  const [intent, setIntent] = useState('');
  const [sentiment, setSentiment] = useState<'positive' | 'neutral' | 'negative'>('neutral');
  const [config, setConfig] = useState<RAGConfig>({
    maxKnowledgeItems: 5,
    similarityThreshold: 0.6,
    enableHybridSearch: true,
    includeSourceReferences: true
  });
  const [response, setResponse] = useState<RAGResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testQueries = [
    '门票价格是多少？',
    '景区开放时间',
    '怎么去景区？',
    '有什么好玩的景点？',
    '景区内有餐厅吗？'
  ];

  const intentOptions = [
    { value: '', label: '自动识别' },
    { value: 'ticket_inquiry', label: '门票咨询' },
    { value: 'opening_hours', label: '开放时间' },
    { value: 'transportation', label: '交通指南' },
    { value: 'route_planning', label: '路线规划' },
    { value: 'facilities', label: '设施服务' },
    { value: 'dining', label: '餐饮信息' }
  ];

  const handleTest = async () => {
    if (!query.trim()) {
      setError('请输入测试查询');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const res = await fetch('/api/ai/rag', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          intent: intent || undefined,
          sentiment,
          config
        })
      });

      const data = await res.json();

      if (data.success) {
        setResponse(data.data);
      } else {
        setError(data.error || 'RAG测试失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络请求失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setQuery('');
    setIntent('');
    setSentiment('neutral');
    setResponse(null);
    setError(null);
    setConfig({
      maxKnowledgeItems: 5,
      similarityThreshold: 0.6,
      enableHybridSearch: true,
      includeSourceReferences: true
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            RAG检索增强生成测试
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="test" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="test">功能测试</TabsTrigger>
              <TabsTrigger value="config">配置调整</TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="space-y-4">
              {/* 查询输入 */}
              <div className="space-y-2">
                <Label htmlFor="query">测试查询</Label>
                <div className="flex gap-2">
                  <Input
                    id="query"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="输入您的问题..."
                    onKeyPress={(e) => e.key === 'Enter' && handleTest()}
                  />
                  <Button onClick={handleTest} disabled={loading}>
                    {loading ? (
                      <RotateCcw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* 快捷查询 */}
              <div className="space-y-2">
                <Label>快捷测试查询</Label>
                <div className="flex flex-wrap gap-2">
                  {testQueries.map((testQuery, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setQuery(testQuery)}
                    >
                      {testQuery}
                    </Button>
                  ))}
                </div>
              </div>

              {/* 上下文设置 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="intent">意图类型</Label>
                  <select
                    id="intent"
                    value={intent}
                    onChange={(e) => setIntent(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    {intentOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sentiment">情感倾向</Label>
                  <select
                    id="sentiment"
                    value={sentiment}
                    onChange={(e) => setSentiment(e.target.value as any)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="positive">积极</option>
                    <option value="neutral">中性</option>
                    <option value="negative">消极</option>
                  </select>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <Button onClick={handleReset} variant="outline">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="config" className="space-y-4">
              {/* RAG配置 */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>最大知识条目数: {config.maxKnowledgeItems}</Label>
                  <Slider
                    value={[config.maxKnowledgeItems]}
                    onValueChange={([value]) => 
                      setConfig(prev => ({ ...prev, maxKnowledgeItems: value }))
                    }
                    min={1}
                    max={10}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>相似度阈值: {config.similarityThreshold}</Label>
                  <Slider
                    value={[config.similarityThreshold]}
                    onValueChange={([value]) => 
                      setConfig(prev => ({ ...prev, similarityThreshold: value }))
                    }
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="hybrid-search">启用混合搜索</Label>
                  <Switch
                    id="hybrid-search"
                    checked={config.enableHybridSearch}
                    onCheckedChange={(checked) =>
                      setConfig(prev => ({ ...prev, enableHybridSearch: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="source-refs">包含来源引用</Label>
                  <Switch
                    id="source-refs"
                    checked={config.includeSourceReferences}
                    onCheckedChange={(checked) =>
                      setConfig(prev => ({ ...prev, includeSourceReferences: checked }))
                    }
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 错误显示 */}
      {error && (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-red-600">❌ {error}</div>
          </CardContent>
        </Card>
      )}

      {/* 结果显示 */}
      {response && (
        <div className="space-y-4">
          {/* 回答结果 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                AI回答
                <Badge variant={response.confidence > 0.7 ? 'default' : 'secondary'}>
                  置信度: {(response.confidence * 100).toFixed(1)}%
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                {response.answer}
              </div>
            </CardContent>
          </Card>

          {/* 性能指标 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                性能指标
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {response.searchTime}ms
                  </div>
                  <div className="text-sm text-gray-600">检索时间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {response.generateTime}ms
                  </div>
                  <div className="text-sm text-gray-600">生成时间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {response.totalTime}ms
                  </div>
                  <div className="text-sm text-gray-600">总时间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {response.knowledgeUsed.length}
                  </div>
                  <div className="text-sm text-gray-600">知识条目</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 知识库引用 */}
          {response.knowledgeUsed.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  使用的知识库 ({response.knowledgeUsed.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {response.knowledgeUsed.map((knowledge, index) => (
                    <div key={knowledge.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{knowledge.title}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">
                            相似度: {(knowledge.similarity * 100).toFixed(1)}%
                          </Badge>
                          {knowledge.category && (
                            <Badge variant="secondary">{knowledge.category}</Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{knowledge.excerpt}</p>
                      {knowledge.tags.length > 0 && (
                        <div className="flex gap-1">
                          {knowledge.tags.map((tag, tagIndex) => (
                            <Badge key={tagIndex} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 元数据 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                处理信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">处理后查询:</span>
                  <div className="text-gray-600">{response.metadata.queryProcessed}</div>
                </div>
                <div>
                  <span className="font-medium">搜索类型:</span>
                  <div className="text-gray-600">{response.searchType}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
