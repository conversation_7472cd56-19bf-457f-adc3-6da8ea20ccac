# 🚀 吴都乔街智能客服平台 - 快速启动指南

## 📋 准备工作

### ✅ **环境要求**
- [ ] Node.js 18+ 已安装
- [ ] Docker Desktop 已安装并运行
- [ ] Git 已配置
- [ ] VS Code + 推荐插件已安装

### ✅ **账号准备**
- [ ] GitHub账号 (用于项目管理)
- [ ] DeepSeek API账号和密钥
- [ ] 钉钉开放平台应用配置 (已有)
- [ ] 服务器资源 (开发/测试/生产)

## 🎯 **第一天快速搭建 (6小时)**

### **Step 1: 创建Shadcn-Admin项目 (1小时)**

```bash
# 1. 创建Next.js项目
npx create-next-app@latest wudu-platform-v4 --typescript --tailwind --eslint
cd wudu-platform-v4

# 2. 安装Shadcn/ui
npx shadcn-ui@latest init
# 选择配置:
# ✅ TypeScript
# ✅ Tailwind CSS
# ✅ src/ directory
# ✅ App Router

# 3. 安装核心组件
npx shadcn-ui@latest add button card input label
npx shadcn-ui@latest add navigation-menu sidebar
npx shadcn-ui@latest add table form dialog
npx shadcn-ui@latest add tabs badge avatar

# 4. 安装额外依赖
npm install @radix-ui/react-icons lucide-react
npm install zustand @tanstack/react-query axios
npm install @hookform/resolvers zod
npm install recharts date-fns

# 5. 启动开发服务器
npm run dev
# 访问: http://localhost:3000
```

### **Step 2: 配置钉钉登录 (2小时)**

```typescript
// src/lib/auth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
}

interface AuthState {
  user: User | null;
  token: string | null;
  login: (authCode: string, state: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: () => boolean;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      
      login: async (authCode: string, state: string) => {
        try {
          // 使用现有的钉钉登录API
          const response = await fetch('/api/auth/dingtalk/official-login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ authCode, state })
          });
          
          const data = await response.json();
          
          if (data.success) {
            set({ user: data.user, token: data.token });
            return true;
          }
          return false;
        } catch (error) {
          console.error('登录失败:', error);
          return false;
        }
      },
      
      logout: () => {
        set({ user: null, token: null });
      },
      
      isAuthenticated: () => {
        const { token } = get();
        return !!token;
      }
    }),
    {
      name: 'auth-storage'
    }
  )
);
```

```typescript
// src/app/login/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const router = useRouter();

  const handleDingtalkLogin = () => {
    setLoading(true);
    // 使用现有的钉钉登录逻辑
    const authUrl = `https://login.dingtalk.com/oauth2/auth?redirect_uri=${encodeURIComponent(window.location.origin)}&response_type=code&client_id=dinggai5cng27n76jvbq&scope=openid&state=test&prompt=consent`;
    window.location.href = authUrl;
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-[400px]">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">吴都乔街景区</CardTitle>
          <p className="text-muted-foreground">智能客服中控平台</p>
        </CardHeader>
        <CardContent>
          <Button 
            className="w-full" 
            onClick={handleDingtalkLogin}
            disabled={loading}
          >
            {loading ? '登录中...' : '钉钉登录'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
```

### **Step 3: 创建主布局 (2小时)**

```typescript
// src/components/layout/main-layout.tsx
'use client';

import { useState } from 'react';
import { Sidebar } from '@/components/layout/sidebar';
import { Header } from '@/components/layout/header';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      <Sidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />
      <div className={cn(
        "transition-all duration-300 ease-in-out",
        sidebarOpen ? "lg:pl-64" : "lg:pl-16"
      )}>
        <Header onMenuClick={() => setSidebarOpen(!sidebarOpen)} />
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
```

```typescript
// src/components/layout/sidebar.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  MessageSquare, 
  BookOpen, 
  BarChart3, 
  Settings,
  Users
} from 'lucide-react';

const navigation = [
  { name: '仪表板', href: '/dashboard', icon: LayoutDashboard },
  { name: 'AI客服', href: '/chat', icon: MessageSquare },
  { name: '知识库', href: '/knowledge', icon: BookOpen },
  { name: '数据分析', href: '/analytics', icon: BarChart3 },
  { name: '用户管理', href: '/users', icon: Users },
  { name: '系统设置', href: '/settings', icon: Settings },
];

interface SidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function Sidebar({ open, onOpenChange }: SidebarProps) {
  const pathname = usePathname();

  return (
    <div className={cn(
      "fixed inset-y-0 left-0 z-50 flex flex-col bg-background border-r transition-all duration-300",
      open ? "w-64" : "w-16"
    )}>
      <div className="flex h-16 items-center justify-center border-b">
        <h1 className={cn(
          "font-bold transition-all duration-300",
          open ? "text-xl" : "text-sm"
        )}>
          {open ? "吴都乔街" : "WD"}
        </h1>
      </div>
      
      <nav className="flex-1 space-y-2 p-4">
        {navigation.map((item) => {
          const isActive = pathname.startsWith(item.href);
          return (
            <Link key={item.name} href={item.href}>
              <Button
                variant={isActive ? "default" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  !open && "px-2"
                )}
              >
                <item.icon className="h-4 w-4" />
                {open && <span className="ml-2">{item.name}</span>}
              </Button>
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
```

### **Step 4: 创建仪表板 (1小时)**

```typescript
// src/app/dashboard/page.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, Users, TrendingUp, Clock } from 'lucide-react';

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">智能客服中控台</h1>
        <p className="text-muted-foreground">
          欢迎使用吴都乔街景区智能客服管理系统
        </p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日对话</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +12% 较昨日
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI解决率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87.3%</div>
            <p className="text-xs text-muted-foreground">
              +2.1% 较昨日
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">456</div>
            <p className="text-xs text-muted-foreground">
              +8% 较昨日
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均响应时间</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.1s</div>
            <p className="text-xs text-muted-foreground">
              -0.3s 较昨日
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>服务概览</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center text-muted-foreground">
              图表组件将在后续开发中添加
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm">用户张三发起咨询</p>
                  <p className="text-xs text-muted-foreground">2分钟前</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm">AI成功解决问题</p>
                  <p className="text-xs text-muted-foreground">5分钟前</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm">知识库更新</p>
                  <p className="text-xs text-muted-foreground">10分钟前</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

## 🎯 **第一天完成标准**

### ✅ **功能验证**
- [ ] 项目成功创建并运行在 http://localhost:3000
- [ ] Shadcn/ui组件正常显示
- [ ] 钉钉登录功能正常 (使用现有API)
- [ ] 主布局和导航正常工作
- [ ] 仪表板页面正常显示

### ✅ **代码质量**
- [ ] TypeScript编译无错误
- [ ] ESLint检查通过
- [ ] 代码格式化规范
- [ ] 组件结构清晰

## 🚀 **下一步计划**

### **第二天：AI客服模块**
- 部署ChatWiki服务
- 创建聊天界面组件
- 集成DeepSeek API
- 实现对话管理

### **第三天：知识库模块**
- 部署PandaWiki服务
- 创建知识库管理界面
- 实现智能搜索功能
- 集成文档管理

### **第四天：数据分析模块**
- 部署Apache Superset
- 创建数据分析界面
- 配置数据源连接
- 设计业务仪表板

## 📞 **技术支持**

如果在快速启动过程中遇到问题，可以：

1. **查看错误日志** - 检查控制台输出
2. **检查依赖版本** - 确保Node.js和npm版本正确
3. **重新安装依赖** - `rm -rf node_modules && npm install`
4. **清除缓存** - `npm run build` 重新构建

**第一天的目标是搭建一个可运行的基础框架，为后续开发奠定坚实基础！** 🎉
