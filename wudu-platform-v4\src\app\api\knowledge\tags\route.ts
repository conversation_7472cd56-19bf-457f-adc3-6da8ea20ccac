import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { auth } from '@/lib/auth';

// GET /api/knowledge/tags - 获取标签列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const includeCount = searchParams.get('includeCount') === 'true';
    const limit = parseInt(searchParams.get('limit') || '50');

    const where: any = {};
    if (search) {
      where.name = {
        contains: search
      };
    }

    const tags = await prisma.kbTag.findMany({
      where,
      take: limit,
      orderBy: [
        { useCount: 'desc' },
        { name: 'asc' }
      ],
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        _count: includeCount ? {
          select: {
            knowledgeBaseTags: true
          }
        } : undefined
      }
    });

    return NextResponse.json({
      success: true,
      data: tags
    });

  } catch (error) {
    console.error('❌ 获取标签列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取标签列表失败' },
      { status: 500 }
    );
  }
}

// POST /api/knowledge/tags - 创建标签
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, description, color } = body;

    // 验证必需字段
    if (!name) {
      return NextResponse.json(
        { success: false, error: '标签名称不能为空' },
        { status: 400 }
      );
    }

    // 检查标签名称是否重复
    const existingTag = await prisma.kbTag.findUnique({
      where: { name }
    });

    if (existingTag) {
      return NextResponse.json(
        { success: false, error: '标签名称已存在' },
        { status: 400 }
      );
    }

    // 创建标签
    const tag = await prisma.kbTag.create({
      data: {
        name,
        description,
        color,
        createdBy: session.user.id
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        _count: {
          select: {
            knowledgeBaseTags: true
          }
        }
      }
    });

    console.log(`✅ 创建标签成功: ${tag.id}`);

    return NextResponse.json({
      success: true,
      data: tag
    });

  } catch (error) {
    console.error('❌ 创建标签失败:', error);
    return NextResponse.json(
      { success: false, error: '创建标签失败' },
      { status: 500 }
    );
  }
}
