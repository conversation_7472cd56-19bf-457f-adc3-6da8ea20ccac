# 钉钉登录功能快速部署指南

## 🚀 快速开始

### 前置条件
- Node.js 18+ 
- 钉钉开放平台账号
- 服务器公网IP

### 1分钟部署步骤

#### 1. 克隆项目文件
```bash
# 复制以下核心文件到项目目录
- test-no-proxy-backend.js          # 后端服务
- test-dingtalk-official.html       # 前端页面
- package.json                      # 依赖配置
```

#### 2. 安装依赖
```bash
npm install express axios cors
```

#### 3. 配置环境变量
```bash
# 创建 .env 文件
DINGTALK_CLIENT_ID=your_client_id
DINGTALK_CLIENT_SECRET=your_client_secret  
DINGTALK_CORP_ID=your_corp_id
```

#### 4. 启动服务
```bash
# 启动后端
node test-no-proxy-backend.js

# 启动前端 (新终端)
npx http-server -p 30002
```

#### 5. 访问测试
```
http://your_server_ip:30002/test-dingtalk-official.html
```

---

## ⚙️ 钉钉开放平台配置

### 必需配置项

| 配置项 | 值 | 说明 |
|-------|-----|------|
| 应用类型 | 第三方企业应用 | 支持扫码登录 |
| 回调域名 | `http://your_ip:30002` | 替换为实际IP |
| 权限范围 | Contact.User.Read<br>Contact.Department.Read | 用户和部门信息 |
| IP白名单 | 服务器公网IP | 安全限制 |

### 获取配置信息
1. 登录 [钉钉开放平台](https://open-dev.dingtalk.com/)
2. 创建应用 → 第三方企业应用
3. 记录 Client ID、Client Secret、Corp ID
4. 配置回调域名和权限

---

## 📁 项目文件结构

```
dingtalk-login/
├── docs/                           # 文档目录
│   ├── 钉钉登录功能实施文档.md
│   └── 钉钉登录快速部署指南.md
├── logs/                           # 日志目录
├── test-no-proxy-backend.js        # 后端服务 ⭐
├── test-dingtalk-official.html     # 前端页面 ⭐
├── test-enterprise-login.html      # 企业登录页面
├── test-role-fix.html              # 角色测试页面
├── test-detailed-info.html         # 详细信息测试
├── package.json                    # 依赖配置 ⭐
├── .env                           # 环境变量 ⭐
├── start-dev.sh                   # 启动脚本
└── README.md                      # 项目说明
```

---

## 🔧 核心代码片段

### 后端核心配置
```javascript
// test-no-proxy-backend.js 关键配置
const DINGTALK_CONFIG = {
    CLIENT_ID: process.env.DINGTALK_CLIENT_ID || 'your_client_id',
    CLIENT_SECRET: process.env.DINGTALK_CLIENT_SECRET || 'your_secret',
    CORP_ID: process.env.DINGTALK_CORP_ID || 'your_corp_id'
};

const PORT = process.env.PORT || 33018;
```

### 前端核心配置
```javascript
// test-dingtalk-official.html 关键配置
const CONFIG = {
    CLIENT_ID: 'your_client_id',
    REDIRECT_URI: 'http://your_ip:30002/test-dingtalk-official.html',
    BACKEND_URL: 'http://your_ip:33018'
};
```

---

## ✅ 部署检查清单

### 钉钉平台配置
- [ ] 应用已创建并审核通过
- [ ] Client ID、Secret、Corp ID 已获取
- [ ] 回调域名已正确配置
- [ ] 必需权限已申请并通过
- [ ] IP白名单已添加服务器IP

### 服务器配置  
- [ ] Node.js 18+ 已安装
- [ ] 防火墙已开放 30002、33018 端口
- [ ] 环境变量已正确配置
- [ ] 项目文件已上传完整

### 功能测试
- [ ] 后端服务启动正常 (http://ip:33018/health)
- [ ] 前端页面可访问 (http://ip:30002)
- [ ] 二维码可正常生成
- [ ] 扫码登录流程完整
- [ ] 用户信息获取正确

---

## 🐛 常见问题快速解决

### 1. 二维码无法生成
```bash
# 检查钉钉SDK加载
curl -I https://g.alicdn.com/dingding/dinglogin/0.21.0/ddLogin.js

# 检查前端配置
grep -n "CLIENT_ID" test-dingtalk-official.html
```

### 2. 后端服务无法启动
```bash
# 检查端口占用
netstat -tulpn | grep :33018

# 检查Node.js版本
node --version

# 查看错误日志
node test-no-proxy-backend.js
```

### 3. 扫码后无响应
```bash
# 检查回调URL配置
# 确保钉钉平台配置的回调域名与实际访问地址一致

# 检查网络连通性
curl -X POST http://your_ip:33018/api/auth/dingtalk/official-login \
  -H "Content-Type: application/json" \
  -d '{"authCode":"test","state":"test"}'
```

### 4. 用户信息获取失败
```bash
# 检查权限配置
# 确保应用已申请 Contact.User.Read 权限

# 检查企业Token
# 确保 Client ID 和 Secret 正确
```

---

## 📊 性能监控

### 简单监控脚本
```bash
#!/bin/bash
# monitor.sh

echo "🔍 钉钉登录服务监控"
echo "时间: $(date)"

# 检查后端服务
if curl -s http://localhost:33018/health > /dev/null; then
    echo "✅ 后端服务: 正常"
else
    echo "❌ 后端服务: 异常"
fi

# 检查前端服务
if curl -s http://localhost:30002 > /dev/null; then
    echo "✅ 前端服务: 正常"
else
    echo "❌ 前端服务: 异常"
fi

# 检查钉钉API连通性
if curl -s https://oapi.dingtalk.com > /dev/null; then
    echo "✅ 钉钉API: 可达"
else
    echo "❌ 钉钉API: 不可达"
fi
```

### 日志查看
```bash
# 查看实时日志
tail -f logs/dingtalk-combined.log

# 查看错误日志
tail -f logs/dingtalk-error.log

# 查看最近的登录记录
grep "钉钉官方第三方网站登录成功" logs/dingtalk-combined.log | tail -10
```

---

## 🔄 更新升级

### 版本更新步骤
1. 备份当前配置文件
2. 下载新版本代码
3. 更新依赖包
4. 重启服务
5. 验证功能正常

### 备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

cp test-no-proxy-backend.js $BACKUP_DIR/
cp test-dingtalk-official.html $BACKUP_DIR/
cp .env $BACKUP_DIR/
cp -r logs $BACKUP_DIR/

echo "✅ 备份完成: $BACKUP_DIR"
```

---

## 📞 技术支持

### 问题反馈
- 详细描述问题现象
- 提供错误日志
- 说明环境配置信息

### 有用链接
- [钉钉开放平台文档](https://open.dingtalk.com/document/)
- [Node.js官方文档](https://nodejs.org/docs/)
- [Express.js文档](https://expressjs.com/)

---

**🎉 部署完成后，您将拥有一个完整的钉钉第三方登录系统！**

*如需详细技术说明，请参考《钉钉登录功能实施文档.md》*
