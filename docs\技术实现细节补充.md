# 🔧 AI智能客服系统技术实现细节补充

## 📋 文档说明
本文档是对主开发文档的技术细节补充，提供更具体的实现方案和代码示例。

---

## 🤖 AI服务集成详细实现

### 1. AI服务管理器（支持多服务商和容错）
```typescript
// src/lib/ai/ai-service-manager.ts
export interface AIService {
  chat(messages: ChatMessage[], context?: ConversationContext): Promise<string>;
  getEmbedding(text: string): Promise<number[]>;
  isAvailable(): Promise<boolean>;
}

export class AIServiceManager {
  private services: AIService[] = [];
  private currentServiceIndex = 0;

  constructor() {
    // 按优先级添加AI服务
    if (process.env.DEEPSEEK_API_KEY) {
      this.services.push(new DeepSeekService(process.env.DEEPSEEK_API_KEY));
    }
    if (process.env.OPENAI_API_KEY) {
      this.services.push(new OpenAIService(process.env.OPENAI_API_KEY));
    }
  }

  async chat(messages: ChatMessage[], context?: ConversationContext): Promise<string> {
    const startTime = Date.now();
    
    for (let i = 0; i < this.services.length; i++) {
      const serviceIndex = (this.currentServiceIndex + i) % this.services.length;
      const service = this.services[serviceIndex];
      
      try {
        // 检查服务可用性
        if (!(await service.isAvailable())) {
          continue;
        }

        const response = await Promise.race([
          service.chat(messages, context),
          this.timeoutPromise(30000) // 30秒超时
        ]);

        // 记录成功的服务，下次优先使用
        this.currentServiceIndex = serviceIndex;
        
        // 记录性能指标
        await this.recordMetrics(serviceIndex, Date.now() - startTime, true);
        
        return response;
      } catch (error) {
        console.warn(`AI服务 ${serviceIndex} 失败:`, error.message);
        await this.recordMetrics(serviceIndex, Date.now() - startTime, false);
        continue;
      }
    }

    // 所有AI服务都失败，使用降级策略
    return this.getFallbackResponse(messages[messages.length - 1]?.content || '');
  }

  private async timeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('AI服务响应超时')), ms);
    });
  }

  private getFallbackResponse(userMessage: string): string {
    // 基于关键词的简单回复规则
    const fallbackRules = new Map([
      ['门票', '门票相关信息请咨询景区工作人员，电话：0571-12345'],
      ['开放时间', '景区开放时间为8:00-18:00，具体信息请以官方公告为准'],
      ['交通', '交通路线信息请查看景区官网或咨询工作人员'],
      ['路线', '推荐游览路线请参考景区导览图或咨询现场工作人员'],
      ['餐饮', '景区内有多个餐饮点，详情请咨询服务中心'],
      ['停车', '景区提供免费停车场，位置请参考指示牌'],
      ['天气', '请关注天气预报，建议携带雨具'],
      ['紧急', '如遇紧急情况，请立即联系景区安保：0571-12345']
    ]);

    for (const [keyword, response] of fallbackRules) {
      if (userMessage.includes(keyword)) {
        return `${response}\n\n抱歉，AI客服暂时不可用，以上是基础信息供参考。`;
      }
    }

    return '抱歉，AI客服暂时不可用，请联系人工客服获得帮助。\n客服电话：0571-12345\n工作时间：8:00-18:00';
  }

  private async recordMetrics(serviceIndex: number, responseTime: number, success: boolean) {
    // 记录AI服务性能指标到数据库
    try {
      await prisma.aiServiceMetrics.create({
        data: {
          serviceIndex,
          responseTime,
          success,
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('记录AI服务指标失败:', error);
    }
  }
}
```

### 2. DeepSeek服务实现
```typescript
// src/lib/ai/deepseek-service.ts
export class DeepSeekService implements AIService {
  private apiKey: string;
  private baseURL = 'https://api.deepseek.com';
  private retryCount = 3;
  private retryDelay = 1000; // 1秒

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async chat(messages: ChatMessage[], context?: ConversationContext): Promise<string> {
    const systemPrompt = this.buildSystemPrompt(context);
    const formattedMessages = [
      { role: 'system', content: systemPrompt },
      ...messages
    ];

    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      try {
        const response = await fetch(`${this.baseURL}/v1/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: formattedMessages,
            temperature: 0.7,
            max_tokens: 2000,
            stream: false
          })
        });

        if (!response.ok) {
          throw new Error(`DeepSeek API错误: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
          throw new Error('DeepSeek API返回格式错误');
        }

        return data.choices[0].message.content;
      } catch (error) {
        console.warn(`DeepSeek API调用失败 (尝试 ${attempt}/${this.retryCount}):`, error.message);
        
        if (attempt < this.retryCount) {
          await this.delay(this.retryDelay * attempt); // 指数退避
        } else {
          throw error;
        }
      }
    }
  }

  async getEmbedding(text: string): Promise<number[]> {
    // 注意：DeepSeek可能不提供embedding服务，这里使用OpenAI兼容的API
    const response = await fetch(`${this.baseURL}/v1/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'text-embedding-ada-002',
        input: text
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek Embedding API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.data[0].embedding;
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/v1/models`, {
        headers: { 'Authorization': `Bearer ${this.apiKey}` }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private buildSystemPrompt(context?: ConversationContext): string {
    const basePrompt = `你是吴都乔街景区的智能客服助手，专门为游客提供咨询服务。

你的职责：
1. 回答关于景区的各种问题（门票、开放时间、交通、景点介绍等）
2. 提供路线规划和游览建议
3. 协助解决游客遇到的问题
4. 保持友好、专业的服务态度

景区基本信息：
- 开放时间：8:00-18:00（全年无休）
- 门票价格：成人票80元，儿童票40元，老人票60元
- 客服电话：0571-12345
- 地址：浙江省杭州市吴都乔街

回复要求：
- 使用简洁、友好的语言
- 如果不确定答案，请诚实说明并建议联系人工客服
- 对于紧急情况，立即提供联系方式
- 根据用户情绪调整回复语调`;

    if (context) {
      return `${basePrompt}

当前对话上下文：
- 用户意图：${context.intent || '未知'}
- 情感状态：${context.sentiment || '中性'}
- 对话轮次：${context.messages?.length || 0}`;
    }

    return basePrompt;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

---

## 🔍 向量搜索优化实现

### 1. MySQL向量搜索服务
```typescript
// src/lib/knowledge/mysql-vector-search.ts
export class MySQLVectorSearchService {
  private embeddingCache = new Map<string, number[]>();
  private cacheExpiry = 60 * 60 * 1000; // 1小时缓存

  async searchSimilar(query: string, limit: number = 5, threshold: number = 0.7): Promise<KnowledgeItem[]> {
    try {
      // 1. 获取查询向量（带缓存）
      const queryEmbedding = await this.getCachedEmbedding(query);
      
      // 2. 获取候选知识库条目
      const candidates = await this.getCandidates(query);
      
      // 3. 计算相似度并排序
      const scored = candidates
        .map(item => ({
          ...item,
          similarity: this.cosineSimilarity(queryEmbedding, item.embedding)
        }))
        .filter(item => item.similarity >= threshold)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      // 4. 记录搜索日志
      await this.logSearch(query, scored.length);

      return scored;
    } catch (error) {
      console.error('向量搜索失败:', error);
      // 降级到全文搜索
      return this.fallbackToFullTextSearch(query, limit);
    }
  }

  private async getCachedEmbedding(text: string): Promise<number[]> {
    const cacheKey = `embedding:${text}`;
    
    if (this.embeddingCache.has(cacheKey)) {
      return this.embeddingCache.get(cacheKey)!;
    }

    const embedding = await this.aiService.getEmbedding(text);
    
    // 缓存向量，避免重复计算
    this.embeddingCache.set(cacheKey, embedding);
    setTimeout(() => this.embeddingCache.delete(cacheKey), this.cacheExpiry);
    
    return embedding;
  }

  private async getCandidates(query: string): Promise<any[]> {
    // 先用全文搜索缩小候选范围，提高性能
    const fullTextResults = await prisma.$queryRaw`
      SELECT id, title, content, category, embedding, 
             MATCH(title, content) AGAINST(${query} IN NATURAL LANGUAGE MODE) as relevance
      FROM knowledge_base 
      WHERE status = 'active' 
        AND MATCH(title, content) AGAINST(${query} IN NATURAL LANGUAGE MODE)
      ORDER BY relevance DESC
      LIMIT 50
    `;

    // 如果全文搜索结果不足，补充其他活跃条目
    if (fullTextResults.length < 20) {
      const additionalResults = await prisma.knowledgeBase.findMany({
        where: { 
          status: 'active',
          NOT: { id: { in: fullTextResults.map(r => r.id) } }
        },
        orderBy: { useCount: 'desc' },
        take: 30 - fullTextResults.length
      });
      
      return [...fullTextResults, ...additionalResults];
    }

    return fullTextResults;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (!a || !b || a.length !== b.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  private async fallbackToFullTextSearch(query: string, limit: number): Promise<any[]> {
    console.log('降级到全文搜索');
    
    const results = await prisma.$queryRaw`
      SELECT id, title, content, category,
             MATCH(title, content) AGAINST(${query} IN NATURAL LANGUAGE MODE) as relevance
      FROM knowledge_base 
      WHERE status = 'active' 
        AND MATCH(title, content) AGAINST(${query} IN NATURAL LANGUAGE MODE)
      ORDER BY relevance DESC
      LIMIT ${limit}
    `;

    return results.map(item => ({ ...item, similarity: item.relevance }));
  }

  private async logSearch(query: string, resultCount: number) {
    try {
      await prisma.searchLog.create({
        data: {
          query,
          resultCount,
          searchType: 'vector',
          timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('记录搜索日志失败:', error);
    }
  }
}
```

---

## 📊 性能监控和告警

### 1. 性能监控服务
```typescript
// src/lib/monitoring/performance-monitor.ts
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>();
  private alertThresholds = {
    apiResponseTime: 3000,    // 3秒
    dbQueryTime: 1000,        // 1秒
    aiResponseTime: 10000,    // 10秒
    errorRate: 0.05,          // 5%
    memoryUsage: 0.8          // 80%
  };

  // 记录API响应时间
  recordApiResponse(endpoint: string, duration: number, success: boolean) {
    const key = `api:${endpoint}`;
    this.addMetric(key, duration);
    
    if (duration > this.alertThresholds.apiResponseTime) {
      this.sendAlert('API响应时间过长', { endpoint, duration });
    }
    
    if (!success) {
      this.recordError(endpoint);
    }
  }

  // 记录数据库查询时间
  recordDbQuery(query: string, duration: number) {
    this.addMetric('db:query', duration);
    
    if (duration > this.alertThresholds.dbQueryTime) {
      this.sendAlert('数据库查询时间过长', { query, duration });
    }
  }

  // 记录AI服务响应时间
  recordAiResponse(service: string, duration: number, success: boolean) {
    const key = `ai:${service}`;
    this.addMetric(key, duration);
    
    if (duration > this.alertThresholds.aiResponseTime) {
      this.sendAlert('AI服务响应时间过长', { service, duration });
    }
  }

  // 获取性能统计
  getStats(key: string, timeWindow: number = 300000): PerformanceStats {
    const metrics = this.metrics.get(key) || [];
    const cutoff = Date.now() - timeWindow;
    const recentMetrics = metrics.filter(m => m > cutoff);
    
    if (recentMetrics.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }

    return {
      avg: recentMetrics.reduce((a, b) => a + b, 0) / recentMetrics.length,
      min: Math.min(...recentMetrics),
      max: Math.max(...recentMetrics),
      count: recentMetrics.length
    };
  }

  private addMetric(key: string, value: number) {
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const metrics = this.metrics.get(key)!;
    metrics.push(value);
    
    // 保持最近1000条记录
    if (metrics.length > 1000) {
      metrics.splice(0, metrics.length - 1000);
    }
  }

  private recordError(endpoint: string) {
    // 记录错误率
    const errorKey = `error:${endpoint}`;
    this.addMetric(errorKey, 1);
    
    // 检查错误率是否超过阈值
    const stats = this.getStats(errorKey);
    const totalRequests = this.getStats(`api:${endpoint}`).count;
    const errorRate = totalRequests > 0 ? stats.count / totalRequests : 0;
    
    if (errorRate > this.alertThresholds.errorRate) {
      this.sendAlert('错误率过高', { endpoint, errorRate });
    }
  }

  private async sendAlert(message: string, data: any) {
    console.error(`🚨 告警: ${message}`, data);
    
    // 这里可以集成钉钉、邮件等告警通道
    try {
      await this.sendDingTalkAlert(message, data);
    } catch (error) {
      console.error('发送告警失败:', error);
    }
  }

  private async sendDingTalkAlert(message: string, data: any) {
    // 钉钉机器人告警实现
    const webhook = process.env.DINGTALK_ALERT_WEBHOOK;
    if (!webhook) return;

    const payload = {
      msgtype: 'text',
      text: {
        content: `【AI客服系统告警】\n${message}\n详情: ${JSON.stringify(data, null, 2)}`
      }
    };

    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
  }
}

interface PerformanceStats {
  avg: number;
  min: number;
  max: number;
  count: number;
}
```

---

## 🔒 安全增强实现

### 1. 输入验证和过滤
```typescript
// src/lib/security/input-validator.ts
export class InputValidator {
  private maxMessageLength = 2000;
  private maxConversationLength = 100;
  private bannedPatterns = [
    /script\s*>/i,           // XSS防护
    /javascript:/i,          // XSS防护
    /on\w+\s*=/i,           // 事件处理器
    /<\s*iframe/i,          // iframe注入
    /eval\s*\(/i,           // 代码执行
    /document\./i,          // DOM操作
    /window\./i             // 窗口对象
  ];

  validateUserMessage(message: string): ValidationResult {
    const errors: string[] = [];

    // 长度检查
    if (!message || message.trim().length === 0) {
      errors.push('消息不能为空');
    }
    
    if (message.length > this.maxMessageLength) {
      errors.push(`消息长度不能超过${this.maxMessageLength}字符`);
    }

    // 安全检查
    for (const pattern of this.bannedPatterns) {
      if (pattern.test(message)) {
        errors.push('消息包含不安全内容');
        break;
      }
    }

    // 敏感词检查
    const sensitiveWords = this.checkSensitiveWords(message);
    if (sensitiveWords.length > 0) {
      errors.push(`消息包含敏感词: ${sensitiveWords.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedMessage: this.sanitizeMessage(message)
    };
  }

  private checkSensitiveWords(message: string): string[] {
    const sensitiveWords = [
      '政治敏感词', '违法内容', '不当言论'
      // 根据实际需要添加敏感词
    ];

    return sensitiveWords.filter(word => 
      message.toLowerCase().includes(word.toLowerCase())
    );
  }

  private sanitizeMessage(message: string): string {
    return message
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedMessage: string;
}
```

---

*技术实现细节补充文档版本：v1.0*  
*创建时间：2025-01-28*  
*配合主开发文档使用*
