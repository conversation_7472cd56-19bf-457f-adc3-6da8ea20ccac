import { NextRequest, NextResponse } from 'next/server';
import { conversationManager } from '@/lib/ai/conversation-manager';

// POST /api/ai/chat - 发送消息并获取AI回复
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, sessionId, userId, intent, sentiment } = body;

    // 验证必需参数
    if (!message || !sessionId) {
      return NextResponse.json(
        { success: false, error: '缺少必需参数: message 和 sessionId' },
        { status: 400 }
      );
    }

    console.log('🤖 收到AI对话请求:', {
      sessionId,
      userId,
      messageLength: message.length,
      intent,
      sentiment
    });

    // 获取或创建对话
    const conversation = await conversationManager.getOrCreateConversationBySession(
      sessionId,
      userId
    );

    // 发送消息并获取AI回复
    const result = await conversationManager.sendMessage({
      conversationId: conversation.id,
      content: message,
      role: 'USER' as any,
      intent,
      sentiment,
      metadata: {
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || 'unknown'
      }
    });

    if (result.error) {
      return NextResponse.json({
        success: false,
        error: result.error,
        conversationId: conversation.id,
        userMessage: {
          id: result.userMessage.id,
          content: result.userMessage.content,
          createdAt: result.userMessage.createdAt
        }
      });
    }

    return NextResponse.json({
      success: true,
      conversationId: conversation.id,
      userMessage: {
        id: result.userMessage.id,
        content: result.userMessage.content,
        createdAt: result.userMessage.createdAt
      },
      assistantMessage: result.assistantMessage ? {
        id: result.assistantMessage.id,
        content: result.assistantMessage.content,
        createdAt: result.assistantMessage.createdAt,
        responseTime: result.assistantMessage.responseTimeMs
      } : null
    });

  } catch (error: any) {
    console.error('❌ AI对话API错误:', error);
    return NextResponse.json(
      { success: false, error: error.message || '服务器内部错误' },
      { status: 500 }
    );
  }
}

// GET /api/ai/chat?sessionId=xxx - 获取对话历史
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const conversationId = searchParams.get('conversationId');

    if (!sessionId && !conversationId) {
      return NextResponse.json(
        { success: false, error: '缺少必需参数: sessionId 或 conversationId' },
        { status: 400 }
      );
    }

    let conversation;

    if (conversationId) {
      // 根据conversationId获取对话
      conversation = await conversationManager.getConversationWithMessages(conversationId);
    } else if (sessionId) {
      // 根据sessionId获取对话
      const conv = await conversationManager.getOrCreateConversationBySession(sessionId);
      conversation = await conversationManager.getConversationWithMessages(conv.id);
    }

    if (!conversation) {
      return NextResponse.json(
        { success: false, error: '对话不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      conversation: {
        id: conversation.id,
        sessionId: conversation.sessionId,
        title: conversation.title,
        status: conversation.status,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
        messages: conversation.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          intent: msg.intent,
          sentiment: msg.sentiment,
          createdAt: msg.createdAt
        }))
      }
    });

  } catch (error: any) {
    console.error('❌ 获取对话历史API错误:', error);
    return NextResponse.json(
      { success: false, error: error.message || '服务器内部错误' },
      { status: 500 }
    );
  }
}
