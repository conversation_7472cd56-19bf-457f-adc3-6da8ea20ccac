import { NextRequest, NextResponse } from 'next/server';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';

// 快速回复函数
function getFallbackResponse(content: string): string {
  const lowerContent = content.toLowerCase();

  if (lowerContent.includes('你好') || lowerContent.includes('hello')) {
    return '您好！我是吴都乔街景区智能客服😊\n\n我可以为您提供：\n• 景区信息咨询\n• 门票购买指导\n• 交通路线查询\n\n请问有什么可以帮您的？';
  }

  if (lowerContent.includes('停车')) {
    return '🚗 停车信息：\n• 景区停车场：10元/次\n• 车位约200个\n• 建议公交出行更便捷\n\n需要了解交通路线吗？';
  }

  if (lowerContent.includes('门票') || lowerContent.includes('票价')) {
    return '🎫 门票价格：\n• 成人票：80元\n• 儿童票：40元\n• 老人票：60元\n\n可在景区入口或线上购买。';
  }

  if (lowerContent.includes('时间') || lowerContent.includes('开放')) {
    return '⏰ 开放时间：\n• 每日 8:00-18:00\n• 全年无休\n\n建议您提前1小时到达哦！';
  }

  return '您好！我是吴都乔街景区智能客服。\n\n您可以询问：\n• 门票价格\n• 开放时间\n• 交通路线\n• 停车信息\n\n有什么可以帮您的吗？';
}

// 添加GET方法用于测试连通性
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'ok',
    message: '微信消息接口运行正常',
    timestamp: new Date().toISOString(),
    url: request.url
  });
}

export async function POST(request: NextRequest) {
  try {
    console.log('[本地服务] ========== 收到云托管转发的消息 ==========');
    console.log('[本地服务] Headers:', Object.fromEntries(request.headers.entries()));
    
    // 验证请求来源
    const isFromRelay = request.headers.get('X-Wechat-Relay') === 'true';
    const isAsyncMode = request.headers.get('X-Async-Mode') === 'true';
    const isTwoStageMode = request.headers.get('X-Two-Stage-Reply') === 'true';

    if (!isFromRelay) {
      console.warn('[本地服务] 非云托管中转的请求，拒绝处理');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    console.log(`[本地服务] 处理模式: ${isAsyncMode ? '异步' : '同步'} ${isTwoStageMode ? '(双阶段)' : ''}`);

    const messageData = await request.json();
    console.log('[本地服务] 消息数据:', JSON.stringify(messageData, null, 2));
    
    // 提取用户信息和消息内容
    const {
      toUserName,    // 公众号原始ID
      fromUserName,  // 用户OpenID
      createTime,    // 消息时间
      msgType,       // 消息类型
      content,       // 消息内容
      msgId,         // 消息ID
      callbackUrl    // 双阶段回复的回调地址
    } = messageData;
    
    // 检查消息类型
    if (msgType !== 'text') {
      console.log('[本地服务] 非文本消息，暂不处理');
      return NextResponse.json({ 
        status: 'success',
        message: '收到非文本消息'
      });
    }
    
    // 检查消息内容
    if (!content || content.trim() === '') {
      console.log('[本地服务] 空消息内容');
      return NextResponse.json({ 
        status: 'success',
        message: '收到空消息'
      });
    }
    
    console.log('[本地服务] 处理用户消息:', content);
    console.log('[本地服务] 用户OpenID:', fromUserName);
    console.log('[本地服务] 回调地址:', callbackUrl);

    // 双阶段回复模式：立即返回临时回复，异步处理AI
    if (isTwoStageMode && callbackUrl) {
      console.log('[本地服务] 🚀 启动双阶段回复模式');

      // 生成智能临时回复
      const tempReply = generateSmartTempReply(content);

      // 立即返回临时回复标识
      const immediateResponse = NextResponse.json({
        status: 'processing',
        tempReply: tempReply,
        mode: 'two-stage',
        sessionId: `wechat_${fromUserName}_${Date.now()}`
      });

      // 异步处理AI回复
      processAIAsync(content, fromUserName, callbackUrl).catch(error => {
        console.error('[本地服务] 异步AI处理失败:', error);
      });

      return immediateResponse;
    }

    // 传统模式：AI对话系统 - 根据模式选择处理方式
    let aiResponse = '';

    try {
      console.log(`[本地服务] 开始AI对话 - ${isAsyncMode ? '异步模式' : '快速模式'}`);
      const startTime = Date.now();

      // 简单的对话上下文
      const context = {
        sessionId: `wechat_${fromUserName}`,
        userId: fromUserName,
        platform: 'wechat'
      };

      // 根据模式选择不同的处理策略
      let result;

      if (isAsyncMode) {
        // 异步模式：无超时限制，可以使用完整AI功能
        console.log('[本地服务] 异步模式：调用完整AI服务');
        result = await aiServiceManager.chat([
          {
            role: 'system',
            content: '你是吴都乔街景区的专业智能客服助手。请详细、准确地回答用户问题，提供有用的景区信息和建议。'
          },
          {
            role: 'user',
            content: content
          }
        ], context);
      } else {
        // 同步模式：3秒超时限制
        console.log('[本地服务] 同步模式：快速AI回复');
        const aiPromise = aiServiceManager.chat([
          {
            role: 'system',
            content: '你是吴都乔街景区客服。简洁回答用户问题，不超过100字。'
          },
          {
            role: 'user',
            content: content
          }
        ], context);

        // 设置3秒超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('AI响应超时')), 3000);
        });

        result = await Promise.race([aiPromise, timeoutPromise]);
      }

      const responseTime = Date.now() - startTime;
      console.log(`[本地服务] AI回复完成，耗时: ${responseTime}ms`);

      if (result.success) {
        aiResponse = result.response;
        console.log('[本地服务] AI回复生成成功，长度:', aiResponse.length);
      } else {
        console.error('[本地服务] AI回复生成失败:', result.error);
        aiResponse = getFallbackResponse(content);
      }

    } catch (aiError) {
      console.error('[本地服务] AI处理失败:', aiError.message);

      // 如果是超时错误，使用快速回复
      if (aiError.message === 'AI响应超时') {
        console.log('[本地服务] AI超时，使用快速回复');
        aiResponse = getFallbackResponse(content);
      } else {
        aiResponse = '抱歉，AI服务暂时不可用，请稍后再试或联系人工客服。';
      }
    }
    
    console.log('[本地服务] AI回复:', aiResponse);

    // 根据模式返回不同格式
    if (isAsyncMode) {
      // 异步模式：返回简单状态，回复内容由云托管通过客服API发送
      return NextResponse.json({
        status: 'success',
        reply: aiResponse,
        mode: 'async',
        messageInfo: {
          msgId,
          fromUser: fromUserName,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      // 同步模式：返回回复内容给云托管中转服务
      return NextResponse.json({
        status: 'success',
        reply: aiResponse,
        mode: 'sync',
        messageInfo: {
          msgId,
          fromUser: fromUserName,
          toUser: toUserName,
          processTime: new Date().toISOString()
        }
      });
    }
    
  } catch (error) {
    console.error('[本地服务] 处理消息失败:', error);
    
    return NextResponse.json({
      status: 'error',
      message: '消息处理失败',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// 生成智能临时回复
function generateSmartTempReply(userMessage: string): string {
  const message = userMessage.toLowerCase();
  const hour = new Date().getHours();

  // 🕐 时间感知 - 优先级最高
  const timeGreeting = getTimeBasedGreeting(hour);

  // 😊 情感分析 - 检测用户情绪
  const emotionResponse = analyzeUserEmotion(message);
  if (emotionResponse) {
    return emotionResponse;
  }

  // 景区相关
  if (message.includes('景区') || message.includes('门票') || message.includes('开放时间') ||
      message.includes('营业时间') || message.includes('票价') || message.includes('门票价格')) {
    return `🏛️ ${timeGreeting}小乔正在为您查询景区相关信息，请稍候...`;
  }

  // 交通路线
  if (message.includes('路线') || message.includes('怎么去') || message.includes('交通') ||
      message.includes('地址') || message.includes('位置') || message.includes('怎么走') ||
      message.includes('公交') || message.includes('地铁') || message.includes('开车')) {
    return `🗺️ ${timeGreeting}小乔正在为您规划最佳路线，请稍等...`;
  }

  // 美食餐饮
  if (message.includes('美食') || message.includes('餐厅') || message.includes('小吃') ||
      message.includes('吃饭') || message.includes('特色菜') || message.includes('推荐菜')) {
    return `🍜 ${timeGreeting}小乔正在为您推荐美食信息，请稍候...`;
  }

  // 住宿相关
  if (message.includes('住宿') || message.includes('酒店') || message.includes('民宿') ||
      message.includes('宾馆') || message.includes('过夜') || message.includes('住哪')) {
    return "🏨 小乔正在为您查找住宿信息，请稍等...";
  }

  // 活动娱乐
  if (message.includes('活动') || message.includes('表演') || message.includes('节目') ||
      message.includes('演出') || message.includes('娱乐') || message.includes('玩什么')) {
    return "🎭 小乔正在为您查询活动信息，请稍候...";
  }

  // 购物相关
  if (message.includes('购物') || message.includes('特产') || message.includes('纪念品') ||
      message.includes('商店') || message.includes('买什么') || message.includes('礼品')) {
    return "🛍️ 小乔正在为您推荐购物信息，请稍等...";
  }

  // 天气相关
  if (message.includes('天气') || message.includes('温度') || message.includes('下雨') ||
      message.includes('晴天') || message.includes('穿什么')) {
    return "🌤️ 小乔正在为您查询天气信息，请稍候...";
  }

  // 问题咨询
  if (message.includes('问题') || message.includes('咨询') || message.includes('帮助') ||
      message.includes('客服') || message.includes('投诉') || message.includes('建议')) {
    return "💬 小乔正在为您转接专业客服，请稍等...";
  }

  // 基于问句类型的智能判断
  if (message.includes('什么时候') || message.includes('几点') || message.includes('时间')) {
    return "⏰ 小乔正在为您查询时间信息，请稍候...";
  }

  if (message.includes('多少钱') || message.includes('价格') || message.includes('费用') ||
      message.includes('收费') || message.includes('花费')) {
    return "💰 小乔正在为您查询价格信息，请稍等...";
  }

  if (message.includes('怎么样') || message.includes('好不好') || message.includes('值得') ||
      message.includes('推荐')) {
    return "⭐ 小乔正在为您分析推荐信息，请稍候...";
  }

  // 基于消息长度和复杂度的智能选择
  if (message.length > 20) {
    return `🧠 ${timeGreeting}小乔正在仔细分析您的问题，请稍等片刻...`;
  } else if (message.length > 10) {
    return `🔍 ${timeGreeting}小乔正在为您搜索相关信息，请稍候...`;
  } else {
    return `💭 ${timeGreeting}小乔正在思考您的问题，马上回复...`;
  }
}

// 🕐 时间感知功能
function getTimeBasedGreeting(hour: number): string {
  if (hour >= 5 && hour < 9) {
    return "🌅 早上好！";
  } else if (hour >= 9 && hour < 12) {
    return "☀️ 上午好！";
  } else if (hour >= 12 && hour < 14) {
    return "🌞 中午好！";
  } else if (hour >= 14 && hour < 18) {
    return "🌤️ 下午好！";
  } else if (hour >= 18 && hour < 22) {
    return "🌆 晚上好！";
  } else {
    return "🌙 夜深了，";
  }
}

// 😊 情感分析功能
function analyzeUserEmotion(message: string): string | null {
  // 感谢情绪
  if (message.includes('谢谢') || message.includes('感谢') || message.includes('太好了') ||
      message.includes('非常感谢') || message.includes('多谢')) {
    return "😊 不客气！小乔很高兴为您服务，正在查询中...";
  }

  // 急迫情绪
  if (message.includes('急') || message.includes('快') || message.includes('马上') ||
      message.includes('立即') || message.includes('赶紧') || message.includes('速度')) {
    return "⚡ 小乔明白您很着急，正在优先为您处理，请稍等...";
  }

  // 困惑情绪
  if (message.includes('不懂') || message.includes('不明白') || message.includes('搞不清') ||
      message.includes('糊涂') || message.includes('不知道') || message.includes('迷茫')) {
    return "🤗 别担心，小乔来帮您解答疑惑，正在查询中...";
  }

  // 抱怨情绪
  if (message.includes('太贵') || message.includes('坑人') || message.includes('不满意') ||
      message.includes('差评') || message.includes('投诉') || message.includes('退款') ||
      message.includes('垃圾') || message.includes('骗人') || message.includes('失望') ||
      message.includes('糟糕') || message.includes('恶心') || message.includes('坑爹')) {
    return "😔 小乔理解您的感受，正在记录您反馈的信息...";
  }

  // 兴奋情绪
  if (message.includes('太棒了') || message.includes('好期待') || message.includes('超级') ||
      message.includes('太赞') || message.includes('amazing') || message.includes('哇')) {
    return "🎉 小乔也很兴奋！正在为您准备精彩信息...";
  }

  // 疑问情绪
  if (message.includes('真的吗') || message.includes('确定吗') || message.includes('靠谱吗') ||
      message.includes('可信吗') || message.includes('是否')) {
    return "🔍 小乔会为您提供准确信息，正在核实中...";
  }

  // 第一次来访
  if (message.includes('第一次') || message.includes('初次') || message.includes('新手') ||
      message.includes('没来过') || message.includes('不熟悉')) {
    return "👋 欢迎第一次来到吴都乔街！小乔正在为您准备新手指南...";
  }

  return null; // 没有检测到特殊情绪，继续常规处理
}

// 异步处理AI回复
async function processAIAsync(userMessage: string, fromUserName: string, callbackUrl: string) {
  try {
    console.log('[本地服务] 🔄 开始异步AI处理...');
    const startTime = Date.now();

    // 简单的对话上下文
    const context = {
      sessionId: `wechat_${fromUserName}`,
      userId: fromUserName,
      platform: 'wechat'
    };

    // 调用完整AI服务（无超时限制）
    const result = await aiServiceManager.chat([
      {
        role: 'system',
        content: '你是吴都乔街景区的专业智能客服助手。请详细、准确地回答用户问题，提供有用的景区信息和建议。回复要友好、专业，并且信息准确。'
      },
      {
        role: 'user',
        content: userMessage
      }
    ], context);

    const aiResponse = result.success ? result.response : '抱歉，我暂时无法处理您的问题，请稍后重试或联系人工客服。';
    const processingTime = Date.now() - startTime;

    console.log(`[本地服务] ✅ AI处理完成，耗时: ${processingTime}ms`);
    console.log('[本地服务] AI回复:', aiResponse.substring(0, 100) + '...');

    // 发送正式回复到中转平台
    console.log('[本地服务] 📤 发送正式回复到中转平台:', callbackUrl);

    const response = await fetch(callbackUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fromUserName: fromUserName,
        finalReply: aiResponse,
        sessionId: context.sessionId,
        processingTime: processingTime
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('[本地服务] ✅ 正式回复发送成功:', result);
    } else {
      console.error('[本地服务] ❌ 正式回复发送失败:', response.status, response.statusText);
    }

  } catch (error) {
    console.error('[本地服务] ❌ 异步AI处理失败:', error);

    // 发送错误回复
    try {
      await fetch(callbackUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromUserName: fromUserName,
          finalReply: '抱歉，系统出现问题，请稍后再试或联系人工客服。',
          sessionId: `wechat_${fromUserName}`,
          error: true
        })
      });
    } catch (sendError) {
      console.error('[本地服务] ❌ 发送错误回复也失败了:', sendError);
    }
  }
}
