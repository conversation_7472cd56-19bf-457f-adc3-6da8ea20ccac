import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedKnowledgeBase() {
  console.log('🌱 开始初始化知识库数据...');

  try {
    // 1. 创建测试用户（如果不存在）
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          id: 'test-admin-user',
          unionId: 'test-admin-union-id',
          name: '系统管理员',
          email: '<EMAIL>',
          avatar: null
        }
      });
    }

    // 2. 创建知识库分类
    const categories = await Promise.all([
      prisma.kbCategory.create({
        data: {
          name: '景区信息',
          description: '关于吴都乔街景区的基本信息',
          color: '#3B82F6',
          sortOrder: 1,
          status: 'ACTIVE'
        }
      }),
      prisma.kbCategory.create({
        data: {
          name: '门票相关',
          description: '门票价格、购买方式、优惠政策等',
          color: '#10B981',
          sortOrder: 2,
          status: 'ACTIVE'
        }
      }),
      prisma.kbCategory.create({
        data: {
          name: '交通指南',
          description: '如何到达景区，停车信息等',
          color: '#F59E0B',
          sortOrder: 3,
          status: 'ACTIVE'
        }
      }),
      prisma.kbCategory.create({
        data: {
          name: '游览攻略',
          description: '推荐路线、景点介绍、游玩建议',
          color: '#8B5CF6',
          sortOrder: 4,
          status: 'ACTIVE'
        }
      })
    ]);

    console.log(`✅ 创建了 ${categories.length} 个分类`);

    // 3. 创建标签
    const tagData = [
      { name: '门票', description: '门票相关信息', color: '#EF4444' },
      { name: '开放时间', description: '营业时间相关', color: '#3B82F6' },
      { name: '交通', description: '交通出行相关', color: '#10B981' },
      { name: '停车', description: '停车相关信息', color: '#F59E0B' },
      { name: '优惠', description: '优惠政策相关', color: '#8B5CF6' }
    ];

    const tags = [];
    for (const tag of tagData) {
      const createdTag = await prisma.kbTag.upsert({
        where: { name: tag.name },
        update: tag,
        create: {
          ...tag,
          createdBy: testUser.id
        }
      });
      tags.push(createdTag);
    }

    console.log(`✅ 创建了 ${tags.length} 个标签`);

    // 4. 创建知识库条目
    const knowledgeItems = [
      {
        title: '吴都乔街景区基本信息',
        content: `吴都乔街景区位于浙江省杭州市，是一个集历史文化、休闲娱乐为一体的综合性景区。

景区特色：
- 历史悠久的古街建筑
- 传统文化体验项目
- 特色美食街区
- 文创产品展示

景区面积：约50公顷
建设时间：始建于明代，现代化改造于2020年
主要景点：古街核心区、文化展示馆、美食广场、休闲公园`,
        summary: '吴都乔街景区是杭州市的历史文化景区，集古街建筑、文化体验、美食休闲于一体',
        categoryId: categories[0].id,
        tagIds: [],
        priority: 5
      },
      {
        title: '门票价格及购买方式',
        content: `门票价格：
- 成人票：80元/人
- 儿童票：40元/人（身高1.2-1.5米）
- 老人票：60元/人（60岁以上，凭身份证）
- 学生票：50元/人（凭学生证）

购买方式：
1. 现场购票：景区入口售票处
2. 网上购票：官方网站、微信小程序
3. 第三方平台：携程、美团、大众点评等

优惠政策：
- 身高1.2米以下儿童免费
- 70岁以上老人免费（凭身份证）
- 残疾人免费（凭残疾证）
- 军人免费（凭军官证）`,
        summary: '成人票80元，儿童票40元，老人票60元，支持现场和网上购票，多种优惠政策',
        categoryId: categories[1].id,
        tagIds: [tags[0].id, tags[4].id],
        priority: 5
      },
      {
        title: '景区开放时间',
        content: `开放时间：
- 夏季（4月-10月）：8:00-18:30
- 冬季（11月-3月）：8:30-17:30
- 全年无休，节假日正常开放

特殊时间安排：
- 春节期间：正常开放，可能会有特殊活动
- 恶劣天气：可能临时调整，请关注官方通知

最佳游览时间：
- 上午9:00-11:00：人流较少，适合拍照
- 下午14:00-16:00：阳光充足，景色最佳
- 傍晚17:00-18:00：夕阳西下，氛围浪漫`,
        summary: '夏季8:00-18:30，冬季8:30-17:30，全年无休，建议上午或下午游览',
        categoryId: categories[0].id,
        tagIds: [tags[1].id],
        priority: 4
      },
      {
        title: '交通指南及停车信息',
        content: `公共交通：
地铁：
- 地铁2号线至"吴都乔街站"，A出口步行5分钟
- 地铁1号线至"市中心站"，换乘公交K123路

公交：
- K123路、K456路、K789路直达景区门口
- 班次频繁，约10-15分钟一班

自驾路线：
- 从杭州市区：沿中河高架→上塘高架→吴都乔街出口
- 从萧山机场：机场高速→绕城高速→吴都乔街出口
- 导航地址：杭州市吴都乔街1号

停车信息：
- 景区停车场：500个车位，10元/小时
- 周边停车场：多个选择，价格5-8元/小时
- 节假日建议提前到达，避免停车困难`,
        summary: '地铁2号线直达，多路公交可达，自驾有专用停车场，10元/小时',
        categoryId: categories[2].id,
        tagIds: [tags[2].id, tags[3].id],
        priority: 4
      },
      {
        title: '推荐游览路线',
        content: `经典路线（2-3小时）：
景区入口 → 古街核心区 → 文化展示馆 → 美食广场 → 休闲公园 → 出口

深度游路线（4-5小时）：
景区入口 → 古街核心区 → 传统手工艺体验 → 文化展示馆 → 午餐（美食广场）→ 文创产品区 → 休闲公园 → 茶艺体验 → 出口

亲子游路线（3-4小时）：
景区入口 → 儿童游乐区 → 古街核心区（简单游览）→ 美食广场 → 休闲公园 → 亲子活动区 → 出口

摄影路线（全天）：
早晨：古街晨光 → 上午：建筑细节 → 中午：人文活动 → 下午：自然风光 → 傍晚：夕阳美景

注意事项：
- 建议穿舒适的鞋子
- 携带充电宝和相机
- 关注天气变化`,
        summary: '提供经典、深度、亲子、摄影四种路线，建议2-5小时游览时间',
        categoryId: categories[3].id,
        tagIds: [],
        priority: 3
      }
    ];

    for (const item of knowledgeItems) {
      const { tagIds, ...itemData } = item;
      
      const knowledgeItem = await prisma.knowledgeBase.create({
        data: {
          ...itemData,
          status: 'ACTIVE',
          createdBy: testUser.id,
          tags: tagIds.length > 0 ? {
            create: tagIds.map(tagId => ({ tagId }))
          } : undefined
        }
      });

      // 创建初始版本
      await prisma.kbVersion.create({
        data: {
          knowledgeId: knowledgeItem.id,
          versionNumber: 1,
          title: knowledgeItem.title,
          content: knowledgeItem.content,
          changeLog: '初始版本',
          createdBy: testUser.id
        }
      });
    }

    console.log(`✅ 创建了 ${knowledgeItems.length} 个知识条目`);

    console.log('🎉 知识库数据初始化完成！');

  } catch (error) {
    console.error('❌ 初始化知识库数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedKnowledgeBase();
}

export { seedKnowledgeBase };
