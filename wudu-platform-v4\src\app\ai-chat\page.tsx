'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { ChatWindow } from '@/components/ai/ChatWindow';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Bot, Settings, BarChart3, MessageSquare, Users, Clock } from 'lucide-react';
import { useAuth } from '@/lib/auth';

export default function AIChatPage() {
  const { user } = useAuth();
  const [aiStatus, setAiStatus] = useState<any>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  // 移除流式聊天相关状态

  const checkAIStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const response = await fetch('/api/ai/status');
      const data = await response.json();
      setAiStatus(data.status);
    } catch (error) {
      console.error('获取AI状态失败:', error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  const testAIService = async () => {
    try {
      const response = await fetch('/api/ai/status/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testMessage: '你好，请介绍一下景区' })
      });
      const data = await response.json();
      
      if (data.success) {
        alert(`AI测试成功！\n响应时间: ${data.testResult.responseTime}ms\n服务: ${data.testResult.serviceUsed}`);
      } else {
        alert(`AI测试失败: ${data.error}`);
      }
    } catch (error) {
      alert('AI测试失败: 网络错误');
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">🤖 AI智能客服</h1>
          <p className="text-muted-foreground mt-2">
            吴都乔街景区智能客服系统 - 为游客提供24小时在线咨询服务
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={checkAIStatus} disabled={isLoadingStatus}>
            {isLoadingStatus ? '检查中...' : '检查状态'}
          </Button>
          <Button onClick={testAIService} variant="outline">
            测试AI服务
          </Button>
        </div>
      </div>

      <Tabs defaultValue="chat" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            AI对话
          </TabsTrigger>
          <TabsTrigger value="status" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            系统状态
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            数据分析
          </TabsTrigger>
        </TabsList>

        {/* AI对话标签页 */}
        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 主聊天窗口 */}
            <div className="lg:col-span-2">
              <ChatWindow
                sessionId={`user_${user?.id || 'guest'}_${Date.now()}`}
                height="700px"
              />
            </div>
            
            {/* 侧边栏信息 */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">💡 使用提示</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-2">
                    <p><strong>🎫 门票咨询:</strong> "门票多少钱？"</p>
                    <p><strong>🕐 开放时间:</strong> "景区几点开门？"</p>
                    <p><strong>🚗 交通指南:</strong> "怎么到景区？"</p>
                    <p><strong>🗺️ 游览路线:</strong> "推荐游览路线"</p>
                    <p><strong>🍽️ 餐饮服务:</strong> "景区有餐厅吗？"</p>
                    <p><strong>🅿️ 停车信息:</strong> "停车场在哪里？"</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">📞 联系方式</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="text-sm">
                    <p><strong>客服电话:</strong> 0571-12345</p>
                    <p><strong>工作时间:</strong> 8:00-18:00</p>
                    <p><strong>紧急联系:</strong> 0571-12345</p>
                    <p><strong>地址:</strong> 浙江省杭州市吴都乔街</p>
                  </div>
                </CardContent>
              </Card>

              {user && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">👤 当前用户</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Users className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* 系统状态标签页 */}
        <TabsContent value="status" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  AI服务状态
                </CardTitle>
              </CardHeader>
              <CardContent>
                {aiStatus ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>整体健康度</span>
                      <Badge variant={aiStatus.healthy ? "default" : "destructive"}>
                        {aiStatus.healthScore}%
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      {aiStatus.services.map((service: any, index: number) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm">{service.name}</span>
                          <Badge variant={service.available ? "default" : "destructive"}>
                            {service.available ? "在线" : "离线"}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">点击"检查状态"获取AI服务状态</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  对话统计
                </CardTitle>
              </CardHeader>
              <CardContent>
                {aiStatus?.stats ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>24小时对话数</span>
                      <span className="font-medium">{aiStatus.stats.last24Hours.conversations}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>消息总数</span>
                      <span className="font-medium">{aiStatus.stats.last24Hours.messages}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>平均响应时间</span>
                      <span className="font-medium">{aiStatus.stats.last24Hours.avgResponseTime}ms</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">暂无统计数据</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  系统信息
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>系统版本</span>
                    <span className="font-medium">v2.0</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>部署环境</span>
                    <span className="font-medium">生产环境</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>最后更新</span>
                    <span className="font-medium">
                      {aiStatus?.timestamp ? 
                        new Date(aiStatus.timestamp).toLocaleString('zh-CN') : 
                        '未知'
                      }
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 数据分析标签页 */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>📊 数据分析大屏</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 mx-auto text-muted-foreground/50 mb-4" />
                <h3 className="text-lg font-medium mb-2">数据分析功能开发中</h3>
                <p className="text-muted-foreground">
                  即将推出用户行为分析、满意度统计、热门问题分析等功能
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </MainLayout>
  );
}
