import { NextRequest, NextResponse } from 'next/server';

// 简单的DeepSeek API性能测试
export async function POST(request: NextRequest) {
  try {
    const { apiKey } = await request.json();
    
    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: 'API密钥为空'
      }, { status: 400 });
    }

    console.log('🧪 开始DeepSeek API性能测试...');
    
    // 测试1: 简单的模型列表请求
    const modelsStartTime = Date.now();
    try {
      const modelsResponse = await fetch('https://api.deepseek.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(10000)
      });
      
      const modelsTime = Date.now() - modelsStartTime;
      console.log(`📋 模型列表请求耗时: ${modelsTime}ms`);
      
      if (!modelsResponse.ok) {
        throw new Error(`模型列表请求失败: ${modelsResponse.status}`);
      }
    } catch (error: any) {
      console.error('❌ 模型列表请求失败:', error.message);
      return NextResponse.json({
        success: false,
        error: `模型列表请求失败: ${error.message}`,
        modelsTime: Date.now() - modelsStartTime
      });
    }

    // 测试2: 简单的聊天请求
    const chatStartTime = Date.now();
    try {
      const chatResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: '你好' }
          ],
          temperature: 0.7,
          max_tokens: 50
        }),
        signal: AbortSignal.timeout(20000)
      });
      
      const chatTime = Date.now() - chatStartTime;
      console.log(`💬 聊天请求耗时: ${chatTime}ms`);
      
      if (!chatResponse.ok) {
        const errorText = await chatResponse.text();
        throw new Error(`聊天请求失败: ${chatResponse.status} - ${errorText}`);
      }
      
      const chatData = await chatResponse.json();
      
      return NextResponse.json({
        success: true,
        results: {
          modelsTime: Date.now() - modelsStartTime,
          chatTime,
          totalTime: Date.now() - modelsStartTime,
          response: chatData.choices?.[0]?.message?.content || '无回复'
        }
      });
      
    } catch (error: any) {
      console.error('❌ 聊天请求失败:', error.message);
      return NextResponse.json({
        success: false,
        error: `聊天请求失败: ${error.message}`,
        chatTime: Date.now() - chatStartTime
      });
    }

  } catch (error: any) {
    console.error('❌ 性能测试失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message || '性能测试失败'
    }, { status: 500 });
  }
}
