import { NextRequest, NextResponse } from 'next/server';
import { PlatformIntegration, PlatformConfigForm } from '@/types/webhook';

/**
 * 获取所有平台配置
 */
export async function GET() {
  try {
    // 模拟平台配置数据（实际应该从数据库获取）
    const platforms: PlatformIntegration[] = [
      {
        id: 'wechat-official',
        name: '微信公众号',
        type: 'wechat',
        icon: '💬',
        enabled: false,
        status: 'disconnected',
        config: {
          appId: process.env.WECHAT_APP_ID || '',
          appSecret: process.env.WECHAT_APP_SECRET || '',
          webhookSecret: process.env.WECHAT_TOKEN || '',
          webhookUrl: `${process.env.NEXT_PUBLIC_BASE_URL || process.env.WEBHOOK_BASE_URL || 'https://219.138.230.35'}/api/webhooks/wechat`
        },
        stats: {
          todayMessages: 0,
          totalMessages: 0,
          avgResponseTime: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'dingtalk-bot',
        name: '钉钉机器人',
        type: 'dingtalk',
        icon: '📱',
        enabled: false,
        status: 'disconnected',
        config: {
          appKey: process.env.DINGTALK_APP_KEY || '',
          appSecret: process.env.DINGTALK_APP_SECRET || '',
          agentId: process.env.DINGTALK_AGENT_ID || '',
          webhookUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://219.138.230.35:30002'}/api/webhooks/dingtalk`
        },
        stats: {
          todayMessages: 0,
          totalMessages: 0,
          avgResponseTime: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'douyin-comment',
        name: '抖音评论回复',
        type: 'douyin',
        icon: '🎵',
        enabled: false,
        status: 'disconnected',
        config: {
          appId: process.env.DOUYIN_APP_ID || '',
          appSecret: process.env.DOUYIN_APP_SECRET || '',
          webhookSecret: process.env.DOUYIN_WEBHOOK_SECRET || '',
          webhookUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://219.138.230.35:30002'}/api/webhooks/douyin`
        },
        stats: {
          todayMessages: 0,
          totalMessages: 0,
          avgResponseTime: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'telegram-bot',
        name: 'Telegram机器人',
        type: 'telegram',
        icon: '✈️',
        enabled: false,
        status: 'disconnected',
        config: {
          botToken: process.env.TELEGRAM_BOT_TOKEN || '',
          webhookSecret: process.env.TELEGRAM_WEBHOOK_SECRET || '',
          webhookUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://219.138.230.35:30002'}/api/webhooks/telegram`
        },
        stats: {
          todayMessages: 0,
          totalMessages: 0,
          avgResponseTime: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // 检查配置完整性并更新状态
    platforms.forEach(platform => {
      const hasRequiredConfig = checkPlatformConfig(platform);
      if (hasRequiredConfig) {
        platform.status = 'connected';
        platform.enabled = true;
      }
    });

    return NextResponse.json({
      success: true,
      platforms,
      summary: {
        total: platforms.length,
        enabled: platforms.filter(p => p.enabled).length,
        connected: platforms.filter(p => p.status === 'connected').length
      }
    });

  } catch (error) {
    console.error('[Platforms API] GET error:', error);
    return NextResponse.json({
      success: false,
      message: '获取平台配置失败',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * 更新平台配置
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { platformId, config, enabled } = body;

    console.log('[Platforms API] Updating platform config:', {
      platformId,
      enabled,
      configKeys: Object.keys(config || {})
    });

    // 这里应该保存到数据库
    // 目前只是模拟保存到环境变量（实际应该使用数据库）
    
    // 验证配置
    const validationResult = validatePlatformConfig(platformId, config);
    if (!validationResult.valid) {
      return NextResponse.json({
        success: false,
        message: `配置验证失败: ${validationResult.errors.join(', ')}`
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: `${getPlatformName(platformId)}配置已更新`,
      data: {
        platformId,
        enabled,
        status: enabled && validationResult.valid ? 'connected' : 'disconnected'
      }
    });

  } catch (error) {
    console.error('[Platforms API] POST error:', error);
    return NextResponse.json({
      success: false,
      message: '更新平台配置失败',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * 测试平台连接
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { platformId, config } = body;

    console.log('[Platforms API] Testing platform connection:', platformId);

    // 模拟连接测试
    const testResult = await testPlatformConnection(platformId, config);

    return NextResponse.json({
      success: testResult.success,
      message: testResult.message,
      data: {
        platformId,
        status: testResult.success ? 'connected' : 'error',
        responseTime: testResult.responseTime
      }
    });

  } catch (error) {
    console.error('[Platforms API] PUT error:', error);
    return NextResponse.json({
      success: false,
      message: '连接测试失败',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * 检查平台配置是否完整
 */
function checkPlatformConfig(platform: PlatformIntegration): boolean {
  switch (platform.type) {
    case 'wechat':
      return !!(platform.config.appId && platform.config.appSecret && platform.config.webhookSecret);
    case 'dingtalk':
      return !!(platform.config.appKey && platform.config.appSecret && platform.config.agentId);
    case 'douyin':
      return !!(platform.config.appId && platform.config.appSecret && platform.config.webhookSecret);
    case 'telegram':
      return !!(platform.config.botToken);
    default:
      return false;
  }
}

/**
 * 验证平台配置
 */
function validatePlatformConfig(platformId: string, config: Record<string, unknown>) {
  const errors: string[] = [];

  switch (platformId) {
    case 'wechat-official':
      if (!config.appId) errors.push('App ID不能为空');
      if (!config.appSecret) errors.push('App Secret不能为空');
      if (!config.webhookSecret) errors.push('Token不能为空');
      break;
    case 'dingtalk-bot':
      if (!config.appKey) errors.push('App Key不能为空');
      if (!config.appSecret) errors.push('App Secret不能为空');
      if (!config.agentId) errors.push('Agent ID不能为空');
      break;
    case 'douyin-comment':
      if (!config.appId) errors.push('App ID不能为空');
      if (!config.appSecret) errors.push('App Secret不能为空');
      if (!config.webhookSecret) errors.push('Webhook Secret不能为空');
      break;
    case 'telegram-bot':
      if (!config.botToken) errors.push('Bot Token不能为空');
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 测试平台连接
 */
async function testPlatformConnection(platformId: string, config: Record<string, unknown>) {
  const startTime = Date.now();

  try {
    if (platformId === 'wechat-official') {
      return await testWeChatConnection(config);
    }

    // 其他平台的模拟测试
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const responseTime = Date.now() - startTime;
    const success = Math.random() > 0.2; // 80%成功率

    return {
      success,
      message: success
        ? `${getPlatformName(platformId)}连接测试成功`
        : `${getPlatformName(platformId)}连接测试失败，请检查配置`,
      responseTime
    };
  } catch (error) {
    return {
      success: false,
      message: `连接测试异常: ${error instanceof Error ? error.message : String(error)}`,
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * 测试微信公众号连接
 */
async function testWeChatConnection(config: Record<string, unknown>) {
  const startTime = Date.now();

  try {
    const { appId, appSecret } = config;

    if (!appId || !appSecret) {
      return {
        success: false,
        message: '微信公众号配置不完整，请检查AppID和AppSecret',
        responseTime: Date.now() - startTime
      };
    }

    // 调用微信API获取Access Token来验证配置
    const tokenUrl = 'https://api.weixin.qq.com/cgi-bin/token';
    const params = new URLSearchParams({
      grant_type: 'client_credential',
      appid: appId as string,
      secret: appSecret as string
    });

    const response = await fetch(`${tokenUrl}?${params}`, {
      method: 'GET',
      timeout: 10000 // 10秒超时
    });

    const data = await response.json();
    const responseTime = Date.now() - startTime;

    if (data.access_token) {
      return {
        success: true,
        message: '微信公众号连接测试成功，配置有效',
        responseTime,
        data: {
          expires_in: data.expires_in
        }
      };
    } else {
      // 处理微信API错误
      const errorMessages: Record<string, string> = {
        '40013': 'AppID无效',
        '40001': 'AppSecret无效',
        '40243': 'AppSecret已被冻结，请在微信公众平台解冻',
        '61004': 'IP地址不在白名单中，请在微信公众平台添加服务器IP到白名单',
        '89503': '此IP调用需要管理员确认，请联系公众号管理员'
      };

      const errorMsg = errorMessages[data.errcode] || `微信API错误: ${data.errmsg}`;

      return {
        success: false,
        message: errorMsg,
        responseTime,
        errorCode: data.errcode
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `网络连接失败: ${error instanceof Error ? error.message : String(error)}`,
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * 获取平台名称
 */
function getPlatformName(platformId: string): string {
  const names: Record<string, string> = {
    'wechat-official': '微信公众号',
    'dingtalk-bot': '钉钉机器人',
    'douyin-comment': '抖音评论回复',
    'telegram-bot': 'Telegram机器人'
  };
  return names[platformId] || platformId;
}
