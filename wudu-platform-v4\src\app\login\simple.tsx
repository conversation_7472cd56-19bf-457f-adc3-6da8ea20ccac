'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/lib/auth';

// 简单的钉钉SDK声明
declare global {
  interface Window {
    DTFrameLogin: any;
  }
}

export default function SimpleLoginPage() {
  const [showAlert, setShowAlert] = useState(false);
  const [dingtalkReady, setDingtalkReady] = useState(false);
  const { login, loginDemo } = useAuth();
  const router = useRouter();

  // 简单的钉钉SDK加载 - 遵循简化原则
  useEffect(() => {
    const loadDingtalkSDK = () => {
      // 检查是否已经加载
      if (window.DTFrameLogin) {
        setDingtalkReady(true);
        // 延迟初始化，确保DOM元素已渲染
        setTimeout(() => {
          initDingtalk();
        }, 100);
        return;
      }

      // 动态加载SDK
      const script = document.createElement('script');
      script.src = 'https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js';
      script.onload = () => {
        console.log('✅ 钉钉SDK加载成功');
        setDingtalkReady(true);
        // 延迟初始化，确保DOM元素已渲染
        setTimeout(() => {
          initDingtalk();
        }, 100);
      };
      script.onerror = () => {
        console.error('❌ 钉钉SDK加载失败');
      };

      document.head.appendChild(script);
    };

    loadDingtalkSDK();
  }, []);

  // 简化的钉钉初始化 - 单一职责
  const initDingtalk = () => {
    if (!window.DTFrameLogin) return;

    try {
      window.DTFrameLogin(
        {
          id: 'dingtalk_container',
          width: 300,
          height: 300,
        },
        {
          redirect_uri: 'http://**************:30002/dingtalk-callback',
          client_id: 'dinggai5cng27n76jvbq',
          scope: 'openid',
          response_type: 'code',
          state: 'dingtalk_login_' + Date.now(),
          prompt: 'consent',
        },
        async (loginResult: any) => {
          console.log('✅ 钉钉登录成功:', loginResult);

          if (loginResult?.authCode) {
            const success = await login(loginResult.authCode, loginResult.state);
            if (success) {
              setShowAlert(true);
              setTimeout(() => router.push('/dashboard'), 1500);
            }
          }
        },
        (error: string) => {
          console.error('❌ 钉钉登录失败:', error);
        }
      );
    } catch (error) {
      console.error('❌ 钉钉初始化失败:', error);
    }
  };

  const handleDemoLogin = () => {
    setShowAlert(true);
    loginDemo();
    setTimeout(() => {
      router.push('/dashboard');
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            吴都乔街景区
          </CardTitle>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">智能客服中控平台</h2>
          <p className="text-gray-500">使用钉钉账号安全登录管理系统</p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 钉钉登录区域 */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">钉钉扫码登录</h3>
            
            {/* 钉钉二维码容器 - 简化版本 */}
            <div className="flex justify-center mb-4">
              {dingtalkReady ? (
                // 钉钉SDK就绪，显示二维码容器
                <div
                  id="dingtalk_container"
                  className="w-[300px] h-[300px] border-2 border-dashed border-gray-200 rounded-xl bg-gray-50 flex items-center justify-center"
                >
                  {/* 钉钉SDK会在这里渲染二维码 */}
                </div>
              ) : (
                // 加载中状态
                <div className="w-[300px] h-[300px] border-2 border-dashed border-gray-200 rounded-xl bg-gray-50 flex flex-col items-center justify-center text-center p-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mb-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">正在加载钉钉登录</h4>
                  <p className="text-sm text-gray-600">请稍候...</p>
                </div>
              )}
            </div>

            {/* 状态提示 */}
            <div className="text-center">
              {dingtalkReady ? (
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                  请使用钉钉APP扫描二维码
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                  正在加载钉钉登录组件...
                </div>
              )}
            </div>
          </div>

          {/* 分割线 */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">或</span>
            </div>
          </div>

          {/* 开发测试区域 */}
          <div>
            <Button 
              onClick={handleDemoLogin}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium py-3"
            >
              演示登录 (开发测试)
            </Button>
          </div>

          {/* 安全提示 */}
          <div className="text-center">
            <p className="text-xs text-gray-400">
              🔒 安全登录 · 数据加密传输 · 符合企业安全标准
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 全局提示 */}
      {showAlert && (
        <div className="fixed top-6 right-6 bg-white border border-green-200 text-green-700 px-6 py-4 rounded-xl shadow-lg backdrop-blur-sm">
          <div className="flex items-center">
            <span className="mr-3 text-lg">✅</span>
            <span className="font-medium">登录成功！正在跳转...</span>
          </div>
        </div>
      )}
    </div>
  );
}
