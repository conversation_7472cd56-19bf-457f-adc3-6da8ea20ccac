import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/knowledge/search - 搜索知识库
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query, 
      categoryId, 
      tagIds, 
      status = 'PUBLISHED',
      limit = 10,
      includeContent = false 
    } = body;

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '搜索关键词不能为空' },
        { status: 400 }
      );
    }

    const searchQuery = query.trim();

    // 构建搜索条件
    const where: any = {
      status,
      OR: [
        { title: { contains: searchQuery } },
        { content: { contains: searchQuery } },
        { summary: { contains: searchQuery } }
      ]
    };

    // 添加分类过滤
    if (categoryId) {
      where.categoryId = categoryId;
    }

    // 添加标签过滤
    if (tagIds && tagIds.length > 0) {
      where.tags = {
        some: {
          tagId: { in: tagIds }
        }
      };
    }

    // 执行搜索
    const searchResults = await prisma.knowledgeBase.findMany({
      where,
      take: limit,
      orderBy: [
        { priority: 'desc' },
        { useCount: 'desc' },
        { updatedAt: 'desc' }
      ],
      select: {
        id: true,
        title: true,
        summary: true,
        content: includeContent,
        priority: true,
        viewCount: true,
        useCount: true,
        createdAt: true,
        updatedAt: true,
        category: {
          select: {
            id: true,
            name: true,
            color: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          }
        }
      }
    });

    // 计算相关性得分（简单的关键词匹配）
    const resultsWithScore = searchResults.map(item => {
      let score = 0;
      const lowerQuery = searchQuery.toLowerCase();
      const lowerTitle = item.title.toLowerCase();
      const lowerSummary = (item.summary || '').toLowerCase();
      
      // 标题匹配得分更高
      if (lowerTitle.includes(lowerQuery)) {
        score += 10;
        if (lowerTitle.startsWith(lowerQuery)) {
          score += 5; // 开头匹配额外加分
        }
      }
      
      // 摘要匹配
      if (lowerSummary.includes(lowerQuery)) {
        score += 5;
      }
      
      // 优先级加分
      score += item.priority;
      
      // 使用频率加分
      score += Math.min(item.useCount * 0.1, 5);

      return {
        ...item,
        relevanceScore: score
      };
    });

    // 按相关性排序
    resultsWithScore.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // 更新搜索统计（异步执行，不影响响应速度）
    if (searchResults.length > 0) {
      // 增加匹配条目的使用次数
      const itemIds = searchResults.map(item => item.id);
      prisma.knowledgeBase.updateMany({
        where: { id: { in: itemIds } },
        data: { useCount: { increment: 1 } }
      }).catch(error => {
        console.warn('⚠️ 更新知识库使用统计失败:', error);
      });
    }

    console.log(`🔍 知识库搜索: "${searchQuery}" - 找到 ${searchResults.length} 条结果`);

    return NextResponse.json({
      success: true,
      data: {
        query: searchQuery,
        results: resultsWithScore,
        total: resultsWithScore.length,
        searchTime: Date.now()
      }
    });

  } catch (error) {
    console.error('❌ 知识库搜索失败:', error);
    return NextResponse.json(
      { success: false, error: '知识库搜索失败' },
      { status: 500 }
    );
  }
}

// GET /api/knowledge/search - 获取搜索建议
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '5');

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: true,
        data: {
          suggestions: []
        }
      });
    }

    const searchQuery = query.trim();

    // 获取标题匹配的建议
    const suggestions = await prisma.knowledgeBase.findMany({
      where: {
        status: 'PUBLISHED',
        title: {
          contains: searchQuery
        }
      },
      take: limit,
      orderBy: [
        { useCount: 'desc' },
        { viewCount: 'desc' }
      ],
      select: {
        id: true,
        title: true,
        summary: true
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        query: searchQuery,
        suggestions
      }
    });

  } catch (error) {
    console.error('❌ 获取搜索建议失败:', error);
    return NextResponse.json(
      { success: false, error: '获取搜索建议失败' },
      { status: 500 }
    );
  }
}
