/**
 * HTTP客户端管理器 - 实现连接池和请求优化
 */

import { Agent } from 'https';

class HttpClientManager {
  private static instance: HttpClientManager;
  private agents: Map<string, Agent> = new Map();

  private constructor() {}

  static getInstance(): HttpClientManager {
    if (!HttpClientManager.instance) {
      HttpClientManager.instance = new HttpClientManager();
    }
    return HttpClientManager.instance;
  }

  /**
   * 获取或创建HTTP Agent（连接池）
   */
  private getAgent(baseURL: string): Agent {
    if (!this.agents.has(baseURL)) {
      const agent = new Agent({
        keepAlive: true,
        keepAliveMsecs: 30000, // 30秒
        maxSockets: 10, // 每个主机最大连接数
        maxFreeSockets: 5, // 每个主机最大空闲连接数
        timeout: 20000, // 20秒超时
      });
      this.agents.set(baseURL, agent);
      console.log(`🔗 为 ${baseURL} 创建新的连接池`);
    }
    return this.agents.get(baseURL)!;
  }

  /**
   * 优化的fetch请求，使用连接池
   */
  async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    const urlObj = new URL(url);
    const baseURL = `${urlObj.protocol}//${urlObj.host}`;
    
    // 注意：在Node.js环境中，我们可以使用Agent
    // 但在浏览器环境中，fetch会自动管理连接
    if (typeof window === 'undefined') {
      // Node.js环境
      const agent = this.getAgent(baseURL);
      
      // 使用node-fetch或类似库时的配置
      const enhancedOptions = {
        ...options,
        // agent: agent, // 在实际实现中需要适配具体的HTTP库
      };
      
      return fetch(url, enhancedOptions);
    } else {
      // 浏览器环境，直接使用fetch
      return fetch(url, options);
    }
  }

  /**
   * 流式请求的优化版本
   */
  async fetchStream(url: string, options: RequestInit = {}): Promise<Response> {
    const startTime = Date.now();
    
    try {
      const response = await this.fetch(url, {
        ...options,
        // 确保支持流式响应
        headers: {
          ...options.headers,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
      });

      const fetchTime = Date.now() - startTime;
      console.log(`🌐 HTTP请求完成，耗时: ${fetchTime}ms`);
      
      return response;
    } catch (error) {
      const fetchTime = Date.now() - startTime;
      console.error(`❌ HTTP请求失败，耗时: ${fetchTime}ms，错误:`, error);
      throw error;
    }
  }

  /**
   * 清理连接池
   */
  cleanup(): void {
    for (const [baseURL, agent] of this.agents) {
      agent.destroy();
      console.log(`🧹 清理 ${baseURL} 的连接池`);
    }
    this.agents.clear();
  }
}

// 导出单例实例
export const httpClient = HttpClientManager.getInstance();

// 进程退出时清理连接池
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    httpClient.cleanup();
  });
  
  process.on('SIGINT', () => {
    httpClient.cleanup();
    process.exit(0);
  });
}
