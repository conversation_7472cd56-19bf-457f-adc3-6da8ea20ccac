import { NextRequest, NextResponse } from 'next/server';
import { ragService } from '@/lib/ai/rag-service';
import { type ConversationContext } from '@/lib/ai/ai-service-manager';

// POST /api/ai/rag - 测试RAG检索增强生成功能
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query, 
      sessionId = 'test-session', 
      userId = 'test-user',
      intent,
      sentiment,
      config = {}
    } = body;

    // 验证必需参数
    if (!query) {
      return NextResponse.json(
        { success: false, error: '缺少必需参数: query' },
        { status: 400 }
      );
    }

    console.log('🔍 收到RAG测试请求:', {
      query,
      sessionId,
      userId,
      intent,
      sentiment,
      config
    });

    // 构建对话上下文
    const context: ConversationContext = {
      sessionId,
      userId,
      messages: [
        { role: 'user', content: query }
      ],
      intent,
      sentiment: sentiment as 'positive' | 'neutral' | 'negative',
      metadata: {
        testMode: true,
        timestamp: new Date().toISOString()
      }
    };

    // 调用RAG服务
    const startTime = Date.now();
    const ragResponse = await ragService.generateResponse(query, context, config);
    const totalTime = Date.now() - startTime;

    console.log(`✅ RAG测试完成，总耗时: ${totalTime}ms`);

    return NextResponse.json({
      success: true,
      data: {
        ...ragResponse,
        testInfo: {
          totalProcessingTime: totalTime,
          timestamp: new Date().toISOString(),
          context: {
            sessionId,
            userId,
            intent,
            sentiment
          }
        }
      }
    });

  } catch (error) {
    console.error('❌ RAG测试失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'RAG处理失败',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET /api/ai/rag - 获取RAG服务状态和配置信息
export async function GET() {
  try {
    const status = {
      service: 'RAG检索增强生成服务',
      version: '1.0.0',
      status: 'active',
      features: {
        vectorSearch: '向量搜索',
        hybridSearch: '混合搜索',
        knowledgeRetrieval: '知识库检索',
        contextAware: '上下文感知',
        confidenceScoring: '置信度评分',
        sourceReferences: '来源引用'
      },
      defaultConfig: {
        maxKnowledgeItems: 5,
        similarityThreshold: 0.6,
        knowledgeWeightFactor: 1.2,
        enableHybridSearch: true,
        includeSourceReferences: true,
        contextWindowSize: 10
      },
      supportedIntents: [
        'ticket_inquiry',
        'opening_hours', 
        'transportation',
        'route_planning',
        'facilities',
        'dining',
        'accommodation',
        'weather',
        'emergency',
        'complaint'
      ],
      testQueries: [
        '门票价格是多少？',
        '景区开放时间',
        '怎么去景区？',
        '有什么好玩的景点？',
        '景区内有餐厅吗？'
      ],
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('❌ 获取RAG状态失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '获取服务状态失败',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
