# 🔄 吴都乔街智能客服平台项目恢复指南

## 📋 恢复指南概述

本指南提供完整的项目恢复步骤，确保在任何情况下都能快速恢复到v1.1稳定版本。

---

## 🎯 恢复场景

### 场景1: 全新环境部署
- 新服务器或开发机器
- 从零开始搭建项目
- 预计时间: 45分钟

### 场景2: 代码回滚
- 开发过程中出现问题
- 需要回滚到稳定版本
- 预计时间: 15分钟

### 场景3: 数据库恢复
- 数据库结构损坏
- 需要重建数据模型
- 预计时间: 20分钟

---

## 🛠️ 环境准备

### 系统要求
```yaml
操作系统: Windows 10/11, macOS, Linux
Node.js: 18.0.0 或更高版本
MySQL: 8.0 或更高版本
内存: 最少4GB，推荐8GB
磁盘空间: 最少2GB可用空间
网络: 稳定的互联网连接
```

### 必需软件安装
```bash
# 1. 安装Node.js (https://nodejs.org/)
node --version  # 验证安装

# 2. 安装MySQL (https://dev.mysql.com/downloads/)
mysql --version  # 验证安装

# 3. 安装Git (https://git-scm.com/)
git --version  # 验证安装

# 4. 安装代码编辑器 (推荐VS Code)
```

---

## 🚀 快速恢复步骤

### 步骤1: 创建项目目录
```bash
# 创建项目根目录
mkdir wudu-platform-v4
cd wudu-platform-v4

# 初始化Node.js项目
npm init -y
```

### 步骤2: 安装依赖包
```bash
# 安装生产依赖
npm install next@15.1.3 react@19.0.0 react-dom@19.0.0
npm install @prisma/client@5.22.0
npm install @radix-ui/react-avatar@1.1.1
npm install @radix-ui/react-dropdown-menu@2.1.2
npm install @radix-ui/react-icons@1.3.2
npm install @radix-ui/react-slot@1.1.0
npm install class-variance-authority@0.7.1
npm install clsx@2.1.1
npm install lucide-react@0.460.0
npm install tailwind-merge@2.5.4
npm install tailwindcss-animate@1.0.7
npm install zustand@5.0.2

# 安装开发依赖
npm install -D @types/node@22.10.2
npm install -D @types/react@19.0.1
npm install -D @types/react-dom@19.0.1
npm install -D eslint@9.17.0
npm install -D eslint-config-next@15.1.3
npm install -D postcss@8.5.1
npm install -D prisma@5.22.0
npm install -D tailwindcss@3.4.17
npm install -D typescript@5.7.2
```

### 步骤3: 创建配置文件
```bash
# 从备份文档复制以下文件内容:
# - package.json
# - next.config.ts
# - tailwind.config.js
# - tsconfig.json
# - components.json
# - prisma/schema.prisma
```

### 步骤4: 创建项目结构
```bash
# 创建目录结构
mkdir -p src/app/api/auth/dingtalk/official-login
mkdir -p src/app/api/auth/demo-login
mkdir -p src/app/dashboard
mkdir -p src/app/login
mkdir -p src/components/ui
mkdir -p src/components/layout
mkdir -p src/lib
mkdir -p src/types
mkdir -p prisma
mkdir -p docs
mkdir -p versions
mkdir -p public
```

### 步骤5: 恢复核心代码文件
```bash
# 从备份文档复制以下关键文件:
# - src/lib/auth.ts (认证状态管理)
# - src/lib/db.ts (数据库操作)
# - src/lib/utils.ts (工具函数)
# - src/app/api/auth/dingtalk/official-login/route.ts
# - src/app/api/auth/demo-login/route.ts
# - src/app/dashboard/page.tsx
# - src/app/login/page.tsx
# - src/app/layout.tsx
# - src/app/globals.css
```

### 步骤6: 配置环境变量
```bash
# 创建环境变量文件
cp .env.example .env.local

# 编辑 .env.local 文件，填入以下配置:
DATABASE_URL="mysql://username:password@localhost:3306/wudu_platform"
DINGTALK_APP_KEY="your_app_key"
DINGTALK_APP_SECRET="your_app_secret"
DINGTALK_CORP_ID="your_corp_id"
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"
```

### 步骤7: 初始化数据库
```bash
# 生成Prisma客户端
npx prisma generate

# 推送数据库模式
npx prisma db push

# 验证数据库连接
npx prisma studio
```

### 步骤8: 启动项目
```bash
# 开发模式启动
npm run dev

# 访问应用
# http://localhost:3000
```

---

## 🔧 故障排除

### 常见问题1: 依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 常见问题2: 数据库连接失败
```bash
# 检查MySQL服务状态
# Windows: services.msc 查看MySQL服务
# macOS/Linux: sudo systemctl status mysql

# 检查数据库连接字符串
# 确保用户名、密码、端口正确
```

### 常见问题3: 端口冲突
```bash
# 查看端口占用
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # macOS/Linux

# 使用其他端口启动
npm run dev -- -p 3001
```

### 常见问题4: TypeScript编译错误
```bash
# 重新生成类型定义
npx prisma generate

# 清理TypeScript缓存
rm -rf .next
npm run dev
```

---

## 📊 验证清单

### 功能验证 ✅
- [ ] 项目可以正常启动 (npm run dev)
- [ ] 登录页面可以访问 (/login)
- [ ] 钉钉登录功能正常
- [ ] 演示登录功能正常
- [ ] 主控制台页面正常 (/dashboard)
- [ ] 用户信息正确显示
- [ ] 数据库连接正常

### 技术验证 ✅
- [ ] TypeScript编译无错误
- [ ] ESLint检查通过
- [ ] Prisma客户端生成成功
- [ ] 数据库模式同步成功
- [ ] 所有依赖包安装成功

### 数据验证 ✅
- [ ] 用户表结构正确
- [ ] 部门表结构正确
- [ ] 角色表结构正确
- [ ] 关联关系正确
- [ ] 索引创建成功

---

## 🔄 Git仓库恢复

### 从备份恢复Git仓库
```bash
# 初始化Git仓库
git init

# 添加所有文件
git add .

# 创建初始提交
git commit -m "feat: 恢复项目到v1.1稳定版本"

# 创建标签
git tag -a v1.1-restored -m "v1.1 恢复版本"

# 如果有远程仓库
git remote add origin <repository-url>
git push -u origin main
git push origin --tags
```

### 使用Git仓库恢复
```bash
# 如果已有Git仓库
git clone <repository-url>
cd wudu-platform-v4

# 切换到稳定版本
git checkout v1.1

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local

# 初始化数据库
npx prisma generate
npx prisma db push

# 启动项目
npm run dev
```

---

## 📞 技术支持

### 恢复失败时的联系方式
- **技术文档**: 查看 `docs/` 目录下的详细文档
- **版本备份**: 查看 `versions/` 目录下的备份文件
- **代码备份**: 参考 `versions/v1.1-关键代码备份.md`

### 自助排查步骤
1. 检查Node.js和MySQL版本
2. 验证环境变量配置
3. 查看控制台错误信息
4. 检查网络连接状态
5. 参考故障排除章节

---

## 📈 恢复后的下一步

### 立即任务
1. 验证所有功能正常
2. 备份当前稳定状态
3. 更新项目文档
4. 通知团队成员

### 开发准备
1. 创建开发分支
2. 设置开发环境
3. 准备AI服务API密钥
4. 开始v2.0功能开发

---

*项目恢复指南版本: v1.1*  
*创建时间: 2025-01-28*  
*适用版本: v1.1及以上*  
*预计恢复时间: 15-45分钟*
