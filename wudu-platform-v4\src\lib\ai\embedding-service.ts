/**
 * 文本向量化服务
 * 支持DeepSeek和其他embedding模型
 * 提供缓存、批量处理和错误处理功能
 */

import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import { decrypt } from '../encryption';

const prisma = new PrismaClient();

// 向量化配置
interface EmbeddingConfig {
  provider: 'deepseek' | 'openai';
  model: string;
  apiKey: string;
  baseURL: string;
  maxTokens: number;
  dimensions: number;
}

// 向量化结果
interface EmbeddingResult {
  vector: number[];
  dimensions: number;
  model: string;
  tokenCount?: number;
  processingTime: number;
  quality?: 'high' | 'medium' | 'low';
  qualityScore?: number;
}

// 批量向量化结果
interface BatchEmbeddingResult {
  results: Array<{
    text: string;
    vector: number[];
    error?: string;
  }>;
  totalProcessingTime: number;
  successCount: number;
  errorCount: number;
}

export class EmbeddingService {
  private config: EmbeddingConfig;
  private cache = new Map<string, EmbeddingResult>();
  private cacheTimeout = 24 * 60 * 60 * 1000; // 24小时缓存

  constructor() {
    // 默认使用DeepSeek配置
    this.config = {
      provider: 'deepseek',
      model: 'deepseek-embedding',
      apiKey: process.env.DEEPSEEK_API_KEY || '',
      baseURL: 'https://api.deepseek.com',
      maxTokens: 8192,
      dimensions: 1536
    };
  }

  /**
   * 初始化服务，从数据库加载配置
   */
  async initialize(): Promise<void> {
    try {
      const configs = await prisma.aiConfig.findMany({
        where: {
          configKey: {
            in: ['deepseek_api_key', 'openai_api_key']
          }
        }
      });

      const configMap = configs.reduce((acc, config) => {
        const value = config.isEncrypted ? decrypt(config.configValue || '') : config.configValue;
        acc[config.configKey] = value;
        return acc;
      }, {} as Record<string, string>);

      // 优先使用DeepSeek
      if (configMap.deepseek_api_key) {
        this.config.apiKey = configMap.deepseek_api_key;
        this.config.provider = 'deepseek';
        this.config.model = 'deepseek-embedding';
      } else if (configMap.openai_api_key) {
        this.config.apiKey = configMap.openai_api_key;
        this.config.provider = 'openai';
        this.config.baseURL = 'https://api.openai.com';
        this.config.model = 'text-embedding-ada-002';
      }

      console.log(`🔧 Embedding服务初始化完成，使用: ${this.config.provider}`);
    } catch (error) {
      console.error('❌ Embedding服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成单个文本的向量
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    const startTime = Date.now();
    
    // 检查缓存
    const cacheKey = this.getCacheKey(text);
    const cached = this.cache.get(cacheKey);
    if (cached) {
      console.log('📦 使用缓存的向量结果');
      return cached;
    }

    try {
      // 文本预处理
      const processedText = this.preprocessText(text);
      
      // 调用API生成向量
      const vector = await this.callEmbeddingAPI(processedText);

      // 评估向量质量
      const qualityAssessment = this.evaluateVectorQuality(vector, processedText);

      const result: EmbeddingResult = {
        vector,
        dimensions: vector.length,
        model: this.config.model,
        processingTime: Date.now() - startTime,
        quality: qualityAssessment.quality,
        qualityScore: qualityAssessment.score
      };

      // 记录质量问题
      if (qualityAssessment.issues.length > 0) {
        console.warn(`⚠️ 向量质量问题: ${qualityAssessment.issues.join(', ')}`);
      }

      // 只缓存高质量的向量
      if (qualityAssessment.quality !== 'low') {
        this.cache.set(cacheKey, result);
      }

      console.log(`✅ 向量生成成功，维度: ${result.dimensions}，质量: ${qualityAssessment.quality}，耗时: ${result.processingTime}ms`);
      return result;

    } catch (error) {
      console.error('❌ 向量生成失败:', error);
      throw error;
    }
  }

  /**
   * 批量生成向量
   */
  async generateBatchEmbeddings(texts: string[]): Promise<BatchEmbeddingResult> {
    const startTime = Date.now();
    const results: BatchEmbeddingResult['results'] = [];
    let successCount = 0;
    let errorCount = 0;

    console.log(`🔄 开始批量向量化，文本数量: ${texts.length}`);

    // 分批处理，避免API限制
    const batchSize = 10;
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      
      await Promise.allSettled(
        batch.map(async (text) => {
          try {
            const result = await this.generateEmbedding(text);
            results.push({
              text,
              vector: result.vector
            });
            successCount++;
          } catch (error) {
            results.push({
              text,
              vector: [],
              error: error instanceof Error ? error.message : '未知错误'
            });
            errorCount++;
          }
        })
      );

      // 批次间延迟，避免API限制
      if (i + batchSize < texts.length) {
        await this.delay(100);
      }
    }

    const totalTime = Date.now() - startTime;
    console.log(`✅ 批量向量化完成，成功: ${successCount}，失败: ${errorCount}，总耗时: ${totalTime}ms`);

    return {
      results,
      totalProcessingTime: totalTime,
      successCount,
      errorCount
    };
  }

  /**
   * 调用embedding API（优化版）
   */
  private async callEmbeddingAPI(text: string): Promise<number[]> {
    // 获取可用的嵌入服务提供商
    const providers = this.getAvailableProviders();

    if (providers.length === 0) {
      console.log('⚠️ 没有可用的嵌入服务，使用模拟向量');
      return this.generateMockEmbedding(text);
    }

    // 尝试每个提供商
    for (const provider of providers) {
      try {
        console.log(`🔄 尝试使用 ${provider.name} 生成向量...`);
        const vector = await this.callProviderAPI(provider, text);
        console.log(`✅ ${provider.name} 向量生成成功，维度: ${vector.length}`);
        return vector;
      } catch (error) {
        console.warn(`❌ ${provider.name} 调用失败: ${error.message}`);
        continue;
      }
    }

    // 所有服务都失败，使用模拟向量
    console.warn('⚠️ 所有嵌入服务都失败，使用模拟向量');
    return this.generateMockEmbedding(text);
  }

  /**
   * 获取可用的嵌入服务提供商
   */
  private getAvailableProviders() {
    const providers = [];

    // 优先使用OpenAI（更稳定可靠）
    if (process.env.OPENAI_API_KEY) {
      providers.push({
        name: 'OpenAI-ENV',
        baseURL: 'https://api.openai.com',
        apiKey: process.env.OPENAI_API_KEY,
        model: 'text-embedding-3-small', // 最新的嵌入模型
        provider: 'openai'
      });
    }

    // 使用配置的OpenAI服务
    if (this.config.apiKey && this.config.provider === 'openai') {
      providers.push({
        name: 'OpenAI-Config',
        baseURL: this.config.baseURL,
        apiKey: this.config.apiKey,
        model: this.config.model,
        provider: 'openai'
      });
    }

    // 尝试DeepSeek（如果有API密钥）
    if (this.config.apiKey && this.config.provider === 'deepseek') {
      // DeepSeek目前可能不支持嵌入API，但我们可以尝试
      providers.push({
        name: 'DeepSeek-Experimental',
        baseURL: this.config.baseURL,
        apiKey: this.config.apiKey,
        model: 'text-embedding-ada-002', // 使用兼容格式
        provider: 'deepseek'
      });
    }

    return providers;
  }

  /**
   * 调用特定提供商的API
   */
  private async callProviderAPI(provider: any, text: string): Promise<number[]> {
    const url = `${provider.baseURL}/v1/embeddings`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: provider.model,
        input: text,
        encoding_format: 'float'
      }),
      signal: AbortSignal.timeout(30000) // 30秒超时
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    if (!data.data || !data.data[0] || !data.data[0].embedding) {
      throw new Error('API返回格式错误：缺少embedding数据');
    }

    return data.data[0].embedding;
  }

  /**
   * 生成模拟向量（用于测试）
   */
  private generateMockEmbedding(text: string): number[] {
    // 基于文本内容生成确定性的模拟向量
    const hash = this.simpleHash(text);
    const vector: number[] = [];

    // 生成1536维的向量
    for (let i = 0; i < 1536; i++) {
      // 使用文本哈希和索引生成伪随机数
      const seed = hash + i;
      vector.push((Math.sin(seed) + Math.cos(seed * 0.7) + Math.sin(seed * 0.3)) / 3);
    }

    // 归一化向量
    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return vector.map(val => val / norm);
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 文本预处理（优化中文处理）
   */
  private preprocessText(text: string): string {
    // 1. 基础清理
    let processed = text.trim();

    // 2. 统一空白字符
    processed = processed
      .replace(/\s+/g, ' ')  // 合并多个空格
      .replace(/\n+/g, ' ')  // 替换换行符
      .replace(/\t+/g, ' '); // 替换制表符

    // 3. 清理特殊字符但保留中文内容
    processed = processed.replace(/[^\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef\w\s.,!?;:()[\]{}""'']/g, '');

    // 4. 标准化中文标点符号
    processed = processed
      .replace(/，/g, ', ')
      .replace(/。/g, '. ')
      .replace(/！/g, '! ')
      .replace(/？/g, '? ')
      .replace(/；/g, '; ')
      .replace(/：/g, ': ');

    // 5. 移除多余的标点和空格
    processed = processed
      .replace(/[.,!?;:]+/g, (match) => match[0]) // 移除重复标点
      .replace(/\s+/g, ' ') // 再次清理空格
      .trim();

    // 6. 智能长度限制（在句子边界截断）
    const maxLength = this.config.maxTokens * 4;
    if (processed.length > maxLength) {
      const truncated = processed.substring(0, maxLength);
      const sentenceEnders = ['. ', '! ', '? ', '。', '！', '？'];
      let lastSentenceEnd = -1;

      for (const ender of sentenceEnders) {
        const pos = truncated.lastIndexOf(ender);
        if (pos > lastSentenceEnd) {
          lastSentenceEnd = pos;
        }
      }

      // 如果找到合适的句子结束位置且不会截断太多内容
      if (lastSentenceEnd > maxLength * 0.7) {
        processed = truncated.substring(0, lastSentenceEnd + 1).trim();
      } else {
        processed = truncated.trim();
      }
    }

    return processed;
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(text: string): string {
    return crypto
      .createHash('md5')
      .update(`${this.config.model}:${text}`)
      .digest('hex');
  }

  /**
   * 评估向量质量
   */
  private evaluateVectorQuality(vector: number[], text: string): {
    quality: 'high' | 'medium' | 'low';
    score: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 1.0;

    // 1. 检查向量维度
    if (vector.length !== 1536 && vector.length !== 1024 && vector.length !== 512) {
      issues.push(`异常的向量维度: ${vector.length}`);
      score -= 0.3;
    }

    // 2. 检查向量值范围
    const minVal = Math.min(...vector);
    const maxVal = Math.max(...vector);
    if (minVal < -2 || maxVal > 2) {
      issues.push(`向量值超出正常范围: [${minVal.toFixed(3)}, ${maxVal.toFixed(3)}]`);
      score -= 0.2;
    }

    // 3. 检查向量分布
    const mean = vector.reduce((sum, val) => sum + val, 0) / vector.length;
    const variance = vector.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / vector.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev < 0.1) {
      issues.push(`向量方差过小: ${stdDev.toFixed(4)}`);
      score -= 0.2;
    }

    // 4. 检查零值比例
    const zeroCount = vector.filter(val => Math.abs(val) < 1e-6).length;
    const zeroRatio = zeroCount / vector.length;
    if (zeroRatio > 0.5) {
      issues.push(`零值比例过高: ${(zeroRatio * 100).toFixed(1)}%`);
      score -= 0.3;
    }

    // 5. 检查文本质量对向量的影响
    if (text.length < 10) {
      issues.push('输入文本过短');
      score -= 0.1;
    }

    // 确定质量等级
    let quality: 'high' | 'medium' | 'low';
    if (score >= 0.8) {
      quality = 'high';
    } else if (score >= 0.5) {
      quality = 'medium';
    } else {
      quality = 'low';
    }

    return { quality, score, issues };
  }

  /**
   * 计算向量相似度（用于测试）
   */
  public static calculateSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      throw new Error('向量维度不匹配');
    }

    // 计算余弦相似度
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  // 解密函数已移至 ../encryption.ts 文件中

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 向量缓存已清理');
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      provider: this.config.provider,
      model: this.config.model,
      cacheSize: this.cache.size,
      isConfigured: !!this.config.apiKey
    };
  }
}

// 单例实例
export const embeddingService = new EmbeddingService();
