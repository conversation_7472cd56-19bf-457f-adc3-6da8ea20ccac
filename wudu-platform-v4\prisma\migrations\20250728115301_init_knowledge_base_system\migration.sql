-- CreateTable
CREATE TABLE `users` (
    `id` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `unionId` VARCHAR(191) NOT NULL,
    `openId` VARCHAR(191) NULL,
    `userId` VARCHAR(191) NULL,
    `name` VARCHAR(191) NOT NULL,
    `nick` VARCHAR(191) NULL,
    `avatar` VARCHAR(191) NULL,
    `mobile` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `stateCode` VARCHAR(191) NULL DEFAULT '+86',
    `jobNumber` VARCHAR(191) NULL,
    `title` VARCHAR(191) NULL,
    `workPlace` VARCHAR(191) NULL,
    `hiredDate` VARCHAR(191) NULL,
    `remark` VARCHAR(191) NULL,
    `active` BOOLEAN NOT NULL DEFAULT true,
    `admin` BOOLEAN NOT NULL DEFAULT false,
    `boss` BOOLEAN NOT NULL DEFAULT false,
    `senior` BOOLEAN NOT NULL DEFAULT false,
    `realAuthed` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `users_unionId_key`(`unionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `departments` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `deptId` INTEGER NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `parentId` INTEGER NULL,
    `order` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `departments_deptId_key`(`deptId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `roles` (
    `id` VARCHAR(191) NOT NULL,
    `roleId` VARCHAR(191) NOT NULL,
    `roleName` VARCHAR(191) NOT NULL,
    `groupName` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `roles_roleId_key`(`roleId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_departments` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `deptId` INTEGER NOT NULL,

    UNIQUE INDEX `user_departments_userId_deptId_key`(`userId`, `deptId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_roles` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `roleId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `user_roles_userId_roleId_key`(`userId`, `roleId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_sessions` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `token` VARCHAR(191) NOT NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `user_sessions_token_key`(`token`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_conversations` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NULL,
    `session_id` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `status` ENUM('active', 'closed', 'archived') NOT NULL DEFAULT 'active',
    `intent` VARCHAR(191) NULL,
    `sentiment` VARCHAR(191) NULL,
    `satisfaction_score` DECIMAL(3, 2) NULL,
    `total_messages` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `ai_conversations_user_id_session_id_idx`(`user_id`, `session_id`),
    INDEX `ai_conversations_status_created_at_idx`(`status`, `created_at`),
    INDEX `ai_conversations_intent_idx`(`intent`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_messages` (
    `id` VARCHAR(191) NOT NULL,
    `conversation_id` VARCHAR(191) NOT NULL,
    `role` ENUM('user', 'assistant', 'system') NOT NULL,
    `content` TEXT NOT NULL,
    `intent` VARCHAR(191) NULL,
    `sentiment` VARCHAR(191) NULL,
    `confidence_score` DECIMAL(3, 2) NULL,
    `response_time_ms` INTEGER NULL,
    `knowledge_used` JSON NULL,
    `metadata` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `ai_messages_conversation_id_created_at_idx`(`conversation_id`, `created_at`),
    INDEX `ai_messages_role_created_at_idx`(`role`, `created_at`),
    INDEX `ai_messages_intent_idx`(`intent`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `knowledge_base` (
    `id` VARCHAR(191) NOT NULL,
    `title` VARCHAR(500) NOT NULL,
    `content` LONGTEXT NOT NULL,
    `summary` TEXT NULL,
    `category_id` VARCHAR(191) NULL,
    `tags_json` JSON NULL,
    `embedding` JSON NULL,
    `status` ENUM('active', 'draft', 'archived') NOT NULL DEFAULT 'draft',
    `priority` INTEGER NOT NULL DEFAULT 0,
    `view_count` INTEGER NOT NULL DEFAULT 0,
    `use_count` INTEGER NOT NULL DEFAULT 0,
    `created_by` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `knowledge_base_category_id_status_idx`(`category_id`, `status`),
    INDEX `knowledge_base_status_priority_idx`(`status`, `priority` DESC),
    INDEX `knowledge_base_created_by_idx`(`created_by`),
    INDEX `knowledge_base_use_count_idx`(`use_count` DESC),
    FULLTEXT INDEX `knowledge_base_title_content_summary_idx`(`title`, `content`, `summary`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `kb_categories` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `parent_id` VARCHAR(191) NULL,
    `sort_order` INTEGER NOT NULL DEFAULT 0,
    `icon` VARCHAR(100) NULL,
    `color` VARCHAR(20) NULL,
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `kb_categories_parent_id_sort_order_idx`(`parent_id`, `sort_order`),
    INDEX `kb_categories_status_idx`(`status`),
    UNIQUE INDEX `kb_categories_name_parent_id_key`(`name`, `parent_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `kb_versions` (
    `id` VARCHAR(191) NOT NULL,
    `knowledge_id` VARCHAR(191) NOT NULL,
    `version_number` INTEGER NOT NULL,
    `title` VARCHAR(500) NOT NULL,
    `content` LONGTEXT NOT NULL,
    `change_log` TEXT NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `kb_versions_knowledge_id_created_at_idx`(`knowledge_id`, `created_at` DESC),
    UNIQUE INDEX `kb_versions_knowledge_id_version_number_key`(`knowledge_id`, `version_number`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `kb_tags` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `description` TEXT NULL,
    `color` VARCHAR(20) NULL,
    `use_count` INTEGER NOT NULL DEFAULT 0,
    `created_by` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `kb_tags_name_key`(`name`),
    INDEX `kb_tags_use_count_idx`(`use_count` DESC),
    INDEX `kb_tags_name_idx`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `knowledge_base_tags` (
    `id` VARCHAR(191) NOT NULL,
    `knowledge_id` VARCHAR(191) NOT NULL,
    `tag_id` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `knowledge_base_tags_knowledge_id_idx`(`knowledge_id`),
    INDEX `knowledge_base_tags_tag_id_idx`(`tag_id`),
    UNIQUE INDEX `knowledge_base_tags_knowledge_id_tag_id_key`(`knowledge_id`, `tag_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_feedback` (
    `id` VARCHAR(191) NOT NULL,
    `conversation_id` VARCHAR(191) NULL,
    `message_id` VARCHAR(191) NULL,
    `user_id` VARCHAR(191) NULL,
    `feedback_type` ENUM('like', 'dislike', 'report', 'suggestion') NOT NULL,
    `rating` INTEGER NULL,
    `content` TEXT NULL,
    `status` ENUM('pending', 'processed', 'closed') NOT NULL DEFAULT 'pending',
    `processed_by` VARCHAR(191) NULL,
    `processed_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `user_feedback_conversation_id_idx`(`conversation_id`),
    INDEX `user_feedback_user_id_created_at_idx`(`user_id`, `created_at` DESC),
    INDEX `user_feedback_feedback_type_status_idx`(`feedback_type`, `status`),
    INDEX `user_feedback_rating_idx`(`rating`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `service_analytics` (
    `id` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `hour` TINYINT NULL,
    `total_conversations` INTEGER NOT NULL DEFAULT 0,
    `total_messages` INTEGER NOT NULL DEFAULT 0,
    `unique_users` INTEGER NOT NULL DEFAULT 0,
    `avg_response_time` DECIMAL(10, 2) NOT NULL DEFAULT 0,
    `avg_conversation_length` DECIMAL(10, 2) NOT NULL DEFAULT 0,
    `satisfaction_score` DECIMAL(3, 2) NULL,
    `intent_distribution` JSON NULL,
    `sentiment_distribution` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `service_analytics_date_idx`(`date`),
    INDEX `service_analytics_satisfaction_score_idx`(`satisfaction_score` DESC),
    UNIQUE INDEX `service_analytics_date_hour_key`(`date`, `hour`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_config` (
    `id` VARCHAR(191) NOT NULL,
    `config_key` VARCHAR(100) NOT NULL,
    `config_value` TEXT NULL,
    `config_type` ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
    `description` TEXT NULL,
    `is_encrypted` BOOLEAN NOT NULL DEFAULT false,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ai_config_config_key_key`(`config_key`),
    INDEX `ai_config_config_key_idx`(`config_key`),
    INDEX `ai_config_is_active_idx`(`is_active`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user_departments` ADD CONSTRAINT `user_departments_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_departments` ADD CONSTRAINT `user_departments_deptId_fkey` FOREIGN KEY (`deptId`) REFERENCES `departments`(`deptId`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `roles`(`roleId`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_sessions` ADD CONSTRAINT `user_sessions_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_conversations` ADD CONSTRAINT `ai_conversations_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_messages` ADD CONSTRAINT `ai_messages_conversation_id_fkey` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base` ADD CONSTRAINT `knowledge_base_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `kb_categories`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base` ADD CONSTRAINT `knowledge_base_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `kb_categories` ADD CONSTRAINT `kb_categories_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `kb_categories`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `kb_versions` ADD CONSTRAINT `kb_versions_knowledge_id_fkey` FOREIGN KEY (`knowledge_id`) REFERENCES `knowledge_base`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `kb_versions` ADD CONSTRAINT `kb_versions_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `kb_tags` ADD CONSTRAINT `kb_tags_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base_tags` ADD CONSTRAINT `knowledge_base_tags_knowledge_id_fkey` FOREIGN KEY (`knowledge_id`) REFERENCES `knowledge_base`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `knowledge_base_tags` ADD CONSTRAINT `knowledge_base_tags_tag_id_fkey` FOREIGN KEY (`tag_id`) REFERENCES `kb_tags`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_feedback` ADD CONSTRAINT `user_feedback_conversation_id_fkey` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversations`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_feedback` ADD CONSTRAINT `user_feedback_message_id_fkey` FOREIGN KEY (`message_id`) REFERENCES `ai_messages`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_feedback` ADD CONSTRAINT `user_feedback_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_feedback` ADD CONSTRAINT `user_feedback_processed_by_fkey` FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
