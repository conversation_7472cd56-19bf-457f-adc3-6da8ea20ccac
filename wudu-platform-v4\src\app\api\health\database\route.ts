import { NextResponse } from 'next/server';
import { healthCheck, testDatabaseConnection } from '@/lib/db';

export async function GET() {
  try {
    // 测试数据库连接
    const isConnected = await testDatabaseConnection();
    
    // 获取详细健康状态
    const healthStatus = await healthCheck();
    
    if (isConnected) {
      return NextResponse.json({
        status: 'success',
        message: '数据库连接正常',
        database: {
          connected: true,
          type: 'MySQL',
          host: 'localhost:3306',
          database: 'wudu_platform'
        },
        health: healthStatus,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        status: 'error',
        message: '数据库连接失败',
        database: {
          connected: false,
          type: 'MySQL',
          host: 'localhost:3306',
          database: 'wudu_platform'
        },
        health: healthStatus,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
  } catch (error) {
    console.error('❌ 数据库健康检查失败:', error);
    
    return NextResponse.json({
      status: 'error',
      message: '数据库健康检查失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
